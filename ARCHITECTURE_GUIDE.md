# Camera Module Architecture Guide

## 📋 Overview
This document provides a comprehensive guide to the Camera Module architecture for AI development tools and future maintainers. The module follows **MVVM + Repository + Clean Architecture** patterns.

## 🏗️ Architecture Layers

```
┌─────────────────────────────────────────────────────────────┐
│                        UI Layer                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  CameraActivity │    │  UI Components  │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     Presentation Layer                     │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  CameraViewModel│    │  CameraUiState  │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      Domain Layer                          │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   Use Cases     │    │  Domain Models  │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                       Data Layer                           │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ICameraRepository│    │ CameraRepository│                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

## 📁 File Structure & Responsibilities

### 🎯 UI Layer (`ui/camera/`)
```
ui/camera/
├── CameraActivity.kt              # Main camera screen
├── CameraViewModel.kt             # Presentation logic
├── CameraViewModelFactory.kt      # ViewModel factory
├── DocumentBoundaryOverlay.kt     # Custom overlay views
├── CropSelectionOverlay.kt        # Crop selection UI
└── ...other UI components
```

**Responsibilities:**
- Handle user interactions
- Display UI states
- Observe ViewModel data
- Navigate between screens

### 🧠 Presentation Layer (`ui/camera/CameraViewModel.kt`)
```kotlin
class CameraViewModel(
    private val cameraUseCases: CameraUseCases
) : ViewModel() {
    
    // UI State Management
    private val _uiState = MutableStateFlow(CameraUiState())
    val uiState: StateFlow<CameraUiState> = _uiState.asStateFlow()
    
    // Camera States (from Repository via Use Cases)
    val focusState: Flow<FocusState> = cameraUseCases.getFocusState()
    val captureState: Flow<CaptureState> = cameraUseCases.getCaptureState()
    val documentDetectionState: Flow<DocumentDetectionResult> = cameraUseCases.getDocumentDetectionState()
}
```

**Responsibilities:**
- Manage UI state
- Handle user actions
- Coordinate between Use Cases
- Transform domain data for UI

### 🎯 Domain Layer

#### Use Cases (`domain/camera/usecase/CameraUseCases.kt`)
```kotlin
// Single Responsibility Use Cases
class CaptureImageUseCase(private val repository: ICameraRepository)
class PerformOCRUseCase(private val repository: ICameraRepository)
class SolveWithAIUseCase(private val repository: ICameraRepository)
class ProcessDocumentUseCase(private val repository: ICameraRepository)
// ... 14 total use cases
```

**Responsibilities:**
- Encapsulate business logic
- Single responsibility principle
- Testable units
- Repository abstraction

#### Domain Models (`domain/camera/model/CameraModels.kt`)
```kotlin
// State Enums
enum class FocusState { FOCUSED, TOO_CLOSE, TOO_FAR, UNFOCUSED, CAPTURING }
enum class CaptureState { IDLE, CAPTURING, SUCCESS, ERROR }

// Result Classes
sealed class CaptureResult {
    data class Success(val imagePath: String) : CaptureResult()
    data class Error(val message: String, val exception: Throwable? = null) : CaptureResult()
}

// Data Classes
data class OCRResult(val text: String, val confidence: Float, val isSuccess: Boolean)
data class AIResult(val solution: String, val isSuccess: Boolean)
```

### 💾 Data Layer

#### Repository Interface (`data/camera/ICameraRepository.kt`)
```kotlin
interface ICameraRepository {
    // State Management
    fun getFocusState(): Flow<FocusState>
    fun getCaptureState(): Flow<CaptureState>
    fun getDocumentDetectionState(): Flow<DocumentDetectionResult>
    
    // Camera Operations
    suspend fun captureImage(imageCapture: ImageCapture, outputFileOptions: ImageCapture.OutputFileOptions): CaptureResult
    suspend fun analyzeImage(imageProxy: ImageProxy): ImageAnalysisResult
    
    // Document Processing
    suspend fun detectDocumentBoundaries(imagePath: String): List<PointF>?
    suspend fun correctPerspective(imagePath: String, boundaries: List<PointF>): String?
    suspend fun processDocument(imagePath: String, manualBoundaries: List<PointF>? = null): DocumentProcessResult
    
    // AI & OCR
    suspend fun performOCR(imagePath: String): OCRResult
    suspend fun solveWithAI(imagePath: String, questionText: String? = null): AIResult
}
```

#### Repository Implementation (`data/camera/CameraRepository.kt`)
```kotlin
class CameraRepository(private val context: Context) : ICameraRepository {
    // Concrete implementation of all interface methods
    // Handles actual data operations, file I/O, camera APIs
}
```

## 🔧 Dependency Injection (`di/CameraModule.kt`)

```kotlin
object CameraModule {
    fun provideCameraRepository(context: Context): ICameraRepository {
        return CameraRepository(context)
    }
    
    fun provideCameraUseCases(repository: ICameraRepository): CameraUseCases {
        return CameraUseCases(
            getFocusState = GetFocusStateUseCase(repository),
            getCaptureState = GetCaptureStateUseCase(repository),
            // ... all 14 use cases
        )
    }
}
```

## 🔄 Data Flow

### 1. User Action Flow
```
User Tap → Activity → ViewModel → Use Case → Repository → External API/Storage
                                     ↓
UI Update ← Activity ← ViewModel ← Use Case ← Repository ← Response/Data
```

### 2. State Management Flow
```
Repository StateFlow → Use Case → ViewModel → UI State → Activity → UI Update
```

## 🧪 Testing Strategy

### Unit Tests Structure
```
test/
├── domain/
│   └── usecase/
│       ├── CaptureImageUseCaseTest.kt
│       ├── PerformOCRUseCaseTest.kt
│       └── SolveWithAIUseCaseTest.kt
├── presentation/
│   └── CameraViewModelTest.kt
└── data/
    └── CameraRepositoryTest.kt
```

### Mock Strategy
```kotlin
// Example test setup
class CameraViewModelTest {
    @Mock private lateinit var mockRepository: ICameraRepository
    @Mock private lateinit var mockUseCases: CameraUseCases
    
    private lateinit var viewModel: CameraViewModel
    
    @Before
    fun setup() {
        viewModel = CameraViewModel(mockUseCases)
    }
}
```

## 🔍 Key Patterns Used

### 1. **MVVM Pattern**
- **Model**: Domain models and Repository
- **View**: Activity and UI components
- **ViewModel**: CameraViewModel with UI state management

### 2. **Repository Pattern**
- Abstract data access through ICameraRepository
- Concrete implementation in CameraRepository
- Single source of truth for camera data

### 3. **Use Case Pattern (Clean Architecture)**
- Single responsibility business logic
- Testable and reusable components
- Clear separation of concerns

### 4. **Observer Pattern**
- StateFlow/Flow for reactive programming
- UI automatically updates when data changes

### 5. **Factory Pattern**
- CameraViewModelFactory for ViewModel creation
- CameraModule for dependency provision

## 🚀 Adding New Features

### To add a new camera feature:

1. **Add Domain Model** (if needed)
```kotlin
// In CameraModels.kt
data class NewFeatureResult(val data: String, val isSuccess: Boolean)
```

2. **Add Repository Method**
```kotlin
// In ICameraRepository.kt
suspend fun performNewFeature(input: String): NewFeatureResult

// In CameraRepository.kt
override suspend fun performNewFeature(input: String): NewFeatureResult {
    // Implementation
}
```

3. **Create Use Case**
```kotlin
// In CameraUseCases.kt
class PerformNewFeatureUseCase(private val repository: ICameraRepository) {
    suspend operator fun invoke(input: String): NewFeatureResult = 
        repository.performNewFeature(input)
}
```

4. **Update ViewModel**
```kotlin
// In CameraViewModel.kt
fun performNewFeature(input: String) {
    viewModelScope.launch {
        _uiState.value = _uiState.value.copy(isLoading = true)
        try {
            val result = cameraUseCases.performNewFeature(input)
            // Handle result
        } catch (e: Exception) {
            // Handle error
        }
    }
}
```

5. **Update UI**
```kotlin
// In CameraActivity.kt
// Observe new state and update UI accordingly
```

## 📚 AI Development Tool Guidelines

### For Code Analysis Tools:
- **Entry Point**: Start with `CameraActivity.kt` for UI flow
- **Business Logic**: Check `CameraUseCases.kt` for feature list
- **Data Operations**: Examine `ICameraRepository.kt` for available operations
- **State Management**: Look at `CameraViewModel.kt` for state handling

### For Code Generation Tools:
- Follow the established patterns in `CameraUseCases.kt`
- Use the same error handling patterns as existing use cases
- Maintain the same naming conventions
- Always update the dependency injection in `CameraModule.kt`

### For Refactoring Tools:
- Preserve the layer separation
- Maintain interface contracts
- Keep use cases single-responsibility
- Ensure proper dependency injection

## 🔧 Common Operations

### Adding a new Use Case:
1. Create use case class in `CameraUseCases.kt`
2. Add to `CameraUseCases` data class
3. Update `CameraModule.provideCameraUseCases()`
4. Use in `CameraViewModel`

### Adding a new UI State:
1. Update `CameraUiState` data class
2. Add handling in `CameraViewModel`
3. Observe in `CameraActivity`

### Adding a new Repository Method:
1. Add to `ICameraRepository` interface
2. Implement in `CameraRepository`
3. Create corresponding use case
4. Wire through dependency injection

## 📚 **卡組與卡片系統整合**

### **完整應用架構**
```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                       │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  CameraActivity │    │ DeckDetailActivity │             │
│  │  AiSolveActivity│    │ CardViewerActivity │             │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  Presentation Layer                        │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  CameraViewModel│    │DeckDetailViewModel│              │
│  │  CameraUiState  │    │  CardFilter     │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                           │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ Camera UseCases │    │   StudyCard     │                │
│  │ Camera Models   │    │   StudyDeck     │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      Data Layer                            │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ICameraRepository│    │ IDeckRepository │                │
│  │ CameraRepository│    │ DeckDataManager │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

### **跨模組數據流**
```
Camera Capture → OCR/AI Processing → Card Creation → Deck Storage
     ↓                ↓                  ↓             ↓
CameraRepository → AIResult → StudyCard → DeckDataManager
```

### **核心業務流程**
1. **拍照 → 學習卡片流程**
   ```
   用戶拍照 → 文檔檢測 → OCR識別 → AI求解 → 創建卡片 → 保存到卡組
   ```

2. **學習複習流程**
   ```
   選擇卡組 → 過濾卡片 → 開始學習 → 更新掌握度 → 計算下次複習時間
   ```

### **數據模型關聯**
```kotlin
// Camera 模組產生的數據
data class AIResult(
    val solution: String,
    val isSuccess: Boolean
) 

// 轉換為 Study 模組的卡片
data class StudyCard(
    val question: String,        // 來自 OCR
    val answer: String,          // 用戶輸入
    val aiAnswer: String,        // 來自 AIResult
    val questionImagePath: String?, // 來自 Camera
    val deckId: String          // 目標卡組
)
```

This comprehensive architecture ensures maintainability, testability, and clear separation of concerns across both Camera and Study modules while being easily understandable by AI development tools.