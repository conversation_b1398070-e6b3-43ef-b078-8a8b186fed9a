// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDeckSelectionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RadioButton rbSelect;

  @NonNull
  public final TextView tvDeckIcon;

  @NonNull
  public final TextView tvDeckName;

  private ItemDeckSelectionBinding(@NonNull LinearLayout rootView, @NonNull RadioButton rbSelect,
      @NonNull TextView tvDeckIcon, @NonNull TextView tvDeckName) {
    this.rootView = rootView;
    this.rbSelect = rbSelect;
    this.tvDeckIcon = tvDeckIcon;
    this.tvDeckName = tvDeckName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDeckSelectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDeckSelectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_deck_selection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDeckSelectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.rb_select;
      RadioButton rbSelect = ViewBindings.findChildViewById(rootView, id);
      if (rbSelect == null) {
        break missingId;
      }

      id = R.id.tv_deck_icon;
      TextView tvDeckIcon = ViewBindings.findChildViewById(rootView, id);
      if (tvDeckIcon == null) {
        break missingId;
      }

      id = R.id.tv_deck_name;
      TextView tvDeckName = ViewBindings.findChildViewById(rootView, id);
      if (tvDeckName == null) {
        break missingId;
      }

      return new ItemDeckSelectionBinding((LinearLayout) rootView, rbSelect, tvDeckIcon,
          tvDeckName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
