<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_ai_solve" modulePackage="com.erroranalysis.app" filePath="app_camera_refactor\src\main\res\layout\activity_ai_solve.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_ai_solve_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="152" endOffset="51"/></Target><Target id="@+id/image_container" view="FrameLayout"><Expressions/><location startLine="10" startOffset="4" endLine="49" endOffset="17"/></Target><Target id="@+id/iv_photo" view="ImageView"><Expressions/><location startLine="21" startOffset="8" endLine="26" endOffset="59"/></Target><Target id="@+id/crop_overlay" view="com.erroranalysis.app.ui.camera.CropSelectionOverlay"><Expressions/><location startLine="29" startOffset="8" endLine="33" endOffset="42"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="36" startOffset="8" endLine="45" endOffset="46"/></Target><Target id="@+id/ai_answer_container" view="ScrollView"><Expressions/><location startLine="52" startOffset="4" endLine="150" endOffset="16"/></Target><Target id="@+id/tv_ai_answer" view="TextView"><Expressions/><location startLine="96" startOffset="12" endLine="105" endOffset="39"/></Target><Target id="@+id/loading_container" view="LinearLayout"><Expressions/><location startLine="108" startOffset="12" endLine="129" endOffset="26"/></Target><Target id="@+id/btn_save_to_card" view="Button"><Expressions/><location startLine="132" startOffset="12" endLine="146" endOffset="43"/></Target></Targets></Layout>