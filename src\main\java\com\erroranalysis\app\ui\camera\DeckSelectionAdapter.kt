package com.erroranalysis.app.ui.camera

import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.erroranalysis.app.databinding.ItemDeckSelectionBinding
import com.erroranalysis.app.ui.study.SimpleDeck

/**
 * 卡組選擇適配器（支援搜尋過濾）
 */
class DeckSelectionAdapter(
    private val allDecks: List<SimpleDeck>,
    private val onDeckSelected: (SimpleDeck) -> Unit
) : RecyclerView.Adapter<DeckSelectionAdapter.DeckViewHolder>() {

    private var filteredDecks: List<SimpleDeck> = allDecks
    private var selectedPosition = -1

    inner class DeckViewHolder(private val binding: ItemDeckSelectionBinding) : 
        RecyclerView.ViewHolder(binding.root) {

        fun bind(deck: SimpleDeck, position: Int) {
            binding.apply {
                // 設置卡組信息（只顯示名稱）
                tvDeckName.text = deck.name
                tvDeckIcon.text = deck.icon

                // 設置圖標背景顏色
                try {
                    tvDeckIcon.setBackgroundColor(Color.parseColor(deck.color))
                } catch (e: Exception) {
                    tvDeckIcon.setBackgroundColor(Color.parseColor("#4A90E2"))
                }

                // 設置選擇狀態
                rbSelect.isChecked = position == selectedPosition

                // 點擊事件
                root.setOnClickListener {
                    val previousPosition = selectedPosition
                    selectedPosition = position

                    // 更新UI
                    notifyItemChanged(previousPosition)
                    notifyItemChanged(selectedPosition)

                    // 回調
                    onDeckSelected(deck)
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeckViewHolder {
        val binding = ItemDeckSelectionBinding.inflate(
            LayoutInflater.from(parent.context), 
            parent, 
            false
        )
        return DeckViewHolder(binding)
    }

    override fun onBindViewHolder(holder: DeckViewHolder, position: Int) {
        holder.bind(filteredDecks[position], position)
    }

    override fun getItemCount(): Int = filteredDecks.size

    /**
     * 獲取選中的卡組
     */
    fun getSelectedDeck(): SimpleDeck? {
        return if (selectedPosition >= 0 && selectedPosition < filteredDecks.size) {
            filteredDecks[selectedPosition]
        } else {
            null
        }
    }

    /**
     * 清除選擇
     */
    fun clearSelection() {
        val previousPosition = selectedPosition
        selectedPosition = -1
        if (previousPosition >= 0) {
            notifyItemChanged(previousPosition)
        }
    }

    /**
     * 搜尋過濾卡組
     */
    fun filter(query: String) {
        filteredDecks = if (query.isEmpty()) {
            allDecks
        } else {
            allDecks.filter { deck ->
                deck.name.contains(query, ignoreCase = true) ||
                deck.description.contains(query, ignoreCase = true)
            }
        }

        // 重置選擇狀態
        selectedPosition = -1

        // 通知數據變更
        notifyDataSetChanged()
    }
}
