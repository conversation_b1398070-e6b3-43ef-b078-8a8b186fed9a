<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:maxHeight="350dp"
    android:orientation="vertical"
    android:padding="20dp">

    <!-- 標題 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="選擇卡組"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/text_white"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- 搜尋卡組輸入框 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:hint="搜尋卡組"
        app:startIconDrawable="@drawable/ic_search"
        app:startIconTint="@color/text_secondary"
        app:hintTextColor="@color/text_secondary"
        app:boxStrokeColor="@color/primary_blue"
        app:boxBackgroundMode="outline">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_search_deck"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:inputType="text"
            android:textColor="@color/text_white"
            android:textColorHint="@color/text_secondary" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 卡組列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_decks"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:maxHeight="150dp"
        android:layout_marginBottom="12dp"
        android:scrollbars="vertical" />

    <!-- 建立新卡組按鈕 -->
    <Button
        android:id="@+id/btn_create_new_deck"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginBottom="12dp"
        android:background="@drawable/button_outline"
        android:text="建立新卡組"
        android:textColor="@color/primary_blue"
        android:textSize="16sp"
        android:drawableStart="@drawable/ic_add"
        android:drawablePadding="8dp"
        android:drawableTint="@color/primary_blue"
        android:gravity="center" />

    <!-- 按鈕區域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginEnd="12dp"
            android:background="@android:color/transparent"
            android:text="取消"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:minWidth="80dp" />

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:background="@drawable/button_primary"
            android:text="確認"
            android:textColor="@color/text_white"
            android:textSize="14sp"
            android:minWidth="80dp"
            android:enabled="false" />

    </LinearLayout>

</LinearLayout>
