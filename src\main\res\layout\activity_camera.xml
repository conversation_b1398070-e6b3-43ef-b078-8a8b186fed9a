<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/camera_background"
    tools:context=".ui.camera.CameraActivity">

    <!-- 滿版相機預覽 -->
    <androidx.camera.view.PreviewView
        android:id="@+id/viewFinder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/camera_background"
        app:scaleType="fillCenter"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 相機覆蓋層 - 包含所有UI元素 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 對焦指示器 -->
        <TextView
            android:id="@+id/tv_focus_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/focus_indicator_capturing"
            android:padding="16dp"
            android:text="@string/camera_hint_capturing"
            android:textColor="@color/text_white"
            android:textSize="16sp"
            android:textStyle="bold"
            android:visibility="gone"
            tools:visibility="visible" />

        <!-- 返回按鈕 -->
        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="top|start"
            android:layout_margin="16dp"
            android:background="@drawable/circle_button_background"
            android:contentDescription="返回"
            android:src="@drawable/ic_arrow_back"
            android:tint="@color/text_white" />

        <!-- 底部相機控制區域 - 覆蓋在相機畫面上 -->
        <LinearLayout
            android:id="@+id/layout_camera_controls"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_gravity="bottom"
            android:background="@drawable/gradient_overlay_bottom"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="32dp"
            android:paddingVertical="20dp">

            <!-- 圖庫按鈕 -->
            <ImageButton
                android:id="@+id/btn_gallery"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:background="@drawable/circle_button_background"
                android:contentDescription="圖庫"
                android:src="@drawable/ic_photo_library"
                android:tint="@color/text_white" />

            <!-- 拍照按鈕 -->
            <FrameLayout
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginHorizontal="48dp">

                <View
                    android:id="@+id/btn_capture"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/capture_button_background"
                    android:clickable="true"
                    android:focusable="true" />

                <!-- AI圖標覆蓋層 -->
                <ImageView
                    android:id="@+id/iv_ai_indicator"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_ai_white"
                    android:visibility="gone"
                    android:contentDescription="AI求解模式" />

            </FrameLayout>

            <!-- AI求解按鈕 -->
            <ImageButton
                android:id="@+id/btn_ai_solve"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:background="@drawable/circle_button_background"
                android:contentDescription="AI求解"
                android:src="@drawable/ic_ai"
                android:tint="@color/text_white" />

        </LinearLayout>

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
