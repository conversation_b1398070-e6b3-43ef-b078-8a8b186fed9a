// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemIconSelectorBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialCardView cardIcon;

  @NonNull
  public final TextView textIcon;

  @NonNull
  public final TextView textIconName;

  private ItemIconSelectorBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialCardView cardIcon, @NonNull TextView textIcon,
      @NonNull TextView textIconName) {
    this.rootView = rootView;
    this.cardIcon = cardIcon;
    this.textIcon = textIcon;
    this.textIconName = textIconName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemIconSelectorBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemIconSelectorBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_icon_selector, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemIconSelectorBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.card_icon;
      MaterialCardView cardIcon = ViewBindings.findChildViewById(rootView, id);
      if (cardIcon == null) {
        break missingId;
      }

      id = R.id.text_icon;
      TextView textIcon = ViewBindings.findChildViewById(rootView, id);
      if (textIcon == null) {
        break missingId;
      }

      id = R.id.text_icon_name;
      TextView textIconName = ViewBindings.findChildViewById(rootView, id);
      if (textIconName == null) {
        break missingId;
      }

      return new ItemIconSelectorBinding((LinearLayout) rootView, cardIcon, textIcon, textIconName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
