<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingHorizontal="12dp"
    android:paddingVertical="16dp"
    android:gravity="center_vertical"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <!-- 選擇指示器 -->
    <RadioButton
        android:id="@+id/rb_select"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:layout_gravity="center_vertical"
        android:clickable="false"
        android:focusable="false" />

    <!-- 卡組圖標 -->
    <TextView
        android:id="@+id/tv_deck_icon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="12dp"
        android:background="@drawable/circle_background"
        android:gravity="center"
        android:text="📚"
        android:textSize="20sp" />

    <!-- 卡組名稱 -->
    <TextView
        android:id="@+id/tv_deck_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="卡組名稱"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/text_white"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center_vertical" />

</LinearLayout>
