package com.erroranalysis.app.ui.camera

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.erroranalysis.app.domain.camera.usecase.CameraUseCases

/**
 * CameraViewModel 的 Factory
 */
class CameraViewModelFactory(
    private val cameraUseCases: CameraUseCases
) : ViewModelProvider.Factory {
    
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(CameraViewModel::class.java)) {
            return CameraViewModel(cameraUseCases) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}