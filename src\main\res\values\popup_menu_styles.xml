<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 自定義PopupMenu樣式 - 白底藍字 -->
    <style name="CustomPopupMenuStyle" parent="Widget.AppCompat.PopupMenu">
        <item name="android:popupBackground">@android:color/white</item>
        <item name="android:textColor">@color/primary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColorPrimary">@color/primary</item>
        <item name="android:textColorSecondary">@color/primary</item>
    </style>

    <!-- PopupMenu項目樣式 -->
    <style name="CustomPopupMenuItemStyle" parent="@style/Widget.AppCompat.TextView">
        <item name="android:textColor">@color/primary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:padding">12dp</item>
        <item name="android:background">?android:attr/selectableItemBackground</item>
    </style>
</resources>
