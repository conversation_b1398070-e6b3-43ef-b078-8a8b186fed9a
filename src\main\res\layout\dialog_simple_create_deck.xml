<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="卡組名稱">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edit_deck_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1"
            android:textColor="@color/black"
            android:textColorHint="@color/text_tertiary" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="科目">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edit_subject"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1"
            android:textColor="@color/black"
            android:textColorHint="@color/text_tertiary" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 圖標選擇區域 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="選擇圖標"
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_icons"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_marginBottom="16dp" />

</LinearLayout>
