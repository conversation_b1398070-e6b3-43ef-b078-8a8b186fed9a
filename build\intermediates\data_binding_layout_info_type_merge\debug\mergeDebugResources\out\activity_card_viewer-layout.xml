<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_card_viewer" modulePackage="com.erroranalysis.app" filePath="app_camera_refactor\src\main\res\layout\activity_card_viewer.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_card_viewer_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="154" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="15" startOffset="8" endLine="22" endOffset="55"/></Target><Target id="@+id/tv_content_type" view="TextView"><Expressions/><location startLine="35" startOffset="8" endLine="43" endOffset="53"/></Target><Target id="@+id/btn_tts" view="ImageButton"><Expressions/><location startLine="46" startOffset="8" endLine="55" endOffset="44"/></Target><Target id="@+id/content_frame" view="FrameLayout"><Expressions/><location startLine="62" startOffset="4" endLine="114" endOffset="17"/></Target><Target id="@+id/scroll_view" view="ScrollView"><Expressions/><location startLine="74" startOffset="8" endLine="104" endOffset="20"/></Target><Target id="@+id/tv_content" view="TextView"><Expressions/><location startLine="90" startOffset="16" endLine="100" endOffset="63"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="107" startOffset="8" endLine="112" endOffset="39"/></Target><Target id="@+id/btn_edit" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="125" startOffset="8" endLine="135" endOffset="51"/></Target><Target id="@+id/btn_toggle" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="137" startOffset="8" endLine="150" endOffset="51"/></Target></Targets></Layout>