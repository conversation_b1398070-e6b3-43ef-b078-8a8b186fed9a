/ Header Record For PersistentHashMapValueStorageT Sapp_camera_refactor/src/main/java/com/erroranalysis/app/ErrorAnalysisApplication.ktP Oapp_camera_refactor/src/main/java/com/erroranalysis/app/data/DeckDataManager.ktP Oapp_camera_refactor/src/main/java/com/erroranalysis/app/data/IDeckRepository.ktX Wapp_camera_refactor/src/main/java/com/erroranalysis/app/data/camera/CameraRepository.ktY Xapp_camera_refactor/src/main/java/com/erroranalysis/app/data/camera/ICameraRepository.ktK Japp_camera_refactor/src/main/java/com/erroranalysis/app/di/CameraModule.ktM Lapp_camera_refactor/src/main/java/com/erroranalysis/app/di/ServiceLocator.kt\ [app_camera_refactor/src/main/java/com/erroranalysis/app/domain/camera/model/CameraModels.kt` _app_camera_refactor/src/main/java/com/erroranalysis/app/domain/camera/usecase/CameraUseCases.ktR Qapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/base/ThemedActivity.ktU Tapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/AiSolveActivity.ktT Sapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CameraActivity.ktU Tapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CameraViewModel.kt\ [app_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CameraViewModelFactory.kt[ Zapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CombinedImageAnalyzer.kt] \app_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CropOverlayTestActivity.ktZ Yapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CropSelectionOverlay.ktZ Yapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/DeckSelectionAdapter.kt] \app_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/DocumentBoundaryOverlay.kt_ ^app_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/DocumentDetectionAnalyzer.ktS Rapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/FocusAnalyzer.ktW Vapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/PhotoEditActivity.ktZ Yapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/SimpleCameraActivity.ktV Uapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/main/SimpleMainActivity.kt] \app_camera_refactor/src/main/java/com/erroranalysis/app/ui/settings/BackupRestoreActivity.ktY Xapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/settings/FontOptionAdapter.ktX Wapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/settings/SettingsActivity.kt] \app_camera_refactor/src/main/java/com/erroranalysis/app/ui/settings/adapters/ThemeAdapter.ktX Wapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/BatchImportActivity.ktU Tapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/CardEditActivity.ktO Napp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/CardFilter.ktW Vapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/CardViewerActivity.ktW Vapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailActivity.ktX Wapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailViewModel.kt_ ^app_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailViewModelFactory.ktO Napp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckFilter.ktX Wapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/SimpleStudyActivity.ktN Mapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/StudyCard.ktN Mapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/StudyDeck.ktZ Yapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/adapters/ColorAdapter.ktY Xapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/adapters/IconAdapter.kt_ ^app_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/adapters/SimpleDeckAdapter.kt^ ]app_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/adapters/StudyCardAdapter.ktb aapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/test/DocumentCorrectionTestActivity.ktM Lapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/AppTheme.ktS Rapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/FontExtensions.ktP Oapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/FontManager.ktX Wapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/FontSelectorAdapter.ktW Vapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/FontSelectorDialog.ktQ Papp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/ThemeManager.ktW Vapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/widgets/RichTextEditText.ktT Sapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/BatchImportManager.ktZ Yapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/DocumentBoundaryDetector.ktS Rapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/DocumentProcessor.ktQ Papp_camera_refactor/src/main/java/com/erroranalysis/app/utils/GeminiAIService.kt[ Zapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/GridSpacingItemDecoration.ktU Tapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/ImageStorageManager.ktR Qapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/MarkdownRenderer.ktR Qapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/MathFormatHelper.ktK Japp_camera_refactor/src/main/java/com/erroranalysis/app/utils/OCRHelper.ktS Rapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/OpenCVInitializer.ktO Napp_camera_refactor/src/main/java/com/erroranalysis/app/utils/OpenCVManager.ktM Lapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/PdfExporter.ktV Uapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/PerspectiveCorrector.ktU Tapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/SimpleBackupManager.ktW Vapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailActivity.ktX Wapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailViewModel.ktW Vapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailActivity.ktM Lapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/PdfExporter.ktM Lapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/PdfExporter.kt] \app_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CropOverlayTestActivity.ktV Uapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/main/SimpleMainActivity.ktW Vapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailActivity.ktX Wapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/SimpleStudyActivity.kt_ ^app_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/adapters/SimpleDeckAdapter.ktW Vapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailActivity.ktX Wapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailViewModel.ktW Vapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailActivity.ktW Vapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailActivity.kt