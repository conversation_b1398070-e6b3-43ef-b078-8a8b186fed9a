// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSimpleDeckBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView textCardCount;

  @NonNull
  public final TextView textCardCountNumber;

  @NonNull
  public final TextView textCoverIcon;

  @NonNull
  public final TextView textDeckName;

  @NonNull
  public final TextView textStarIcon;

  private ItemSimpleDeckBinding(@NonNull MaterialCardView rootView, @NonNull TextView textCardCount,
      @NonNull TextView textCardCountNumber, @NonNull TextView textCoverIcon,
      @NonNull TextView textDeckName, @NonNull TextView textStarIcon) {
    this.rootView = rootView;
    this.textCardCount = textCardCount;
    this.textCardCountNumber = textCardCountNumber;
    this.textCoverIcon = textCoverIcon;
    this.textDeckName = textDeckName;
    this.textStarIcon = textStarIcon;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSimpleDeckBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSimpleDeckBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_simple_deck, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSimpleDeckBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.text_card_count;
      TextView textCardCount = ViewBindings.findChildViewById(rootView, id);
      if (textCardCount == null) {
        break missingId;
      }

      id = R.id.text_card_count_number;
      TextView textCardCountNumber = ViewBindings.findChildViewById(rootView, id);
      if (textCardCountNumber == null) {
        break missingId;
      }

      id = R.id.text_cover_icon;
      TextView textCoverIcon = ViewBindings.findChildViewById(rootView, id);
      if (textCoverIcon == null) {
        break missingId;
      }

      id = R.id.text_deck_name;
      TextView textDeckName = ViewBindings.findChildViewById(rootView, id);
      if (textDeckName == null) {
        break missingId;
      }

      id = R.id.text_star_icon;
      TextView textStarIcon = ViewBindings.findChildViewById(rootView, id);
      if (textStarIcon == null) {
        break missingId;
      }

      return new ItemSimpleDeckBinding((MaterialCardView) rootView, textCardCount,
          textCardCountNumber, textCoverIcon, textDeckName, textStarIcon);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
