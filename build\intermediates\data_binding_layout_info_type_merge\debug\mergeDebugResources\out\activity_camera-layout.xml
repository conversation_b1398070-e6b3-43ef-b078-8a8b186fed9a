<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_camera" modulePackage="com.erroranalysis.app" filePath="app_camera_refactor\src\main\res\layout\activity_camera.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_camera_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="119" endOffset="51"/></Target><Target id="@+id/viewFinder" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="10" startOffset="4" endLine="19" endOffset="51"/></Target><Target id="@+id/tv_focus_indicator" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="43" endOffset="40"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="46" startOffset="8" endLine="55" endOffset="46"/></Target><Target id="@+id/layout_camera_controls" view="LinearLayout"><Expressions/><location startLine="58" startOffset="8" endLine="115" endOffset="22"/></Target><Target id="@+id/btn_gallery" view="ImageButton"><Expressions/><location startLine="70" startOffset="12" endLine="77" endOffset="50"/></Target><Target id="@+id/btn_capture" view="View"><Expressions/><location startLine="85" startOffset="16" endLine="91" endOffset="46"/></Target><Target id="@+id/iv_ai_indicator" view="ImageView"><Expressions/><location startLine="94" startOffset="16" endLine="101" endOffset="57"/></Target><Target id="@+id/btn_ai_solve" view="ImageButton"><Expressions/><location startLine="106" startOffset="12" endLine="113" endOffset="50"/></Target></Targets></Layout>