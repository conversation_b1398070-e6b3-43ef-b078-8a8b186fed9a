<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light">

    <!-- 工具列 - 橫屏時包含所有控制項 -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_blue"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:titleTextColor="@android:color/white">

            <!-- 自定義工具列內容 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingEnd="16dp">

                <!-- 標題區域 -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="卡片檢視"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/white"
                        android:layout_marginEnd="16dp" />

                    <TextView
                        android:id="@+id/tv_content_type"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="題目"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/white"
                        android:background="@drawable/bg_toolbar_chip"
                        android:paddingHorizontal="12dp"
                        android:paddingVertical="4dp" />

                </LinearLayout>

                <!-- 控制按鈕區域 -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <!-- TTS語音播放按鈕 -->
                    <ImageButton
                        android:id="@+id/btn_tts"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginEnd="8dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="語音播放"
                        android:src="@drawable/ic_volume_up"
                        android:padding="12dp"
                        app:tint="@android:color/white" />

                    <!-- 編輯按鈕 -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_edit"
                        android:layout_width="wrap_content"
                        android:layout_height="48dp"
                        android:layout_marginEnd="8dp"
                        android:text="編輯"
                        android:textSize="16sp"
                        android:textColor="@android:color/white"
                        android:minWidth="0dp"
                        android:paddingHorizontal="16dp"
                        android:gravity="center"
                        android:layout_gravity="center_vertical"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        app:strokeColor="@android:color/white" />

                    <!-- 切換按鈕 -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_toggle"
                        android:layout_width="wrap_content"
                        android:layout_height="48dp"
                        android:text="查看答案"
                        android:textSize="16sp"
                        android:textColor="@color/primary_blue"
                        android:backgroundTint="@android:color/white"
                        android:minWidth="0dp"
                        android:paddingHorizontal="16dp"
                        android:gravity="center"
                        android:layout_gravity="center_vertical"
                        android:maxLines="1"
                        android:ellipsize="end"
                        style="@style/Widget.Material3.Button" />

                </LinearLayout>

            </LinearLayout>

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- 主要內容區域 - 橫屏時佔滿剩餘空間 -->
    <FrameLayout
        android:id="@+id/content_frame"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_margin="8dp"
        android:background="@drawable/bg_card_content"
        android:elevation="2dp"
        android:clickable="true"
        android:focusable="true">

        <!-- 可滾動的內容容器 -->
        <ScrollView
            android:id="@+id/scroll_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="vertical"
            android:scrollbarStyle="insideInset"
            android:fadeScrollbars="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- 內容顯示區 -->
                <TextView
                    android:id="@+id/tv_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:textColor="@color/text_primary"
                    android:lineSpacingExtra="4dp"
                    android:textIsSelectable="true"
                    android:minHeight="100dp"
                    android:gravity="top"
                    tools:text="這裡顯示卡片內容，可以是題目或答案。橫屏時有更多空間顯示圖片。" />

            </LinearLayout>

        </ScrollView>

        <!-- 載入指示器 -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

    </FrameLayout>

</LinearLayout>
