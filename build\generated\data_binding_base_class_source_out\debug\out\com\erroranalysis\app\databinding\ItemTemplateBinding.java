// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTemplateBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialCardView cardTemplate;

  @NonNull
  public final TextView textTemplateIcon;

  @NonNull
  public final TextView textTemplateName;

  private ItemTemplateBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialCardView cardTemplate, @NonNull TextView textTemplateIcon,
      @NonNull TextView textTemplateName) {
    this.rootView = rootView;
    this.cardTemplate = cardTemplate;
    this.textTemplateIcon = textTemplateIcon;
    this.textTemplateName = textTemplateName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTemplateBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTemplateBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_template, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTemplateBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.card_template;
      MaterialCardView cardTemplate = ViewBindings.findChildViewById(rootView, id);
      if (cardTemplate == null) {
        break missingId;
      }

      id = R.id.text_template_icon;
      TextView textTemplateIcon = ViewBindings.findChildViewById(rootView, id);
      if (textTemplateIcon == null) {
        break missingId;
      }

      id = R.id.text_template_name;
      TextView textTemplateName = ViewBindings.findChildViewById(rootView, id);
      if (textTemplateName == null) {
        break missingId;
      }

      return new ItemTemplateBinding((LinearLayout) rootView, cardTemplate, textTemplateIcon,
          textTemplateName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
