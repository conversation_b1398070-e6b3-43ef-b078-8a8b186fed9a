<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_study_card" modulePackage="com.erroranalysis.app" filePath="app_camera_refactor\src\main\res\layout\item_study_card.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_study_card_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="182" endOffset="51"/></Target><Target id="@+id/indicator_status" view="View"><Expressions/><location startLine="17" startOffset="8" endLine="21" endOffset="49"/></Target><Target id="@+id/star_container" view="FrameLayout"><Expressions/><location startLine="30" startOffset="12" endLine="51" endOffset="25"/></Target><Target id="@+id/icon_star" view="ImageView"><Expressions/><location startLine="43" startOffset="16" endLine="49" endOffset="56"/></Target><Target id="@+id/text_question" view="TextView"><Expressions/><location startLine="61" startOffset="12" endLine="70" endOffset="42"/></Target><Target id="@+id/text_answer" view="TextView"><Expressions/><location startLine="83" startOffset="12" endLine="93" endOffset="43"/></Target><Target id="@+id/text_tags" view="TextView"><Expressions/><location startLine="96" startOffset="12" endLine="104" endOffset="43"/></Target><Target id="@+id/text_difficulty" view="TextView"><Expressions/><location startLine="114" startOffset="16" endLine="122" endOffset="45"/></Target><Target id="@+id/progress_mastery" view="ProgressBar"><Expressions/><location startLine="133" startOffset="20" endLine="140" endOffset="47"/></Target><Target id="@+id/text_mastery_level" view="TextView"><Expressions/><location startLine="142" startOffset="20" endLine="149" endOffset="49"/></Target><Target id="@+id/text_created_time" view="TextView"><Expressions/><location startLine="154" startOffset="16" endLine="161" endOffset="45"/></Target><Target id="@+id/text_stats" view="TextView"><Expressions/><location startLine="166" startOffset="12" endLine="174" endOffset="43"/></Target></Targets></Layout>