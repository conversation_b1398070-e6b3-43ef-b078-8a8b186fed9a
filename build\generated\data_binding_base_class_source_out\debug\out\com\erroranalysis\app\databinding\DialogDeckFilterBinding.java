// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogDeckFilterBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnApply;

  @NonNull
  public final MaterialButton btnClear;

  @NonNull
  public final Chip chipEmpty;

  @NonNull
  public final Chip chipFewCards;

  @NonNull
  public final ChipGroup chipGroupCardCount;

  @NonNull
  public final ChipGroup chipGroupStar;

  @NonNull
  public final Chip chipManyCards;

  @NonNull
  public final Chip chipNotStarred;

  @NonNull
  public final Chip chipStarred;

  @NonNull
  public final TextInputEditText editKeyword;

  private DialogDeckFilterBinding(@NonNull LinearLayout rootView, @NonNull MaterialButton btnApply,
      @NonNull MaterialButton btnClear, @NonNull Chip chipEmpty, @NonNull Chip chipFewCards,
      @NonNull ChipGroup chipGroupCardCount, @NonNull ChipGroup chipGroupStar,
      @NonNull Chip chipManyCards, @NonNull Chip chipNotStarred, @NonNull Chip chipStarred,
      @NonNull TextInputEditText editKeyword) {
    this.rootView = rootView;
    this.btnApply = btnApply;
    this.btnClear = btnClear;
    this.chipEmpty = chipEmpty;
    this.chipFewCards = chipFewCards;
    this.chipGroupCardCount = chipGroupCardCount;
    this.chipGroupStar = chipGroupStar;
    this.chipManyCards = chipManyCards;
    this.chipNotStarred = chipNotStarred;
    this.chipStarred = chipStarred;
    this.editKeyword = editKeyword;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogDeckFilterBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogDeckFilterBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_deck_filter, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogDeckFilterBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_apply;
      MaterialButton btnApply = ViewBindings.findChildViewById(rootView, id);
      if (btnApply == null) {
        break missingId;
      }

      id = R.id.btn_clear;
      MaterialButton btnClear = ViewBindings.findChildViewById(rootView, id);
      if (btnClear == null) {
        break missingId;
      }

      id = R.id.chip_empty;
      Chip chipEmpty = ViewBindings.findChildViewById(rootView, id);
      if (chipEmpty == null) {
        break missingId;
      }

      id = R.id.chip_few_cards;
      Chip chipFewCards = ViewBindings.findChildViewById(rootView, id);
      if (chipFewCards == null) {
        break missingId;
      }

      id = R.id.chip_group_card_count;
      ChipGroup chipGroupCardCount = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupCardCount == null) {
        break missingId;
      }

      id = R.id.chip_group_star;
      ChipGroup chipGroupStar = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupStar == null) {
        break missingId;
      }

      id = R.id.chip_many_cards;
      Chip chipManyCards = ViewBindings.findChildViewById(rootView, id);
      if (chipManyCards == null) {
        break missingId;
      }

      id = R.id.chip_not_starred;
      Chip chipNotStarred = ViewBindings.findChildViewById(rootView, id);
      if (chipNotStarred == null) {
        break missingId;
      }

      id = R.id.chip_starred;
      Chip chipStarred = ViewBindings.findChildViewById(rootView, id);
      if (chipStarred == null) {
        break missingId;
      }

      id = R.id.edit_keyword;
      TextInputEditText editKeyword = ViewBindings.findChildViewById(rootView, id);
      if (editKeyword == null) {
        break missingId;
      }

      return new DialogDeckFilterBinding((LinearLayout) rootView, btnApply, btnClear, chipEmpty,
          chipFewCards, chipGroupCardCount, chipGroupStar, chipManyCards, chipNotStarred,
          chipStarred, editKeyword);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
