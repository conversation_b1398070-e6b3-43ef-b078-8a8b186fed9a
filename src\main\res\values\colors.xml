<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 主色調 -->
    <color name="primary_blue">#667eea</color>
    <color name="primary_purple">#764ba2</color>
    <color name="primary_gradient_start">#667eea</color>
    <color name="primary_gradient_end">#764ba2</color>
    
    <!-- 狀態顏色 -->
    <color name="success_green">#27ae60</color>
    <color name="error_red">#e74c3c</color>
    <color name="warning_orange">#f39c12</color>
    <color name="warning_yellow">#f1c40f</color>
    <color name="info_blue">#3498db</color>
    
    <!-- 背景顏色 -->
    <color name="background_light">#f5f7fa</color>
    <color name="background_white">#ffffff</color>
    <color name="background_card">#ffffff</color>
    <color name="background_section">#f8f9fa</color>
    <color name="background_gray">#e5e7eb</color>
    
    <!-- 文字顏色 -->
    <color name="text_primary">#2c3e50</color>
    <color name="text_secondary">#7f8c8d</color>
    <color name="text_hint">#95a5a6</color>
    <color name="divider_color">#E0E0E0</color>
    <color name="text_white">#ffffff</color>
    
    <!-- 邊框顏色 -->
    <color name="border_light">#ecf0f1</color>
    <color name="border_medium">#bdc3c7</color>
    <color name="border_dark">#95a5a6</color>
    
    <!-- 選擇框顏色 -->
    <color name="selection_active">#e74c3c</color>
    <color name="selection_inactive">#667eea</color>
    <color name="selection_background_active">#33e74c3c</color>
    <color name="selection_background_inactive">#33667eea</color>
    
    <!-- 檢測區域顏色 -->
    <color name="detected_area_border">#95a5a6</color>
    <color name="detected_area_background">#1a95a5a6</color>
    
    <!-- 狀態背景顏色 -->
    <color name="success_background">#e8f5e8</color>
    <color name="error_background">#fff5f5</color>
    <color name="warning_background">#fff3cd</color>
    <color name="info_background">#e8f4fd</color>
    
    <!-- 狀態文字顏色 -->
    <color name="success_text">#155724</color>
    <color name="error_text">#721c24</color>
    <color name="warning_text">#856404</color>
    <color name="info_text">#0c5460</color>
    
    <!-- Material Design 系統顏色 -->
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>

    <!-- Study Deck Colors -->
    <color name="background_primary">#F8F9FA</color>
    <color name="background_secondary">#FFFFFF</color>
    <color name="text_tertiary">#9CA3AF</color>
    <color name="ripple_color">#1F000000</color>
    <color name="white_80">#CCFFFFFF</color>
    <color name="primary">#667eea</color>
    <color name="stroke_light">#E5E7EB</color>

    <!-- Template Colors -->
    <color name="template_math">#4A90E2</color>
    <color name="template_physics">#F5A623</color>
    <color name="template_chemistry">#7ED321</color>
    <color name="template_biology">#50E3C2</color>
    <color name="template_english">#D0021B</color>
    <color name="template_classic">#8B572A</color>
    <color name="template_modern">#9013FE</color>
    <color name="template_minimal">#6B7280</color>
    
    <!-- 透明度變化 -->
    <color name="overlay_dark">#80000000</color>
    <color name="overlay_light">#40ffffff</color>
    
    <!-- 相機界面顏色 -->
    <color name="camera_background">#000000</color>
    <color name="camera_overlay_border">#ffffff</color>
    <color name="focus_indicator_capturing">#8027ae60</color>
    <color name="focus_indicator_too_close">#80e74c3c</color>
    <color name="focus_indicator_too_far">#80f1c40f</color>

    <!-- 文檔邊界檢測顏色 -->
    <color name="document_boundary">#ff0000</color>          <!-- 紅色邊框 -->
    <color name="document_corner">#ff0000</color>            <!-- 紅色角點 -->
    <color name="document_fill">#00000000</color>            <!-- 透明填充 -->
    <color name="document_boundary_preview">#ff0000</color>  <!-- 拍照預覽時的紅色邊框 -->
</resources>
