plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-parcelize'
}

android {
    namespace 'com.erroranalysis.app'
    compileSdk 34

    defaultConfig {
        applicationId "com.erroranalysis.app"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // NDK configuration for OpenCV - 只保留現代手機架構以最大化減小APK大小
        ndk {
            abiFilters 'arm64-v8a'  // 只保留64位ARM架構，適用於所有現代手機
        }
    }

    signingConfigs {
        debug {
            storeFile file("debug.keystore")
            storePassword "android"
            keyAlias "androiddebugkey"
            keyPassword "android"
        }
        release {
            storeFile file("../my-release-key.keystore") // 替換為您的 keystore 文件路徑
            storePassword System.getenv("STORE_PASSWORD") // 從環境變量獲取密碼
            keyAlias "my-key-alias" // 替換為您的密鑰別名
            keyPassword System.getenv("KEY_PASSWORD") // 從環境變量獲取密碼
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release // 引用 release 簽名配置
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            signingConfig signingConfigs.debug
            debuggable true
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    buildFeatures {
        viewBinding true
    }

    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
            excludes += 'META-INF/DEPENDENCIES'
            excludes += 'META-INF/LICENSE'
            excludes += 'META-INF/LICENSE.txt'
            excludes += 'META-INF/NOTICE'
            excludes += 'META-INF/NOTICE.txt'
        }
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'

    // Google Drive Backup
    implementation 'com.google.android.gms:play-services-auth:21.0.0'
    implementation 'com.google.api-client:google-api-client-android:2.2.0'
    implementation 'com.google.http-client:google-http-client-gson:1.43.3'
    implementation 'com.google.android.gms:play-services-base:18.2.0'

    // Google Drive API
    implementation 'com.google.apis:google-api-services-drive:v3-rev20220815-2.0.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    // ViewModel and LiveData
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'
    implementation 'androidx.fragment:fragment-ktx:1.6.2'

    // Camera
    implementation 'androidx.camera:camera-camera2:1.3.1'
    implementation 'androidx.camera:camera-lifecycle:1.3.1'
    implementation 'androidx.camera:camera-view:1.3.1'

    // OpenCV for perspective transformation (optimized for minimal APK size)
    implementation project(':opencv')

    // ML Kit Text Recognition for OCR
    implementation 'com.google.mlkit:text-recognition:16.0.0'
    implementation 'com.google.mlkit:text-recognition-chinese:16.0.0'

    // Google AI Gemini API
    implementation 'com.google.ai.client.generativeai:generativeai:0.2.2'

    // HTTP client for API calls
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'

    // Network
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'

    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'

    // Markdown rendering (simplified)
    implementation 'io.noties.markwon:core:4.6.2'
    implementation 'io.noties.markwon:html:4.6.2'

    // PDF generation
    implementation 'com.itextpdf:itext7-core:7.2.5'

    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}