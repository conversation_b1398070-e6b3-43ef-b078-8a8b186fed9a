// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemThemeBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialCardView cardTheme;

  @NonNull
  public final ImageView iconSelected;

  @NonNull
  public final TextView textThemeDescription;

  @NonNull
  public final TextView textThemeEmoji;

  @NonNull
  public final TextView textThemeName;

  @NonNull
  public final View viewThemePreview;

  private ItemThemeBinding(@NonNull MaterialCardView rootView, @NonNull MaterialCardView cardTheme,
      @NonNull ImageView iconSelected, @NonNull TextView textThemeDescription,
      @NonNull TextView textThemeEmoji, @NonNull TextView textThemeName,
      @NonNull View viewThemePreview) {
    this.rootView = rootView;
    this.cardTheme = cardTheme;
    this.iconSelected = iconSelected;
    this.textThemeDescription = textThemeDescription;
    this.textThemeEmoji = textThemeEmoji;
    this.textThemeName = textThemeName;
    this.viewThemePreview = viewThemePreview;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemThemeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemThemeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_theme, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemThemeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      MaterialCardView cardTheme = (MaterialCardView) rootView;

      id = R.id.icon_selected;
      ImageView iconSelected = ViewBindings.findChildViewById(rootView, id);
      if (iconSelected == null) {
        break missingId;
      }

      id = R.id.text_theme_description;
      TextView textThemeDescription = ViewBindings.findChildViewById(rootView, id);
      if (textThemeDescription == null) {
        break missingId;
      }

      id = R.id.text_theme_emoji;
      TextView textThemeEmoji = ViewBindings.findChildViewById(rootView, id);
      if (textThemeEmoji == null) {
        break missingId;
      }

      id = R.id.text_theme_name;
      TextView textThemeName = ViewBindings.findChildViewById(rootView, id);
      if (textThemeName == null) {
        break missingId;
      }

      id = R.id.view_theme_preview;
      View viewThemePreview = ViewBindings.findChildViewById(rootView, id);
      if (viewThemePreview == null) {
        break missingId;
      }

      return new ItemThemeBinding((MaterialCardView) rootView, cardTheme, iconSelected,
          textThemeDescription, textThemeEmoji, textThemeName, viewThemePreview);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
