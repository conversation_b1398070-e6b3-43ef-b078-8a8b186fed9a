<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_backup_restore" modulePackage="com.erroranalysis.app" filePath="app_camera_refactor\src\main\res\layout\activity_backup_restore.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_backup_restore_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="318" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="15" startOffset="8" endLine="21" endOffset="55"/></Target><Target id="@+id/layout_local_backup" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="84" startOffset="12" endLine="134" endOffset="63"/></Target><Target id="@+id/layout_local_restore" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="137" startOffset="12" endLine="187" endOffset="63"/></Target><Target id="@+id/layout_google_drive_backup" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="200" startOffset="12" endLine="250" endOffset="63"/></Target><Target id="@+id/layout_google_drive_restore" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="253" startOffset="12" endLine="303" endOffset="63"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="310" startOffset="4" endLine="316" endOffset="38"/></Target></Targets></Layout>