# 卡組與卡片架構設計指南

## 📋 Overview
本文檔詳細說明卡組（Deck）與卡片（Card）系統的設計邏輯、功能架構和業務流程，為 AI 開發工具提供完整的理解框架。

## 🏗️ 核心架構

### 📊 數據模型層次結構
```
StudyDeck (完整卡組)
    ├── SimpleDeck (簡化卡組信息)
    └── List<StudyCard> (卡片集合)
        ├── CardMastery (掌握程度)
        ├── CardDifficulty (難度等級)
        └── CardMetadata (元數據)
```

## 🎯 核心領域模型

### 1. **卡組模型 (Deck Models)**

#### **SimpleDeck - 基礎卡組信息**
```kotlin
data class SimpleDeck(
    val id: String,                    // 唯一標識符
    val name: String,                  // 卡組名稱
    val description: String,           // 卡組描述
    val icon: String,                  // 圖標標識
    val color: String,                 // 主題顏色
    val createdAt: Long,              // 創建時間
    val updatedAt: Long,              // 更新時間
    val cardCount: Int = 0,           // 卡片數量
    val isStarred: Boolean = false    // 是否收藏
)
```

#### **StudyDeck - 完整卡組數據**
```kotlin
data class StudyDeck(
    val id: String,
    val name: String,
    val description: String,
    val icon: String,
    val color: String,
    val createdAt: Long,
    val updatedAt: Long,
    val cards: MutableList<StudyCard> = mutableListOf(),  // 包含所有卡片
    val settings: DeckSettings = DeckSettings(),          // 學習設置
    val statistics: DeckStatistics = DeckStatistics()     // 統計數據
)
```

### 2. **卡片模型 (Card Models)**

#### **StudyCard - 學習卡片**
```kotlin
data class StudyCard(
    val id: String,                           // 唯一標識符
    val deckId: String,                       // 所屬卡組ID
    val question: String,                     // 問題內容
    val answer: String,                       // 答案內容
    val imagePath: String? = null,            // 圖片路徑
    val tags: List<String> = emptyList(),     // 標籤列表
    val difficulty: CardDifficulty = CardDifficulty.MEDIUM,  // 難度等級
    val mastery: CardMastery = CardMastery.NEW,              // 掌握程度
    val createdAt: Long,                      // 創建時間
    val updatedAt: Long,                      // 更新時間
    val lastReviewedAt: Long? = null,         // 最後複習時間
    val reviewCount: Int = 0,                 // 複習次數
    val correctCount: Int = 0,                // 正確次數
    val isStarred: Boolean = false,           // 是否收藏
    val notes: String = ""                    // 備註
)
```

#### **CardMastery - 掌握程度枚舉**
```kotlin
enum class CardMastery(val displayName: String, val color: String, val level: Int) {
    NEW("新卡片", "#9E9E9E", 0),           // 新卡片
    LEARNING("學習中", "#FF9800", 1),       // 學習階段
    REVIEW("複習中", "#2196F3", 2),         // 複習階段
    MASTERED("已掌握", "#4CAF50", 3),       // 已掌握
    DIFFICULT("困難", "#F44336", -1)        // 困難卡片
}
```

#### **CardDifficulty - 難度等級枚舉**
```kotlin
enum class CardDifficulty(val displayName: String, val color: String) {
    EASY("簡單", "#4CAF50"),      // 綠色
    MEDIUM("中等", "#FF9800"),    // 橙色
    HARD("困難", "#F44336")       // 紅色
}
```

## 🔄 業務邏輯與功能

### 📚 **卡組管理功能**

#### **1. 卡組生命週期**
```kotlin
// 創建卡組流程
fun createDeck(name: String, description: String): String {
    val deckId = generateNewDeckId()
    val deck = SimpleDeck(
        id = deckId,
        name = name,
        description = description,
        icon = "default",
        color = "#2196F3",
        createdAt = System.currentTimeMillis(),
        updatedAt = System.currentTimeMillis()
    )
    saveDeck(deck)
    return deckId
}

// 更新卡組
fun updateDeck(updatedDeck: SimpleDeck) {
    val decks = loadDecks().toMutableList()
    val index = decks.indexOfFirst { it.id == updatedDeck.id }
    if (index != -1) {
        decks[index] = updatedDeck.copy(updatedAt = System.currentTimeMillis())
        saveDecks(decks)
    }
}

// 刪除卡組（級聯刪除卡片）
fun deleteDeck(deckId: String) {
    // 1. 刪除卡組下的所有卡片
    val cards = loadCards().filter { it.deckId != deckId }
    saveCards(cards)
    
    // 2. 刪除卡組
    val decks = loadDecks().filter { it.id != deckId }
    saveDecks(decks)
}
```

#### **2. 特殊卡組 - 隨手記**
```kotlin
companion object {
    const val QUICK_NOTES_DECK_ID = "quick_notes_deck"
}

fun getQuickNotesDeck(): SimpleDeck? {
    return loadDecks().find { it.id == QUICK_NOTES_DECK_ID }
}

fun isQuickNotesDeck(deckId: String): Boolean {
    return deckId == QUICK_NOTES_DECK_ID
}
```

### 🃏 **卡片管理功能**

#### **1. 卡片CRUD操作**
```kotlin
// 添加卡片
fun addCard(card: StudyCard) {
    val cards = loadCards().toMutableList()
    cards.add(card)
    saveCards(cards)
    
    // 更新卡組的卡片數量
    updateDeckCardCount(card.deckId)
}

// 更新卡片
fun updateCard(updatedCard: StudyCard) {
    val cards = loadCards().toMutableList()
    val index = cards.indexOfFirst { it.id == updatedCard.id }
    if (index != -1) {
        cards[index] = updatedCard.copy(updatedAt = System.currentTimeMillis())
        saveCards(cards)
    }
}

// 移動卡片到其他卡組
fun moveCard(cardId: String, targetDeckId: String) {
    val cards = loadCards().toMutableList()
    val cardIndex = cards.indexOfFirst { it.id == cardId }
    if (cardIndex != -1) {
        val oldDeckId = cards[cardIndex].deckId
        cards[cardIndex] = cards[cardIndex].copy(
            deckId = targetDeckId,
            updatedAt = System.currentTimeMillis()
        )
        saveCards(cards)
        
        // 更新兩個卡組的卡片數量
        updateDeckCardCount(oldDeckId)
        updateDeckCardCount(targetDeckId)
    }
}
```

#### **2. 卡片學習狀態管理**
```kotlin
// 更新卡片掌握程度
fun updateCardMastery(cardId: String, isCorrect: Boolean) {
    val cards = loadCards().toMutableList()
    val cardIndex = cards.indexOfFirst { it.id == cardId }
    if (cardIndex != -1) {
        val card = cards[cardIndex]
        val newMastery = calculateNewMastery(card.mastery, isCorrect)
        
        cards[cardIndex] = card.copy(
            mastery = newMastery,
            reviewCount = card.reviewCount + 1,
            correctCount = if (isCorrect) card.correctCount + 1 else card.correctCount,
            lastReviewedAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
        saveCards(cards)
    }
}

// 掌握程度計算邏輯
private fun calculateNewMastery(currentMastery: CardMastery, isCorrect: Boolean): CardMastery {
    return when (currentMastery) {
        CardMastery.NEW -> if (isCorrect) CardMastery.LEARNING else CardMastery.DIFFICULT
        CardMastery.LEARNING -> if (isCorrect) CardMastery.REVIEW else CardMastery.DIFFICULT
        CardMastery.REVIEW -> if (isCorrect) CardMastery.MASTERED else CardMastery.LEARNING
        CardMastery.MASTERED -> if (!isCorrect) CardMastery.REVIEW else CardMastery.MASTERED
        CardMastery.DIFFICULT -> if (isCorrect) CardMastery.LEARNING else CardMastery.DIFFICULT
    }
}
```

## 🎨 **UI層架構 (MVVM)**

### **DeckDetailViewModel - 卡組詳情視圖模型**
```kotlin
class DeckDetailViewModel(
    application: Application, 
    private val deckId: String
) : AndroidViewModel(application) {
    
    private val repository: IDeckRepository = DeckDataManager(application)
    
    // UI 狀態
    private val _uiState = MutableLiveData<DeckDetailUiState>()
    val uiState: LiveData<DeckDetailUiState> = _uiState
    
    // 卡組數據
    private val _deck = MutableLiveData<SimpleDeck?>()
    val deck: LiveData<SimpleDeck?> = _deck
    
    // 卡片列表
    private val _cards = MutableLiveData<List<StudyCard>>()
    val cards: LiveData<List<StudyCard>> = _cards
    
    // 過濾和排序
    private val _filteredCards = MutableLiveData<List<StudyCard>>()
    val filteredCards: LiveData<List<StudyCard>> = _filteredCards
}
```

### **UI狀態管理**
```kotlin
data class DeckDetailUiState(
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val isEditMode: Boolean = false,
    val selectedCards: Set<String> = emptySet(),
    val currentFilter: CardFilter = CardFilter(),
    val currentSort: CardSortType = CardSortType.CREATED_DESC
)

// 卡片過濾器
data class CardFilter(
    val searchQuery: String = "",
    val difficulties: Set<CardDifficulty> = emptySet(),
    val masteries: Set<CardMastery> = emptySet(),
    val isStarredOnly: Boolean = false,
    val hasImageOnly: Boolean = false,
    val tags: Set<String> = emptySet()
)

// 卡片排序類型
enum class CardSortType(val displayName: String) {
    CREATED_ASC("創建時間 ↑"),
    CREATED_DESC("創建時間 ↓"),
    UPDATED_ASC("更新時間 ↑"),
    UPDATED_DESC("更新時間 ↓"),
    DIFFICULTY_ASC("難度 ↑"),
    DIFFICULTY_DESC("難度 ↓"),
    MASTERY_ASC("掌握程度 ↑"),
    MASTERY_DESC("掌握程度 ↓"),
    REVIEW_COUNT_ASC("複習次數 ↑"),
    REVIEW_COUNT_DESC("複習次數 ↓")
}
```

## 🔍 **搜索與過濾系統**

### **智能搜索功能**
```kotlin
fun searchCards(query: String, cards: List<StudyCard>): List<StudyCard> {
    if (query.isBlank()) return cards
    
    val lowercaseQuery = query.lowercase()
    return cards.filter { card ->
        card.question.lowercase().contains(lowercaseQuery) ||
        card.answer.lowercase().contains(lowercaseQuery) ||
        card.tags.any { it.lowercase().contains(lowercaseQuery) } ||
        card.notes.lowercase().contains(lowercaseQuery)
    }
}

// 多條件過濾
fun applyFilter(cards: List<StudyCard>, filter: CardFilter): List<StudyCard> {
    return cards.filter { card ->
        // 搜索查詢
        (filter.searchQuery.isBlank() || matchesSearchQuery(card, filter.searchQuery)) &&
        
        // 難度過濾
        (filter.difficulties.isEmpty() || card.difficulty in filter.difficulties) &&
        
        // 掌握程度過濾
        (filter.masteries.isEmpty() || card.mastery in filter.masteries) &&
        
        // 收藏過濾
        (!filter.isStarredOnly || card.isStarred) &&
        
        // 圖片過濾
        (!filter.hasImageOnly || !card.imagePath.isNullOrBlank()) &&
        
        // 標籤過濾
        (filter.tags.isEmpty() || filter.tags.any { it in card.tags })
    }
}
```

## 📊 **數據持久化策略**

### **SharedPreferences 存儲結構**
```kotlin
// 卡組數據結構
{
    "decks": [
        {
            "id": "deck_001",
            "name": "數學基礎",
            "description": "基礎數學概念",
            "icon": "math",
            "color": "#2196F3",
            "createdAt": 1640995200000,
            "updatedAt": 1640995200000,
            "cardCount": 25,
            "isStarred": true
        }
    ],
    "cards": [
        {
            "id": "card_001",
            "deckId": "deck_001",
            "question": "什麼是微積分？",
            "answer": "微積分是研究變化率的數學分支",
            "imagePath": "/storage/images/card_001.jpg",
            "tags": ["數學", "微積分"],
            "difficulty": "MEDIUM",
            "mastery": "LEARNING",
            "createdAt": 1640995200000,
            "updatedAt": 1640995200000,
            "lastReviewedAt": 1640995200000,
            "reviewCount": 5,
            "correctCount": 3,
            "isStarred": false,
            "notes": "重要概念，需要多練習"
        }
    ]
}
```

### **數據完整性檢查**
```kotlin
fun validateDataIntegrity(): String {
    val issues = mutableListOf<String>()
    val decks = loadDecks()
    val cards = loadCards()
    
    // 檢查孤立卡片
    val deckIds = decks.map { it.id }.toSet()
    val orphanCards = cards.filter { it.deckId !in deckIds }
    if (orphanCards.isNotEmpty()) {
        issues.add("發現 ${orphanCards.size} 張孤立卡片")
    }
    
    // 檢查卡片數量一致性
    decks.forEach { deck ->
        val actualCount = cards.count { it.deckId == deck.id }
        if (deck.cardCount != actualCount) {
            issues.add("卡組 ${deck.name} 的卡片數量不一致")
        }
    }
    
    return if (issues.isEmpty()) "數據完整性檢查通過" else issues.joinToString("\n")
}
```

## 🚀 **AI 開發工具指南**

### **常用操作模式**

#### **1. 添加新卡片功能**
```kotlin
// 模板：在 IDeckRepository 添加新方法
suspend fun [newCardOperation](params): [ReturnType]

// 模板：在 DeckDataManager 實現
override suspend fun [newCardOperation](params): [ReturnType] {
    val cards = loadCards().toMutableList()
    // 業務邏輯處理
    saveCards(cards)
    return result
}

// 模板：在 ViewModel 中使用
fun [newCardOperation](params) {
    viewModelScope.launch {
        try {
            val result = repository.[newCardOperation](params)
            // 更新 UI 狀態
        } catch (e: Exception) {
            // 錯誤處理
        }
    }
}
```

#### **2. 添加新的卡片屬性**
```kotlin
// 1. 更新 StudyCard 數據類
data class StudyCard(
    // ... 現有屬性
    val newProperty: [Type] = [defaultValue]  // 新屬性
)

// 2. 更新 JSON 序列化/反序列化
private fun cardToJson(card: StudyCard): JSONObject {
    return JSONObject().apply {
        // ... 現有屬性
        put("newProperty", card.newProperty)  // 新屬性
    }
}

// 3. 更新過濾器（如需要）
data class CardFilter(
    // ... 現有過濾條件
    val newPropertyFilter: [Type]? = null  // 新過濾條件
)
```

### **架構擴展點**

#### **1. 新增卡片類型**
- 擴展 `StudyCard` 添加 `cardType` 屬性
- 創建對應的 UI 組件
- 更新過濾和排序邏輯

#### **2. 學習算法優化**
- 修改 `calculateNewMastery` 方法
- 添加間隔重複算法
- 實現個性化學習路徑

#### **3. 數據同步功能**
- 實現雲端備份接口
- 添加數據衝突解決機制
- 支持多設備同步

### **測試策略**

#### **單元測試重點**
```kotlin
// 測試卡片狀態轉換
@Test
fun `updateCardMastery should correctly transition states`() {
    val card = createTestCard(mastery = CardMastery.NEW)
    val result = calculateNewMastery(card.mastery, isCorrect = true)
    assertEquals(CardMastery.LEARNING, result)
}

// 測試數據完整性
@Test
fun `validateDataIntegrity should detect orphan cards`() {
    // 設置測試數據
    val result = repository.validateDataIntegrity()
    assertTrue(result.contains("孤立卡片"))
}
```

## 📈 **性能優化建議**

### **1. 數據加載優化**
- 實現分頁加載大量卡片
- 使用 LazyColumn 進行虛擬化
- 緩存常用查詢結果

### **2. 搜索性能優化**
- 實現索引機制
- 使用 Debounce 減少搜索頻率
- 異步執行複雜過濾操作

### **3. 內存管理**
- 及時釋放大圖片資源
- 使用 WeakReference 避免內存洩漏
- 實現 LRU 緩存策略

這個架構設計確保了卡組和卡片系統的可擴展性、可維護性和高性能，為 AI 開發工具提供了清晰的開發指南。