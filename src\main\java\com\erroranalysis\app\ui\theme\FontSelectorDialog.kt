package com.erroranalysis.app.ui.theme

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.erroranalysis.app.R
import com.erroranalysis.app.databinding.DialogFontSelectorBinding

/**
 * 字體選擇對話框
 */
class FontSelectorDialog(
    context: Context,
    private val currentFont: FontManager.FontType,
    private val onFontSelected: (FontManager.FontType) -> Unit
) : Dialog(context) {

    private lateinit var binding: DialogFontSelectorBinding
    private lateinit var fontAdapter: FontSelectorAdapter
    private var selectedFont = currentFont

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = DialogFontSelectorBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupDialog()
        setupRecyclerView()
        setupButtons()
        updatePreview()
    }
    
    private fun setupDialog() {
        // 設置對話框屬性
        window?.setLayout(
            (context.resources.displayMetrics.widthPixels * 0.9).toInt(),
            android.view.ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }
    
    private fun setupRecyclerView() {
        val availableFonts = FontManager.getAvailableFonts(context)

        fontAdapter = FontSelectorAdapter(
            fonts = availableFonts,
            selectedFont = selectedFont
        ) { fontType ->
            selectedFont = fontType
            updatePreview()
        }

        binding.recyclerFonts.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = fontAdapter
        }
    }
    
    private fun setupButtons() {
        binding.btnCancel.setOnClickListener {
            dismiss()
        }
        
        binding.btnApply.setOnClickListener {
            onFontSelected(selectedFont)
            dismiss()
        }
    }
    
    private fun updatePreview() {
        val previewText = "字體預覽：${selectedFont.displayName}\n" +
                "這是一段示例文字，用於預覽字體效果。\n" +
                "The quick brown fox jumps over the lazy dog."

        binding.textFontPreview.text = previewText

        // 應用字體
        val typeface = FontManager.getTypeface(context, selectedFont)
        binding.textFontPreview.typeface = typeface
    }
}
