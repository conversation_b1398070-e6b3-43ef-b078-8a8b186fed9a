<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_card_filter" modulePackage="com.erroranalysis.app" filePath="app_camera_refactor\src\main\res\layout\dialog_card_filter.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_card_filter_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="193" endOffset="14"/></Target><Target id="@+id/edit_keyword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="39" startOffset="16" endLine="46" endOffset="67"/></Target><Target id="@+id/chip_group_mastery" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="60" startOffset="12" endLine="108" endOffset="56"/></Target><Target id="@+id/chip_level1" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="68" startOffset="16" endLine="74" endOffset="61"/></Target><Target id="@+id/chip_level2" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="76" startOffset="16" endLine="82" endOffset="61"/></Target><Target id="@+id/chip_level3" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="84" startOffset="16" endLine="90" endOffset="61"/></Target><Target id="@+id/chip_level4" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="92" startOffset="16" endLine="98" endOffset="61"/></Target><Target id="@+id/chip_level5" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="100" startOffset="16" endLine="106" endOffset="61"/></Target><Target id="@+id/chip_group_star" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="120" startOffset="12" endLine="144" endOffset="56"/></Target><Target id="@+id/chip_starred" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="128" startOffset="16" endLine="134" endOffset="61"/></Target><Target id="@+id/chip_not_starred" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="136" startOffset="16" endLine="142" endOffset="61"/></Target><Target id="@+id/chip_group_tags" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="156" startOffset="12" endLine="162" endOffset="45"/></Target><Target id="@+id/btn_clear" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="175" startOffset="8" endLine="182" endOffset="55"/></Target><Target id="@+id/btn_apply" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="184" startOffset="8" endLine="189" endOffset="58"/></Target></Targets></Layout>