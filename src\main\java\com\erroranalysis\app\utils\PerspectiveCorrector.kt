package com.erroranalysis.app.utils

import android.graphics.*
import android.util.Log
import org.opencv.android.Utils
import org.opencv.core.*
import org.opencv.imgproc.Imgproc
import kotlin.math.*

/**
 * 基於 OpenCV 的透視變換校正器
 * 使用 OpenCV 的高精度透視變換算法
 */
class PerspectiveCorrector {

    companion object {
        private const val TAG = "PerspectiveCorrector"

        // 默認輸出尺寸比例
        private const val DEFAULT_ASPECT_RATIO = 1.414f // A4紙比例 (√2)
        private const val DEFAULT_OUTPUT_WIDTH = 800
    }
    
    /**
     * 執行透視變換校正
     * @param bitmap 原始圖像
     * @param corners 四個角點（按順序：左上、右上、右下、左下）
     * @param outputWidth 輸出圖像寬度，如果為null則自動計算
     * @param outputHeight 輸出圖像高度，如果為null則自動計算
     * @return 校正後的圖像，如果失敗則返回null
     */
    fun correctPerspective(
        bitmap: Bitmap,
        corners: List<PointF>,
        outputWidth: Int? = null,
        outputHeight: Int? = null
    ): Bitmap? {
        if (corners.size != 4) {
            Log.e(TAG, "需要恰好 4 個角點")
            return null
        }

        if (!OpenCVManager.isInitialized()) {
            Log.w(TAG, "OpenCV 未初始化，使用簡化版透視校正")
            return fallbackPerspectiveCorrection(bitmap, corners, outputWidth, outputHeight)
        }

        try {
            Log.d(TAG, "開始 OpenCV 透視校正，原始圖像尺寸: ${bitmap.width}x${bitmap.height}")

            // 將 Bitmap 轉換為 OpenCV Mat
            val originalMat = Mat()
            Utils.bitmapToMat(bitmap, originalMat)

            // 計算輸出尺寸
            val outputSize = calculateOutputSize(corners, outputWidth, outputHeight)

            // 執行 OpenCV 透視變換
            val correctedMat = performOpenCVPerspectiveTransform(originalMat, corners, outputSize)

            if (correctedMat != null) {
                // 將結果轉換回 Bitmap
                val correctedBitmap = Bitmap.createBitmap(
                    outputSize.first, outputSize.second, Bitmap.Config.ARGB_8888
                )
                Utils.matToBitmap(correctedMat, correctedBitmap)

                // 釋放資源
                originalMat.release()
                correctedMat.release()

                Log.d(TAG, "OpenCV 透視校正成功，輸出尺寸: ${outputSize.first}x${outputSize.second}")
                return correctedBitmap
            } else {
                originalMat.release()
                Log.w(TAG, "OpenCV 透視變換失敗，使用簡化版")
                return fallbackPerspectiveCorrection(bitmap, corners, outputWidth, outputHeight)
            }

        } catch (e: Exception) {
            Log.e(TAG, "OpenCV 透視校正失敗", e)
            return fallbackPerspectiveCorrection(bitmap, corners, outputWidth, outputHeight)
        }
    }

    /**
     * 執行 OpenCV 透視變換
     */
    private fun performOpenCVPerspectiveTransform(
        originalMat: Mat,
        corners: List<PointF>,
        outputSize: Pair<Int, Int>
    ): Mat? {
        try {
            // 驗證角點順序和質量
            validateCorners(corners)

            // 創建源點矩陣（四個角點）
            val srcPoints = MatOfPoint2f()
            val srcArray = corners.map { org.opencv.core.Point(it.x.toDouble(), it.y.toDouble()) }.toTypedArray()
            srcPoints.fromArray(*srcArray)

            Log.d(TAG, "源點座標: ${srcArray.map { "(${it.x}, ${it.y})" }}")
            Log.d(TAG, "目標尺寸: ${outputSize.first}x${outputSize.second}")

            // 創建目標點矩陣（矩形）
            val dstPoints = MatOfPoint2f()
            val dstArray = arrayOf(
                org.opencv.core.Point(0.0, 0.0),                                    // 左上
                org.opencv.core.Point(outputSize.first.toDouble(), 0.0),            // 右上
                org.opencv.core.Point(outputSize.first.toDouble(), outputSize.second.toDouble()), // 右下
                org.opencv.core.Point(0.0, outputSize.second.toDouble())            // 左下
            )
            dstPoints.fromArray(*dstArray)

            Log.d(TAG, "目標點座標: ${dstArray.map { "(${it.x}, ${it.y})" }}")

            // 計算透視變換矩陣
            val transformMatrix = Imgproc.getPerspectiveTransform(srcPoints, dstPoints)

            // 檢查變換矩陣的有效性
            if (isValidTransformMatrix(transformMatrix)) {
                // 執行透視變換
                val correctedMat = Mat()
                Imgproc.warpPerspective(
                    originalMat,
                    correctedMat,
                    transformMatrix,
                    Size(outputSize.first.toDouble(), outputSize.second.toDouble()),
                    Imgproc.INTER_LINEAR,
                    Core.BORDER_CONSTANT,
                    Scalar(255.0, 255.0, 255.0)  // 白色邊界
                )

                // 釋放資源
                srcPoints.release()
                dstPoints.release()
                transformMatrix.release()

                return correctedMat
            } else {
                // 釋放資源
                srcPoints.release()
                dstPoints.release()
                transformMatrix.release()
                return null
            }

        } catch (e: Exception) {
            Log.e(TAG, "OpenCV 透視變換執行失敗", e)
            return null
        }
    }

    /**
     * 簡化版透視校正（當 OpenCV 不可用時使用）
     */
    private fun fallbackPerspectiveCorrection(
        bitmap: Bitmap,
        corners: List<PointF>,
        outputWidth: Int?,
        outputHeight: Int?
    ): Bitmap? {
        try {
            val outputSize = calculateOutputSize(corners, outputWidth, outputHeight)
            return createCroppedBitmap(bitmap, corners, outputSize)
        } catch (e: Exception) {
            Log.e(TAG, "簡化版透視校正失敗", e)
            return null
        }
    }

    /**
     * 創建裁剪後的圖像（簡化版透視校正）
     */
    private fun createCroppedBitmap(
        bitmap: Bitmap,
        corners: List<PointF>,
        outputSize: Pair<Int, Int>
    ): Bitmap {
        // 計算邊界矩形
        val minX = corners.minOf { it.x }.toInt().coerceAtLeast(0)
        val minY = corners.minOf { it.y }.toInt().coerceAtLeast(0)
        val maxX = corners.maxOf { it.x }.toInt().coerceAtMost(bitmap.width)
        val maxY = corners.maxOf { it.y }.toInt().coerceAtMost(bitmap.height)

        val cropWidth = maxX - minX
        val cropHeight = maxY - minY

        // 裁剪圖像
        val croppedBitmap = Bitmap.createBitmap(
            bitmap, minX, minY, cropWidth, cropHeight
        )

        // 縮放到目標尺寸
        return Bitmap.createScaledBitmap(
            croppedBitmap, outputSize.first, outputSize.second, true
        ).also {
            if (croppedBitmap != it) {
                croppedBitmap.recycle()
            }
        }
    }

    /**
     * 計算輸出圖像尺寸
     */
    private fun calculateOutputSize(
        corners: List<PointF>,
        outputWidth: Int?,
        outputHeight: Int?
    ): Pair<Int, Int> {
        if (outputWidth != null && outputHeight != null) {
            return Pair(outputWidth, outputHeight)
        }

        // 計算文檔的實際尺寸
        val topWidth = distance(corners[0], corners[1])
        val bottomWidth = distance(corners[2], corners[3])
        val leftHeight = distance(corners[0], corners[3])
        val rightHeight = distance(corners[1], corners[2])

        // 使用較長的邊作為參考，提高精度
        val maxWidth = kotlin.math.max(topWidth, bottomWidth)
        val maxHeight = kotlin.math.max(leftHeight, rightHeight)

        val aspectRatio = maxWidth / maxHeight

        Log.d(TAG, "邊長計算 - 上寬: $topWidth, 下寬: $bottomWidth, 左高: $leftHeight, 右高: $rightHeight")
        Log.d(TAG, "最大尺寸 - 寬: $maxWidth, 高: $maxHeight, 比例: $aspectRatio")

        return if (outputWidth != null) {
            Pair(outputWidth, (outputWidth.toFloat() / aspectRatio).toInt())
        } else if (outputHeight != null) {
            Pair((outputHeight.toFloat() * aspectRatio).toInt(), outputHeight)
        } else {
            // 使用默認尺寸
            val width = DEFAULT_OUTPUT_WIDTH
            val height = if (aspectRatio > 1) {
                (width.toFloat() / aspectRatio).toInt()
            } else {
                (width.toFloat() * DEFAULT_ASPECT_RATIO).toInt()
            }
            Pair(width, height)
        }
    }
    
    /**
     * 計算兩點間距離
     */
    private fun distance(p1: PointF, p2: PointF): Float {
        val dx = p1.x - p2.x
        val dy = p1.y - p2.y
        return sqrt(dx * dx + dy * dy)
    }

    /**
     * 驗證角點的有效性
     */
    private fun validateCorners(corners: List<PointF>) {
        if (corners.size != 4) {
            throw IllegalArgumentException("需要恰好 4 個角點")
        }

        // 檢查角點是否形成有效的四邊形
        val area = calculatePolygonArea(corners)
        if (area < 100) {  // 最小面積檢查
            Log.w(TAG, "⚠️ 檢測到的四邊形面積過小: $area")
        }

        // 檢查角點是否按正確順序排列（應該是順時針或逆時針）
        val isClockwise = isClockwiseOrder(corners)
        Log.d(TAG, "角點順序: ${if (isClockwise) "順時針" else "逆時針"}")

        // 計算邊長比例，檢查是否合理
        val topWidth = distance(corners[0], corners[1])
        val bottomWidth = distance(corners[2], corners[3])
        val leftHeight = distance(corners[0], corners[3])
        val rightHeight = distance(corners[1], corners[2])

        val widthRatio = kotlin.math.max(topWidth, bottomWidth) / kotlin.math.min(topWidth, bottomWidth)
        val heightRatio = kotlin.math.max(leftHeight, rightHeight) / kotlin.math.min(leftHeight, rightHeight)

        Log.d(TAG, "邊長比例 - 寬度: $widthRatio, 高度: $heightRatio")

        if (widthRatio > 3.0 || heightRatio > 3.0) {
            Log.w(TAG, "⚠️ 檢測到的四邊形形狀可能不正確，邊長比例過大")
        }
    }

    /**
     * 計算多邊形面積
     */
    private fun calculatePolygonArea(corners: List<PointF>): Float {
        var area = 0.0f
        for (i in corners.indices) {
            val j = (i + 1) % corners.size
            area += corners[i].x * corners[j].y
            area -= corners[j].x * corners[i].y
        }
        return abs(area) / 2.0f
    }

    /**
     * 檢查角點是否按順時針順序排列
     */
    private fun isClockwiseOrder(corners: List<PointF>): Boolean {
        var sum = 0.0f
        for (i in corners.indices) {
            val j = (i + 1) % corners.size
            sum += (corners[j].x - corners[i].x) * (corners[j].y + corners[i].y)
        }
        return sum > 0
    }

    /**
     * 檢查變換矩陣是否有效
     */
    private fun isValidTransformMatrix(matrix: Mat): Boolean {
        // 檢查矩陣是否為空或無效
        if (matrix.empty() || matrix.rows() != 3 || matrix.cols() != 3) {
            Log.e(TAG, "❌ 變換矩陣無效")
            return false
        }

        // 檢查矩陣的行列式，確保變換是可逆的
        val det = Core.determinant(matrix)
        if (abs(det) < 1e-6) {
            Log.e(TAG, "❌ 變換矩陣接近奇異，行列式: $det")
            return false
        }

        Log.d(TAG, "✅ 變換矩陣有效，行列式: $det")
        return true
    }
    
    /**
     * 自動校正文檔
     * 結合邊界檢測和透視變換
     */
    fun autoCorrectDocument(
        bitmap: Bitmap,
        outputWidth: Int? = null,
        outputHeight: Int? = null
    ): Bitmap? {
        val detector = DocumentBoundaryDetector()
        val corners = detector.detectDocumentBoundary(bitmap)
        
        return if (corners != null) {
            correctPerspective(bitmap, corners, outputWidth, outputHeight)
        } else {
            Log.w(TAG, "No document boundary detected")
            null
        }
    }
    
    /**
     * 驗證角點是否有效
     */
    fun validateCorners(corners: List<PointF>, imageWidth: Int, imageHeight: Int): Boolean {
        if (corners.size != 4) return false
        
        // 檢查所有點是否在圖像範圍內
        for (corner in corners) {
            if (corner.x < 0 || corner.x > imageWidth || 
                corner.y < 0 || corner.y > imageHeight) {
                return false
            }
        }
        
        // 檢查是否形成有效的四邊形（面積大於0）
        val area = calculateQuadrilateralArea(corners)
        return area > 100 // 最小面積閾值
    }
    
    /**
     * 計算四邊形面積
     */
    private fun calculateQuadrilateralArea(corners: List<PointF>): Float {
        if (corners.size != 4) return 0f
        
        // 使用鞋帶公式計算多邊形面積
        var area = 0f
        for (i in corners.indices) {
            val j = (i + 1) % corners.size
            area += corners[i].x * corners[j].y
            area -= corners[j].x * corners[i].y
        }
        return abs(area) / 2f
    }
}
