package com.erroranalysis.app.ui.study

import android.content.Intent
import android.os.Bundle
import android.os.Environment
import android.util.Log
import android.view.ContextThemeWrapper
import android.view.Menu
import android.view.MenuItem
import android.widget.PopupMenu
import android.widget.Toast
import java.io.File
import androidx.activity.result.contract.ActivityResultContracts
import androidx.documentfile.provider.DocumentFile
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import com.erroranalysis.app.R
import com.erroranalysis.app.databinding.ActivityDeckDetailBinding
import com.erroranalysis.app.databinding.DialogCardFilterBinding
import com.erroranalysis.app.ui.base.ThemedActivity
import com.erroranalysis.app.ui.study.adapters.StudyCardAdapter
import com.erroranalysis.app.ui.theme.AppTheme
import com.erroranalysis.app.utils.PdfExporter
import com.google.android.material.chip.Chip
import kotlinx.coroutines.launch

class DeckDetailActivity : ThemedActivity() {

    private lateinit var binding: ActivityDeckDetailBinding
    private lateinit var cardAdapter: StudyCardAdapter
    private lateinit var viewModel: DeckDetailViewModel
    private lateinit var deck: SimpleDeck

    // 文件選擇器
    private val directoryPickerLauncher = registerForActivityResult(
        ActivityResultContracts.OpenDocumentTree()
    ) { uri ->
        uri?.let { selectedUri ->
            // 用戶選擇了目錄，開始PDF匯出
            exportToPdfInDirectory(selectedUri)
        }
    }
    private lateinit var deckId: String
    private var currentFilter = CardFilter()
    private var currentSortType = CardSortType.CREATED_TIME_DESC
    private lateinit var pdfExporter: PdfExporter

    companion object {
        const val EXTRA_DECK = "extra_deck"
        const val REQUEST_CREATE_CARD = 1001
        const val REQUEST_EDIT_CARD = 1002
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDeckDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)

        deck = intent.getParcelableExtra(EXTRA_DECK) ?: run {
            finish()
            return
        }
        deckId = deck.id

        val factory = DeckDetailViewModelFactory(application, deckId)
        viewModel = ViewModelProvider(this, factory)[DeckDetailViewModel::class.java]
        pdfExporter = PdfExporter(this)

        setupToolbar()
        setupRecyclerView()
        setupFab()
        setupFilterButton()
        setupMoreOptionsButton()
        setupObservers()
        viewModel.loadCards()

        applyTheme()
    }





    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun setupObservers() {
        viewModel.filteredCards.observe(this) { cards ->
            cardAdapter.submitList(cards)
            updateEmptyState(cards.isEmpty())
            updateFilterIndicator(cards.size)
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayShowTitleEnabled(false)
            setDisplayHomeAsUpEnabled(true)
        }
        binding.textDeckTitle.text = deck.name
    }

    private fun setupRecyclerView() {
        cardAdapter = StudyCardAdapter(
            onCardClick = { card -> viewCard(card) },
            onCardLongClick = { card -> showCardOptions(card) },
            onStarClick = { card -> toggleCardStar(card) }
        )
        binding.recyclerCards.apply {
            layoutManager = LinearLayoutManager(this@DeckDetailActivity)
            adapter = cardAdapter
        }
        setupSwipeToDelete()
    }

    private fun setupFab() {
        binding.fabCreateCard.setOnClickListener {
            showCreateCardDialog()
        }
    }

    private fun setupFilterButton() {
        binding.btnFilter.setOnClickListener {
            showFilterDialog()
        }
        binding.btnSort.setOnClickListener {
            showCardSortDialog()
        }
    }

    private fun setupMoreOptionsButton() {
        binding.btnMoreOptions.setOnClickListener { view ->
            showMoreOptionsMenu(view)
        }
    }

    private fun showMoreOptionsMenu(anchorView: android.view.View) {
        // 創建PopupMenu並設置自定義樣式
        val wrapper = ContextThemeWrapper(this, R.style.CustomPopupMenuStyle)
        val popup = PopupMenu(wrapper, anchorView)
        popup.menuInflater.inflate(R.menu.menu_deck_more_options, popup.menu)

        // 設置選單項目的文字顏色為藍色
        try {
            val menu = popup.menu
            for (i in 0 until menu.size()) {
                val menuItem = menu.getItem(i)
                val spanString = android.text.SpannableString(menuItem.title.toString())
                spanString.setSpan(
                    android.text.style.ForegroundColorSpan(androidx.core.content.ContextCompat.getColor(this, R.color.primary)),
                    0,
                    spanString.length,
                    0
                )
                menuItem.title = spanString
            }
        } catch (e: Exception) {
            Log.e("DeckDetailActivity", "設置PopupMenu文字顏色失敗", e)
        }

        popup.setOnMenuItemClickListener { item ->
            when (item.itemId) {
                R.id.action_export_pdf -> {
                    exportToPdf()
                    true
                }
                R.id.action_share_pdf -> {
                    sharePdf()
                    true
                }
                R.id.action_share_deck -> {
                    shareDeck()
                    true
                }
                else -> false
            }
        }

        popup.show()
    }

    private fun exportToPdf() {
        val cards = viewModel.cards.value ?: emptyList()
        if (cards.isEmpty()) {
            Toast.makeText(this, "卡組中沒有卡片可以匯出", Toast.LENGTH_SHORT).show()
            return
        }

        // 啟動目錄選擇器
        try {
            directoryPickerLauncher.launch(null)
        } catch (e: Exception) {
            // 如果目錄選擇器失敗，使用預設位置
            exportToPdfInDefaultLocation(cards)
        }
    }

    private fun sharePdf() {
        val cards = viewModel.cards.value ?: emptyList()
        if (cards.isEmpty()) {
            Toast.makeText(this, "卡組中沒有卡片可以分享", Toast.LENGTH_SHORT).show()
            return
        }

        // 先生成PDF到臨時位置，然後分享
        lifecycleScope.launch {
            try {
                Toast.makeText(this@DeckDetailActivity, "正在生成PDF...", Toast.LENGTH_SHORT).show()

                val pdfExporter = PdfExporter(this@DeckDetailActivity)
                val result = pdfExporter.exportDeckToPdf(deck, cards)

                result.onSuccess { file ->
                    // 分享PDF文件
                    sharePdfFile(file)
                }.onFailure { error ->
                    Toast.makeText(this@DeckDetailActivity, "PDF生成失敗：${error.message}", Toast.LENGTH_LONG).show()
                    Log.e("DeckDetailActivity", "PDF生成失敗", error)
                }
            } catch (e: Exception) {
                Toast.makeText(this@DeckDetailActivity, "分享PDF時發生錯誤：${e.message}", Toast.LENGTH_LONG).show()
                Log.e("DeckDetailActivity", "分享PDF時發生錯誤", e)
            }
        }
    }

    private fun exportToPdfInDirectory(directoryUri: android.net.Uri) {
        val cards = viewModel.cards.value ?: emptyList()
        lifecycleScope.launch {
            try {
                val pdfExporter = PdfExporter(this@DeckDetailActivity)
                val result = pdfExporter.exportDeckToPdfInDirectory(deck, cards, directoryUri)

                result.onSuccess { fileName ->
                    Toast.makeText(this@DeckDetailActivity, "PDF匯出成功：$fileName", Toast.LENGTH_LONG).show()
                }.onFailure { error ->
                    Toast.makeText(this@DeckDetailActivity, "PDF匯出失敗：${error.message}", Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                Toast.makeText(this@DeckDetailActivity, "PDF匯出失敗：${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun sharePdfFile(file: File) {
        try {
            val uri = androidx.core.content.FileProvider.getUriForFile(
                this,
                "${packageName}.fileprovider",
                file
            )

            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                type = "application/pdf"
                putExtra(Intent.EXTRA_STREAM, uri)
                putExtra(Intent.EXTRA_SUBJECT, "卡組：${deck.name}")
                putExtra(Intent.EXTRA_TEXT, "分享卡組「${deck.name}」的PDF文件")
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            val chooser = Intent.createChooser(shareIntent, "分享PDF")
            startActivity(chooser)

            Toast.makeText(this, "PDF已準備好分享", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Toast.makeText(this, "分享PDF失敗：${e.message}", Toast.LENGTH_LONG).show()
            Log.e("DeckDetailActivity", "分享PDF失敗", e)
        }
    }

    private fun shareDeck() {
        lifecycleScope.launch {
            try {
                Toast.makeText(this@DeckDetailActivity, "正在打包卡組...", Toast.LENGTH_SHORT).show()

                val deckDataManager = com.erroranalysis.app.data.DeckDataManager(this@DeckDetailActivity)
                val result = deckDataManager.exportDeckAsZip(deck.id)

                result.onSuccess { zipFile ->
                    // 分享ZIP文件
                    shareZipFile(zipFile)
                }.onFailure { error ->
                    Toast.makeText(this@DeckDetailActivity, "分享失敗：${error.message}", Toast.LENGTH_LONG).show()
                    Log.e("DeckDetailActivity", "分享卡組失敗", error)
                }
            } catch (e: Exception) {
                Toast.makeText(this@DeckDetailActivity, "分享卡組時發生錯誤：${e.message}", Toast.LENGTH_LONG).show()
                Log.e("DeckDetailActivity", "分享卡組時發生錯誤", e)
            }
        }
    }

    private fun shareZipFile(file: File) {
        try {
            val uri = androidx.core.content.FileProvider.getUriForFile(
                this,
                "${packageName}.fileprovider",
                file
            )

            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                type = "application/zip"
                putExtra(Intent.EXTRA_STREAM, uri)
                putExtra(Intent.EXTRA_SUBJECT, "卡組：${deck.name}")
                putExtra(Intent.EXTRA_TEXT, "分享卡組「${deck.name}」，包含所有題目和圖片")
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            val chooser = Intent.createChooser(shareIntent, "分享卡組")
            startActivity(chooser)

            Toast.makeText(this, "卡組已準備好分享", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Toast.makeText(this, "分享卡組失敗：${e.message}", Toast.LENGTH_LONG).show()
            Log.e("DeckDetailActivity", "分享卡組失敗", e)
        }
    }

    private fun exportToPdfInDefaultLocation(cards: List<StudyCard>) {
        lifecycleScope.launch {
            try {
                val pdfExporter = PdfExporter(this@DeckDetailActivity)
                val result = pdfExporter.exportDeckToPdf(deck, cards)

                result.onSuccess { file ->
                    Toast.makeText(this@DeckDetailActivity, "PDF匯出成功：${file.name}", Toast.LENGTH_LONG).show()
                }.onFailure { error ->
                    Toast.makeText(this@DeckDetailActivity, "PDF匯出失敗：${error.message}", Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                Toast.makeText(this@DeckDetailActivity, "PDF匯出失敗：${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun setupSwipeToDelete() {
        val itemTouchHelper = ItemTouchHelper(object : ItemTouchHelper.SimpleCallback(
            0,
            ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT
        ) {
            override fun onMove(
                recyclerView: androidx.recyclerview.widget.RecyclerView,
                viewHolder: androidx.recyclerview.widget.RecyclerView.ViewHolder,
                target: androidx.recyclerview.widget.RecyclerView.ViewHolder
            ): Boolean {
                return false
            }

            override fun onSwiped(
                viewHolder: androidx.recyclerview.widget.RecyclerView.ViewHolder,
                direction: Int
            ) {
                val position = viewHolder.adapterPosition
                if (position != androidx.recyclerview.widget.RecyclerView.NO_POSITION) {
                    viewModel.filteredCards.value?.get(position)?.let { card ->
                        showSwipeDeleteConfirmation(card, position)
                    }
                }
            }

            override fun getSwipeThreshold(viewHolder: androidx.recyclerview.widget.RecyclerView.ViewHolder): Float {
                return 0.3f
            }

            override fun onChildDraw(
                c: android.graphics.Canvas,
                recyclerView: androidx.recyclerview.widget.RecyclerView,
                viewHolder: androidx.recyclerview.widget.RecyclerView.ViewHolder,
                dX: Float,
                dY: Float,
                actionState: Int,
                isCurrentlyActive: Boolean
            ) {
                if (actionState == ItemTouchHelper.ACTION_STATE_SWIPE) {
                    val itemView = viewHolder.itemView
                    val paint = android.graphics.Paint()
                    if (dX > 0) {
                        paint.color = android.graphics.Color.parseColor("#F44336")
                        c.drawRect(
                            itemView.left.toFloat(),
                            itemView.top.toFloat(),
                            dX,
                            itemView.bottom.toFloat(),
                            paint
                        )
                        val deleteIcon = androidx.core.content.ContextCompat.getDrawable(
                            this@DeckDetailActivity,
                            android.R.drawable.ic_menu_delete
                        )
                        deleteIcon?.let { icon ->
                            val iconSize = 64
                            val iconMargin = (itemView.height - iconSize) / 2
                            icon.setBounds(
                                itemView.left + iconMargin,
                                itemView.top + iconMargin,
                                itemView.left + iconMargin + iconSize,
                                itemView.bottom - iconMargin
                            )
                            icon.setTint(android.graphics.Color.WHITE)
                            icon.draw(c)
                        }
                    } else if (dX < 0) {
                        paint.color = android.graphics.Color.parseColor("#F44336")
                        c.drawRect(
                            itemView.right.toFloat() + dX,
                            itemView.top.toFloat(),
                            itemView.right.toFloat(),
                            itemView.bottom.toFloat(),
                            paint
                        )
                        val deleteIcon = androidx.core.content.ContextCompat.getDrawable(
                            this@DeckDetailActivity,
                            android.R.drawable.ic_menu_delete
                        )
                        deleteIcon?.let { icon ->
                            val iconSize = 64
                            val iconMargin = (itemView.height - iconSize) / 2
                            icon.setBounds(
                                itemView.right - iconMargin - iconSize,
                                itemView.top + iconMargin,
                                itemView.right - iconMargin,
                                itemView.bottom - iconMargin
                            )
                            icon.setTint(android.graphics.Color.WHITE)
                            icon.draw(c)
                        }
                    }
                }
                super.onChildDraw(c, recyclerView, viewHolder, dX, dY, actionState, isCurrentlyActive)
            }
        })
        itemTouchHelper.attachToRecyclerView(binding.recyclerCards)
    }



    override fun onResume() {
        super.onResume()
        viewModel.loadCards()
    }

    private fun showCreateCardDialog() {
        val intent = Intent(this, CardEditActivity::class.java)
        intent.putExtra(CardEditActivity.EXTRA_DECK_ID, deck.id)
        startActivityForResult(intent, REQUEST_CREATE_CARD)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            when (requestCode) {
                REQUEST_CREATE_CARD, REQUEST_EDIT_CARD -> {
                    val cardSaved = data?.getBooleanExtra(CardEditActivity.RESULT_CARD_SAVED, false) ?: false
                    if (cardSaved) {
                        viewModel.loadCards()
                    }
                }
            }
        }
    }

    private fun viewCard(card: StudyCard) {
        val intent = Intent(this, CardViewerActivity::class.java)
        intent.putExtra(CardViewerActivity.EXTRA_CARD, card)
        intent.putExtra(CardViewerActivity.EXTRA_DECK_ID, deckId)
        startActivity(intent)
    }

    private fun showCardOptions(card: StudyCard) {
        val options = arrayOf("編輯", "移動", "刪除")
        AlertDialog.Builder(this)
            .setTitle("卡片選項")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> editCard(card)
                    1 -> moveCard(card)
                    2 -> deleteCard(card)
                }
            }
            .show()
    }

    private fun editCard(card: StudyCard) {
        val intent = Intent(this, CardEditActivity::class.java)
        intent.putExtra(CardEditActivity.EXTRA_DECK_ID, deck.id)
        intent.putExtra(CardEditActivity.EXTRA_CARD, card)
        startActivityForResult(intent, REQUEST_EDIT_CARD)
    }

    private fun moveCard(card: StudyCard) {
        val allDecks = viewModel.loadAllDecks()
        val targetDecks = allDecks.filter { it.id != deckId }
        if (targetDecks.isEmpty()) {
            AlertDialog.Builder(this)
                .setTitle("無法移動")
                .setMessage("沒有其他卡組可以移動到")
                .setPositiveButton("確定", null)
                .show()
            return
        }
        val deckNames = targetDecks.map { "${it.icon} ${it.name}" }.toTypedArray()
        AlertDialog.Builder(this)
            .setTitle("移動卡片到")
            .setItems(deckNames) { _, which ->
                val targetDeck = targetDecks[which]
                confirmMoveCard(card, targetDeck)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun confirmMoveCard(card: StudyCard, targetDeck: SimpleDeck) {
        AlertDialog.Builder(this)
            .setTitle("確認移動")
            .setMessage("確定要將此卡片移動到「${targetDeck.name}」嗎？")
            .setPositiveButton("移動") { _, _ ->
                viewModel.moveCard(card.id, targetDeck.id)
                Toast.makeText(this, "卡片已移動到「${targetDeck.name}」", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun toggleCardStar(card: StudyCard) {
        viewModel.toggleCardStar(card.id)
    }

    private fun showSwipeDeleteConfirmation(card: StudyCard, position: Int) {
        AlertDialog.Builder(this)
            .setTitle("刪除卡片")
            .setMessage("確定要刪除這張卡片嗎？")
            .setPositiveButton("刪除") { _, _ ->
                performDeleteCard(card)
            }
            .setNegativeButton("取消") { _, _ ->
                cardAdapter.notifyItemChanged(position)
            }
            .setOnCancelListener {
                cardAdapter.notifyItemChanged(position)
            }
            .show()
    }

    private fun deleteCard(card: StudyCard) {
        AlertDialog.Builder(this)
            .setTitle("刪除卡片")
            .setMessage("確定要刪除這張卡片嗎？")
            .setPositiveButton("刪除") { _, _ ->
                performDeleteCard(card)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun performDeleteCard(card: StudyCard) {
        viewModel.deleteCard(card.id)
        Toast.makeText(this, "卡片已刪除", Toast.LENGTH_SHORT).show()
    }

    private fun updateEmptyState(isEmpty: Boolean) {
        binding.layoutEmpty.visibility = if (isEmpty) android.view.View.VISIBLE else android.view.View.GONE
        binding.recyclerCards.visibility = if (isEmpty) android.view.View.GONE else android.view.View.VISIBLE
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    private fun showFilterDialog() {
        val dialogBinding = DialogCardFilterBinding.inflate(layoutInflater)
        dialogBinding.editKeyword.setText(currentFilter.keyword)
        setupMasteryChips(dialogBinding)
        setupStarChips(dialogBinding)
        setupTagChips(dialogBinding)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogBinding.root)
            .create()
        dialogBinding.btnClear.setOnClickListener {
            currentFilter = CardFilter()
            applyFilter()
            dialog.dismiss()
        }
        dialogBinding.btnApply.setOnClickListener {
            val keyword = dialogBinding.editKeyword.text.toString().trim()
            val selectedMastery = getSelectedMasteryLevels(dialogBinding)
            val selectedTags = getSelectedTags(dialogBinding)
            val (showStarred, showNotStarred) = getSelectedStarOptions(dialogBinding)
            currentFilter = CardFilter(keyword, selectedMastery, selectedTags, showStarred, showNotStarred)
            viewModel.applyFilter(currentFilter, currentSortType)
            dialog.dismiss()
        }
        dialog.show()
    }

    private fun setupMasteryChips(dialogBinding: DialogCardFilterBinding) {
        val masteryChips = mapOf(
            dialogBinding.chipLevel1 to CardMastery.LEVEL_1,
            dialogBinding.chipLevel2 to CardMastery.LEVEL_2,
            dialogBinding.chipLevel3 to CardMastery.LEVEL_3,
            dialogBinding.chipLevel4 to CardMastery.LEVEL_4,
            dialogBinding.chipLevel5 to CardMastery.LEVEL_5
        )
        masteryChips.forEach { (chip, mastery) ->
            chip.isChecked = currentFilter.selectedMasteryLevels.contains(mastery)
        }
    }

    private fun setupStarChips(dialogBinding: DialogCardFilterBinding) {
        dialogBinding.chipStarred.isChecked = currentFilter.showStarred
        dialogBinding.chipNotStarred.isChecked = currentFilter.showNotStarred
    }

    private fun setupTagChips(dialogBinding: DialogCardFilterBinding) {
        val allTags = viewModel.cards.value.orEmpty().flatMap { it.tags }.distinct().sorted()
        dialogBinding.chipGroupTags.removeAllViews()
        allTags.forEach { tag ->
            val chip = Chip(this)
            chip.text = tag
            chip.isCheckable = true
            chip.isChecked = currentFilter.selectedTags.contains(tag)
            dialogBinding.chipGroupTags.addView(chip)
        }
    }

    private fun getSelectedMasteryLevels(dialogBinding: DialogCardFilterBinding): Set<CardMastery> {
        val selected = mutableSetOf<CardMastery>()
        if (dialogBinding.chipLevel1.isChecked) selected.add(CardMastery.LEVEL_1)
        if (dialogBinding.chipLevel2.isChecked) selected.add(CardMastery.LEVEL_2)
        if (dialogBinding.chipLevel3.isChecked) selected.add(CardMastery.LEVEL_3)
        if (dialogBinding.chipLevel4.isChecked) selected.add(CardMastery.LEVEL_4)
        if (dialogBinding.chipLevel5.isChecked) selected.add(CardMastery.LEVEL_5)
        return selected
    }

    private fun getSelectedStarOptions(dialogBinding: DialogCardFilterBinding): Pair<Boolean, Boolean> {
        return Pair(
            dialogBinding.chipStarred.isChecked,
            dialogBinding.chipNotStarred.isChecked
        )
    }

    private fun getSelectedTags(dialogBinding: DialogCardFilterBinding): Set<String> {
        val selected = mutableSetOf<String>()
        for (i in 0 until dialogBinding.chipGroupTags.childCount) {
            val chip = dialogBinding.chipGroupTags.getChildAt(i) as Chip
            if (chip.isChecked) {
                selected.add(chip.text.toString())
            }
        }
        return selected
    }

    private fun applyFilter() {
        viewModel.applyFilter(currentFilter, currentSortType)
    }

    private fun showCardSortDialog() {
        val sortOptions = CardSortType.values().map { it.displayName }.toTypedArray()
        val currentIndex = CardSortType.values().indexOf(currentSortType)
        AlertDialog.Builder(this)
            .setTitle("排序方式")
            .setSingleChoiceItems(sortOptions, currentIndex) { dialog, which ->
                currentSortType = CardSortType.values()[which]
                viewModel.applyFilter(currentFilter, currentSortType)
                dialog.dismiss()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun updateFilterIndicator(filteredCardCount: Int) {
        val currentFilter = viewModel.currentFilter
        if (currentFilter.hasFilter()) {
            binding.btnFilter.setImageResource(R.drawable.ic_filter_active)
        } else {
            binding.btnFilter.setImageResource(R.drawable.ic_filter_inactive)
        }
    }

    override fun onApplyTheme(theme: AppTheme) {
        findViewById<android.view.View>(android.R.id.content).setBackgroundColor(theme.getBackgroundColorInt())
        binding.toolbar.setBackgroundColor(theme.getPrimaryColorInt())
        binding.fabCreateCard.backgroundTintList =
            android.content.res.ColorStateList.valueOf(theme.getPrimaryColorInt())
    }
}

/**
 * 卡片排序類型
 */
enum class CardSortType(val displayName: String) {
    CREATED_TIME_DESC("創建時間（新到舊）"),
    CREATED_TIME_ASC("創建時間（舊到新）"),
    STARRED_FIRST("重要優先"),
    MASTERY_DESC("熟練度（高到低）"),
    MASTERY_ASC("熟練度（低到高）"),
    REVIEW_COUNT_DESC("複習次數（多到少）"),
    REVIEW_COUNT_ASC("複習次數（少到多）")
}