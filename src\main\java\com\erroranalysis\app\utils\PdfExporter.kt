package com.erroranalysis.app.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Log
import androidx.documentfile.provider.DocumentFile
import com.erroranalysis.app.ui.study.SimpleDeck
import com.erroranalysis.app.ui.study.StudyCard
import com.erroranalysis.app.utils.ImageStorageManager
import com.itextpdf.io.image.ImageDataFactory
import com.itextpdf.kernel.pdf.PdfDocument
import com.itextpdf.kernel.pdf.PdfWriter
import com.itextpdf.layout.Document
import com.itextpdf.layout.element.AreaBreak
import com.itextpdf.layout.element.Image
import com.itextpdf.layout.element.Paragraph
import com.itextpdf.layout.element.Text
import com.itextpdf.layout.properties.AreaBreakType
import com.itextpdf.layout.properties.TextAlignment
import com.itextpdf.kernel.font.PdfFont
import com.itextpdf.kernel.font.PdfFontFactory
import com.itextpdf.io.font.constants.StandardFonts
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONException
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*

/**
 * PDF匯出工具類
 * 用於將卡組內的卡片匯出為PDF文件
 */
class PdfExporter(private val context: Context) {

    companion object {
        private const val TAG = "PdfExporter"
    }

    private val imageManager = ImageStorageManager(context)

    /**
     * 將卡組匯出為PDF到用戶選擇的目錄
     */
    suspend fun exportDeckToPdfInDirectory(deck: SimpleDeck, cards: List<StudyCard>, directoryUri: Uri): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                val documentFile = DocumentFile.fromTreeUri(context, directoryUri)
                    ?: return@withContext Result.failure(Exception("無法訪問選擇的目錄"))

                val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                val fileName = "${deck.name}_${timestamp}.pdf"

                val pdfFile = documentFile.createFile("application/pdf", fileName)
                    ?: return@withContext Result.failure(Exception("無法在選擇的目錄中創建文件"))

                val outputStream = context.contentResolver.openOutputStream(pdfFile.uri)
                    ?: return@withContext Result.failure(Exception("無法打開文件輸出流"))

                val writer = PdfWriter(outputStream)
                val pdfDocument = PdfDocument(writer)
                val document = Document(pdfDocument)

                // 設置字體（支持中文）
                val font = PdfFontFactory.createFont(StandardFonts.HELVETICA)

                // 添加標題
                addTitle(document, deck, font)

                // 添加卡片內容（題目和答案分開）
                addCardsWithSeparatedContent(document, cards, font)

                // 關閉文檔
                document.close()
                pdfDocument.close()
                writer.close()
                outputStream.close()

                Log.d(TAG, "PDF exported successfully to user directory: $fileName")
                Result.success(fileName)

            } catch (e: Exception) {
                Log.e(TAG, "Error exporting PDF to directory", e)
                Result.failure(e)
            }
        }
    }

    /**
     * 將卡組匯出為PDF到預設位置
     */
    suspend fun exportDeckToPdf(deck: SimpleDeck, cards: List<StudyCard>): Result<File> {
        return withContext(Dispatchers.IO) {
            try {
                // 創建PDF文件
                val pdfFile = createPdfFile(deck.name)
                val writer = PdfWriter(FileOutputStream(pdfFile))
                val pdfDocument = PdfDocument(writer)
                val document = Document(pdfDocument)

                // 設置字體（支持中文）
                val font = PdfFontFactory.createFont(StandardFonts.HELVETICA)

                // 添加標題
                addTitle(document, deck, font)

                // 添加卡片內容（題目和答案分開）
                addCardsWithSeparatedContent(document, cards, font)

                // 關閉文檔
                document.close()
                pdfDocument.close()
                writer.close()

                Log.d(TAG, "PDF exported successfully: ${pdfFile.absolutePath}")
                Result.success(pdfFile)

            } catch (e: Exception) {
                Log.e(TAG, "Error exporting PDF", e)
                Result.failure(e)
            }
        }
    }

    /**
     * 創建PDF文件
     */
    private fun createPdfFile(deckName: String): File {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val fileName = "${deckName}_${timestamp}.pdf"
        val downloadsDir = File(context.getExternalFilesDir(null), "exports")
        if (!downloadsDir.exists()) {
            downloadsDir.mkdirs()
        }
        return File(downloadsDir, fileName)
    }

    /**
     * 添加PDF標題
     */
    private fun addTitle(document: Document, deck: SimpleDeck, font: PdfFont) {
        // 主標題
        val title = Paragraph()
            .add(Text("卡組題目集").setFont(font).setFontSize(20f).setBold())
            .setTextAlignment(TextAlignment.CENTER)
            .setMarginBottom(10f)
        document.add(title)

        // 卡組名稱
        val deckTitle = Paragraph()
            .add(Text("卡組：${deck.name}").setFont(font).setFontSize(16f))
            .setTextAlignment(TextAlignment.CENTER)
            .setMarginBottom(5f)
        document.add(deckTitle)

        // 生成時間
        val timestamp = SimpleDateFormat("yyyy年MM月dd日 HH:mm", Locale.getDefault()).format(Date())
        val timeInfo = Paragraph()
            .add(Text("生成時間：$timestamp").setFont(font).setFontSize(12f))
            .setTextAlignment(TextAlignment.CENTER)
            .setMarginBottom(20f)
        document.add(timeInfo)
    }

    /**
     * 添加卡片內容（題目和答案分開）
     */
    private fun addCardsWithSeparatedContent(document: Document, cards: List<StudyCard>, font: PdfFont) {
        if (cards.isEmpty()) {
            val emptyMessage = Paragraph()
                .add(Text("此卡組暫無卡片").setFont(font).setFontSize(14f))
                .setTextAlignment(TextAlignment.CENTER)
            document.add(emptyMessage)
            return
        }

        // 第一部分：所有題目
        val questionsTitle = Paragraph()
            .add(Text("題目").setFont(font).setFontSize(18f).setBold())
            .setTextAlignment(TextAlignment.CENTER)
            .setMarginTop(20f)
            .setMarginBottom(15f)
        document.add(questionsTitle)

        cards.forEachIndexed { index, card ->
            // 題號
            val questionNumber = Paragraph()
                .add(Text("第 ${index + 1} 題").setFont(font).setFontSize(14f).setBold())
                .setMarginTop(15f)
                .setMarginBottom(8f)
            document.add(questionNumber)

            // 題目內容
            addRichContent(document, card.question, font)

            // 添加分隔線（除了最後一題）
            if (index < cards.size - 1) {
                val separator = Paragraph()
                    .add(Text("─".repeat(40)).setFont(font).setFontSize(8f))
                    .setTextAlignment(TextAlignment.CENTER)
                    .setMarginTop(10f)
                    .setMarginBottom(5f)
                document.add(separator)
            }
        }

        // 分頁
        document.add(AreaBreak(AreaBreakType.NEXT_PAGE))

        // 第二部分：所有答案
        val answersTitle = Paragraph()
            .add(Text("答案").setFont(font).setFontSize(18f).setBold())
            .setTextAlignment(TextAlignment.CENTER)
            .setMarginTop(20f)
            .setMarginBottom(15f)
        document.add(answersTitle)

        cards.forEachIndexed { index, card ->
            // 答案題號
            val answerNumber = Paragraph()
                .add(Text("第 ${index + 1} 題答案").setFont(font).setFontSize(14f).setBold())
                .setMarginTop(15f)
                .setMarginBottom(8f)
            document.add(answerNumber)

            // 答案內容
            if (card.answer.isNotBlank()) {
                addRichContent(document, card.answer, font)
            } else {
                val noAnswer = Paragraph()
                    .add(Text("（無答案）").setFont(font).setFontSize(12f))
                    .setMarginBottom(5f)
                document.add(noAnswer)
            }

            // 添加分隔線（除了最後一題）
            if (index < cards.size - 1) {
                val separator = Paragraph()
                    .add(Text("─".repeat(40)).setFont(font).setFontSize(8f))
                    .setTextAlignment(TextAlignment.CENTER)
                    .setMarginTop(10f)
                    .setMarginBottom(5f)
                document.add(separator)
            }
        }
    }

    /**
     * 添加圖文混合內容
     */
    private fun addRichContent(document: Document, content: String, font: PdfFont) {
        try {
            if (content.trim().startsWith("[")) {
                // JSON格式，解析圖文混合內容
                val jsonArray = JSONArray(content)
                for (i in 0 until jsonArray.length()) {
                    val item = jsonArray.getJSONObject(i)
                    val type = item.getString("type")
                    val itemContent = item.getString("content")

                    when (type) {
                        "text" -> {
                            if (itemContent.isNotBlank()) {
                                val textParagraph = Paragraph()
                                    .add(Text(itemContent).setFont(font).setFontSize(12f))
                                    .setMarginBottom(5f)
                                document.add(textParagraph)
                            }
                        }
                        "image" -> {
                            addImageToPdf(document, itemContent)
                        }
                    }
                }
            } else {
                // 純文字格式
                if (content.isNotBlank()) {
                    val textParagraph = Paragraph()
                        .add(Text(content).setFont(font).setFontSize(12f))
                        .setMarginBottom(5f)
                    document.add(textParagraph)
                }
            }
        } catch (e: JSONException) {
            // JSON解析失敗，當作純文字處理
            if (content.isNotBlank()) {
                val textParagraph = Paragraph()
                    .add(Text(content).setFont(font).setFontSize(12f))
                    .setMarginBottom(5f)
                document.add(textParagraph)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error adding rich content", e)
            // 出錯時顯示錯誤信息
            val errorParagraph = Paragraph()
                .add(Text("[內容顯示錯誤]").setFont(font).setFontSize(12f))
                .setMarginBottom(5f)
            document.add(errorParagraph)
        }
    }

    /**
     * 添加圖片到PDF
     */
    private fun addImageToPdf(document: Document, imagePath: String) {
        try {
            Log.d(TAG, "嘗試載入圖片: $imagePath")

            // 使用ImageStorageManager載入圖片
            val bitmap = imageManager.loadImage(imagePath)

            if (bitmap != null) {
                Log.d(TAG, "圖片載入成功: ${bitmap.width}x${bitmap.height}")

                // 將Bitmap轉換為字節數組
                val stream = ByteArrayOutputStream()
                bitmap.compress(Bitmap.CompressFormat.JPEG, 80, stream)
                val imageData = ImageDataFactory.create(stream.toByteArray())

                // 創建圖片元素
                val image = Image(imageData)

                // 設置圖片大小（最大寬度400點，保持比例）
                val maxWidth = 400f
                if (image.imageWidth > maxWidth) {
                    val scale = maxWidth / image.imageWidth
                    image.scaleToFit(maxWidth, image.imageHeight * scale)
                }

                // 添加圖片到文檔
                document.add(image)

                // 添加一些間距
                val spacer = Paragraph().setMarginBottom(10f)
                document.add(spacer)

                stream.close()
            } else {
                Log.w(TAG, "圖片載入失敗: $imagePath")
                // 圖片載入失敗，添加佔位符
                addImagePlaceholder(document, imagePath)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error adding image to PDF: $imagePath", e)
            addImagePlaceholder(document, imagePath)
        }
    }

    /**
     * 添加圖片佔位符
     */
    private fun addImagePlaceholder(document: Document, imagePath: String) {
        try {
            val font = PdfFontFactory.createFont(StandardFonts.HELVETICA)
            val placeholder = Paragraph()
                .add(Text("[圖片: $imagePath]").setFont(font).setFontSize(10f))
                .setMarginBottom(5f)
            document.add(placeholder)
        } catch (e: Exception) {
            Log.e(TAG, "Error adding image placeholder", e)
        }
    }
}
