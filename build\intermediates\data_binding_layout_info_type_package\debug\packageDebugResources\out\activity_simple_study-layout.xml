<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_simple_study" modulePackage="com.erroranalysis.app" filePath="app_camera_refactor\src\main\res\layout\activity_simple_study.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_simple_study_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="137" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="8" endLine="51" endOffset="43"/></Target><Target id="@+id/btn_sort" view="ImageButton"><Expressions/><location startLine="26" startOffset="16" endLine="35" endOffset="57"/></Target><Target id="@+id/btn_filter" view="ImageButton"><Expressions/><location startLine="38" startOffset="16" endLine="47" endOffset="57"/></Target><Target id="@+id/edit_search" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="76" startOffset="16" endLine="83" endOffset="67"/></Target><Target id="@+id/recycler_decks" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="87" startOffset="12" endLine="90" endOffset="54"/></Target><Target id="@+id/layout_empty" view="LinearLayout"><Expressions/><location startLine="92" startOffset="12" endLine="122" endOffset="26"/></Target><Target id="@+id/fab_create_deck" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="129" startOffset="4" endLine="135" endOffset="40"/></Target></Targets></Layout>