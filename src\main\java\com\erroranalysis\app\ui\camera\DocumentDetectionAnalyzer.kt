package com.erroranalysis.app.ui.camera

import android.graphics.Bitmap
import android.graphics.PointF
import android.util.Log
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import com.erroranalysis.app.utils.DocumentBoundaryDetector
import java.nio.ByteBuffer

/**
 * 文檔檢測分析器
 * 用於實時檢測相機預覽中的文檔邊界
 */
class DocumentDetectionAnalyzer(
    private val listener: (List<PointF>?) -> Unit
) : ImageAnalysis.Analyzer {
    
    companion object {
        private const val TAG = "DocumentDetectionAnalyzer"
        private const val ANALYSIS_INTERVAL = 15 // 每15幀分析一次
    }
    
    private val documentDetector = DocumentBoundaryDetector()
    private var frameCount = 0
    
    override fun analyze(image: ImageProxy) {
        try {
            frameCount++

            // 控制分析頻率以提高性能
            if (frameCount % ANALYSIS_INTERVAL == 0) {
                // 簡化版本：直接生成模擬邊界，避免複雜的圖像處理
                val boundaries = generateMockBoundaries(image.width, image.height)
                listener(boundaries)
                Log.d(TAG, "Generated mock boundaries for ${image.width}x${image.height}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error analyzing image for document detection", e)
            listener(null)
        } finally {
            // 確保 ImageProxy 被正確關閉
            image.close()
        }
    }

    /**
     * 生成模擬的文檔邊界（簡化版本）
     * 避免複雜的圖像處理，直接返回一個內縮的矩形
     */
    private fun generateMockBoundaries(width: Int, height: Int): List<PointF> {
        val margin = 50f
        return listOf(
            PointF(margin, margin),                    // 左上
            PointF(width - margin, margin),            // 右上
            PointF(width - margin, height - margin),   // 右下
            PointF(margin, height - margin)            // 左下
        )
    }
}
