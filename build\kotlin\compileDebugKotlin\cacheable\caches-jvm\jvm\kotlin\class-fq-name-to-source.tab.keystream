.com.erroranalysis.app.ErrorAnalysisApplication8com.erroranalysis.app.ErrorAnalysisApplication.Companion*com.erroranalysis.app.data.DeckDataManager4com.erroranalysis.app.data.DeckDataManager.Companion6com.erroranalysis.app.data.DeckDataManager.ContentItem*com.erroranalysis.app.data.IDeckRepository2com.erroranalysis.app.data.camera.CameraRepository<com.erroranalysis.app.data.camera.CameraRepository.Companion3com.erroranalysis.app.data.camera.ICameraRepository%com.erroranalysis.app.di.CameraModule'<EMAIL>?com.erroranalysis.app.domain.camera.model.CaptureResult.Success=com.erroranalysis.app.domain.camera.model.CaptureResult.Error=com.erroranalysis.app.domain.camera.model.ImageAnalysisResult?<EMAIL>?com.erroranalysis.app.domain.camera.usecase.CaptureImageUseCase?com.erroranalysis.app.domain.camera.usecase.AnalyzeImageUseCaseKcom.erroranalysis.app.domain.camera.usecase.DetectDocumentBoundariesUseCaseEcom.erroranalysis.app.domain.camera.usecase.CorrectPerspectiveUseCaseBcom.erroranalysis.app.domain.camera.usecase.ProcessDocumentUseCase=com.erroranalysis.app.domain.camera.usecase.PerformOCRUseCase>com.erroranalysis.app.domain.camera.usecase.SolveWithAIUseCaseEcom.erroranalysis.app.domain.camera.usecase.SaveImageToGalleryUseCase>com.erroranalysis.app.domain.camera.usecase.DeleteImageUseCase?com.erroranalysis.app.domain.camera.usecase.GetImageInfoUseCaseDcom.erroranalysis.app.domain.camera.usecase.GetCameraSettingsUseCaseGcom.erroranalysis.app.domain.camera.usecase.UpdateCameraSettingsUseCase:com.erroranalysis.app.domain.camera.usecase.CameraUseCases,com.erroranalysis.app.ui.base.ThemedActivity/com.erroranalysis.app.ui.camera.AiSolveActivity9com.erroranalysis.app.ui.camera.AiSolveActivity.Companion.com.erroranalysis.app.ui.camera.CameraActivity8com.erroranalysis.app.ui.camera.CameraActivity.Companion/com.erroranalysis.app.ui.camera.CameraViewModel-com.erroranalysis.app.ui.camera.CameraUiState+com.erroranalysis.app.ui.camera.CameraEvent:<EMAIL>=com.erroranalysis.app.ui.camera.CameraEvent.DocumentProcessedDcom.erroranalysis.app.ui.camera.CameraEvent.DocumentProcessingFailed8com.erroranalysis.app.ui.camera.CameraEvent.OCRCompleted5com.erroranalysis.app.ui.camera.CameraEvent.OCRFailed?com.erroranalysis.app.ui.camera.CameraEvent.AISolutionCompleted<com.erroranalysis.app.ui.camera.CameraEvent.AISolutionFailed?com.erroranalysis.app.ui.camera.CameraEvent.ImageSavedToGallery;com.erroranalysis.app.ui.camera.CameraEvent.ImageSaveFailed6com.erroranalysis.app.ui.camera.CameraViewModelFactory5com.erroranalysis.app.ui.camera.CombinedImageAnalyzer?com.erroranalysis.app.ui.camera.CombinedImageAnalyzer.Companion7com.erroranalysis.app.ui.camera.CropOverlayTestActivity4com.erroranalysis.app.ui.camera.CropSelectionOverlay=com.erroranalysis.app.ui.camera.CropSelectionOverlay.DragMode4com.erroranalysis.app.ui.camera.DeckSelectionAdapterCcom.erroranalysis.app.ui.camera.DeckSelectionAdapter.DeckViewHolder7com.erroranalysis.app.ui.camera.DocumentBoundaryOverlayAcom.erroranalysis.app.ui.camera.DocumentBoundaryOverlay.Companion9com.erroranalysis.app.ui.camera.DocumentDetectionAnalyzerCcom.erroranalysis.app.ui.camera.DocumentDetectionAnalyzer.Companion-com.erroranalysis.app.ui.camera.FocusAnalyzer1com.erroranalysis.app.ui.camera.PhotoEditActivity;com.erroranalysis.app.ui.camera.PhotoEditActivity.Companion4com.erroranalysis.app.ui.camera.SimpleCameraActivity>com.erroranalysis.app.ui.camera.SimpleCameraActivity.Companion0com.erroranalysis.app.ui.main.SimpleMainActivity7com.erroranalysis.app.ui.settings.BackupRestoreActivityEcom.erroranalysis.app.ui.settings.BackupRestoreActivity.PendingActionAcom.erroranalysis.app.ui.settings.BackupRestoreActivity.Companion3com.erroranalysis.app.ui.settings.FontOptionAdapterHcom.erroranalysis.app.ui.settings.FontOptionAdapter.FontOptionViewHolder2com.erroranalysis.app.ui.settings.SettingsActivity7com.erroranalysis.app.ui.settings.adapters.ThemeAdapterGcom.erroranalysis.app.ui.settings.adapters.ThemeAdapter.ThemeViewHolder2com.erroranalysis.app.ui.study.BatchImportActivity/com.erroranalysis.app.ui.study.CardEditActivity9com.erroranalysis.app.ui.study.CardEditActivity.Companion)com.erroranalysis.app.ui.study.CardFilter+com.erroranalysis.app.ui.study.FilterResult1com.erroranalysis.app.ui.study.CardViewerActivity;com.erroranalysis.app.ui.study.CardViewerActivity.Companion1com.erroranalysis.app.ui.study.DeckDetailActivity;com.erroranalysis.app.ui.study.DeckDetailActivity.Companion+com.erroranalysis.app.ui.study.CardSortType2com.erroranalysis.app.ui.study.DeckDetailViewModel9com.erroranalysis.app.ui.study.DeckDetailViewModelFactory)com.erroranalysis.app.ui.study.DeckFilter+com.erroranalysis.app.ui.study.DeckSortType2com.erroranalysis.app.ui.study.SimpleStudyActivity<com.erroranalysis.app.ui.study.SimpleStudyActivity.Companion)com.erroranalysis.app.ui.study.SimpleDeck(com.erroranalysis.app.ui.study.StudyCard*com.erroranalysis.app.ui.study.CardMastery4com.erroranalysis.app.ui.study.CardMastery.Companion-com.erroranalysis.app.ui.study.CardDifficulty+com.erroranalysis.app.ui.study.ReviewResult(com.erroranalysis.app.ui.study.StudyDeck4com.erroranalysis.app.ui.study.adapters.ColorAdapterDcom.erroranalysis.app.ui.study.adapters.ColorAdapter.ColorViewHolderFcom.erroranalysis.app.ui.study.adapters.ColorAdapter.ColorDiffCallback3com.erroranalysis.app.ui.study.adapters.IconAdapterBcom.erroranalysis.app.ui.study.adapters.IconAdapter.IconViewHolder0com.erroranalysis.app.ui.study.adapters.IconItem6com.erroranalysis.app.ui.study.adapters.IconCollection9com.erroranalysis.app.ui.study.adapters.SimpleDeckAdapterHcom.erroranalysis.app.ui.study.adapters.SimpleDeckAdapter.DeckViewHolderJcom.erroranalysis.app.ui.study.adapters.SimpleDeckAdapter.DeckDiffCallback8com.erroranalysis.app.ui.study.adapters.StudyCardAdapterGcom.erroranalysis.app.ui.study.adapters.StudyCardAdapter.CardViewHolderDcom.erroranalysis.app.ui.study.adapters.StudyCardAdapter.ContentItemIcom.erroranalysis.app.ui.study.adapters.StudyCardAdapter.CardDiffCallback<com.erroranalysis.app.ui.test.DocumentCorrectionTestActivityFcom.erroranalysis.app.ui.test.DocumentCorrectionTestActivity.Companion'com.erroranalysis.app.ui.theme.AppTheme.com.erroranalysis.app.ui.theme.ThemeCollection*com.erroranalysis.app.ui.theme.FontManager3com.erroranalysis.app.ui.theme.FontManager.FontType2com.erroranalysis.app.ui.theme.FontSelectorAdapterAcom.erroranalysis.app.ui.theme.FontSelectorAdapter.FontViewHolder1com.erroranalysis.app.ui.theme.FontSelectorDialog+com.erroranalysis.app.ui.theme.ThemeManager5com.erroranalysis.app.ui.theme.ThemeManager.Companion?com.erroranalysis.app.ui.theme.ThemeManager.ThemeChangeListener)com.erroranalysis.app.ui.theme.ThemeUtils1com.erroranalysis.app.ui.widgets.RichTextEditText;com.erroranalysis.app.ui.widgets.RichTextEditText.Companion;com.erroranalysis.app.ui.widgets.RichTextEditText.ImageInfo;com.erroranalysis.app.ui.widgets.RichTextEditText.MatchInfoCcom.erroranalysis.app.ui.widgets.RichTextEditText.ZoomableImageSpan=com.erroranalysis.app.ui.widgets.RichTextEditText.ContentItem.com.erroranalysis.app.utils.BatchImportManager8com.erroranalysis.app.utils.BatchImportManager.Companion(com.erroranalysis.app.utils.ImportResult0com.erroranalysis.app.utils.ImportResult.Success.com.erroranalysis.app.utils.ImportResult.Error4com.erroranalysis.app.utils.DocumentBoundaryDetector>com.erroranalysis.app.utils.DocumentBoundaryDetector.Companion-com.erroranalysis.app.utils.DocumentProcessor7com.erroranalysis.app.utils.DocumentProcessor.Companion+com.erroranalysis.app.utils.GeminiAIService5com.erroranalysis.app.utils.GeminiAIService.Companion5com.erroranalysis.app.utils.GridSpacingItemDecoration/com.erroranalysis.app.utils.ImageStorageManager9com.erroranalysis.app.utils.ImageStorageManager.Companion,com.erroranalysis.app.utils.MarkdownRenderer6com.erroranalysis.app.utils.MarkdownRenderer.Companion,com.erroranalysis.app.utils.MathFormatHelper%com.erroranalysis.app.utils.OCRHelper/com.erroranalysis.app.utils.OCRHelper.Companion-com.erroranalysis.app.utils.OpenCVInitializer)com.erroranalysis.app.utils.OpenCVManager'com.erroranalysis.app.utils.PdfExporter1com.erroranalysis.app.utils.PdfExporter.Companion0com.erroranalysis.app.utils.PerspectiveCorrector:com.erroranalysis.app.utils.PerspectiveCorrector.Companion/com.erroranalysis.app.utils.SimpleBackupManager9com.erroranalysis.app.utils.SimpleBackupManager.Companion<com.erroranalysis.app.utils.SimpleBackupManager.BackupResultDcom.erroranalysis.app.utils.SimpleBackupManager.BackupResult.SuccessBcom.erroranalysis.app.utils.SimpleBackupManager.BackupResult.Error=com.erroranalysis.app.utils.SimpleBackupManager.RestoreResultEcom.erroranalysis.app.utils.SimpleBackupManager.RestoreResult.SuccessCcom.erroranalysis.app.utils.SimpleBackupManager.RestoreResult.Error2com.erroranalysis.app.databinding.ItemThemeBinding9com.erroranalysis.app.databinding.ActivityCardEditBinding6com.erroranalysis.app.databinding.ItemStudyCardBinding9com.erroranalysis.app.databinding.ActivitySettingsBinding2com.erroranalysis.app.databinding.ItemColorBinding;com.erroranalysis.app.databinding.ActivityDeckDetailBinding<com.erroranalysis.app.databinding.ActivityBatchImportBinding<com.erroranalysis.app.databinding.ActivitySimpleStudyBinding7com.erroranalysis.app.databinding.ItemFontOptionBinding9com.erroranalysis.app.databinding.DialogCardFilterBinding;com.erroranalysis.app.databinding.DialogFontSelectorBinding7com.erroranalysis.app.databinding.ActivityCameraBinding9com.erroranalysis.app.databinding.ItemIconSelectorBinding7com.erroranalysis.app.databinding.ItemSimpleDeckBinding8com.erroranalysis.app.databinding.ActivityAiSolveBinding>com.erroranalysis.app.databinding.ActivityBackupRestoreBinding;com.erroranalysis.app.databinding.ActivityCardViewerBinding5com.erroranalysis.app.databinding.ActivityMainBinding:com.erroranalysis.app.databinding.ItemDeckSelectionBinding:com.erroranalysis.app.databinding.ActivityPhotoEditBinding<com.erroranalysis.app.databinding.DialogDeckSelectionBinding6com.erroranalysis.app.databinding.PopupMenuItemBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      