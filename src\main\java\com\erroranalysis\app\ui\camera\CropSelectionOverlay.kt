package com.erroranalysis.app.ui.camera

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.core.content.ContextCompat
import com.erroranalysis.app.R
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

/**
 * 裁剪選擇框覆蓋層
 * 用於在靜態照片上選擇裁剪區域，保持矩形形狀
 */
class CropSelectionOverlay @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 繪製相關
    private val selectionPaint = Paint().apply {
        color = Color.WHITE
        style = Paint.Style.STROKE
        strokeWidth = 3f
        isAntiAlias = true
    }

    private val cornerPaint = Paint().apply {
        color = Color.WHITE
        style = Paint.Style.STROKE
        strokeWidth = 15f  // 更粗50%的線條 (10f * 1.5 = 15f)
        strokeCap = Paint.Cap.ROUND
        strokeJoin = Paint.Join.ROUND  // 圓角連接
        isAntiAlias = true
    }

    private val overlayPaint = Paint().apply {
        color = Color.BLACK
        alpha = 120 // 半透明灰色遮罩
    }

    // 用於創建圓角矩形路徑
    private val selectionPath = Path()
    private val overlayPath = Path()

    // 選擇框相關
    private var selectionRect = RectF()
    private var isInitialized = false

    // 觸摸處理相關
    private var isDragging = false
    private var dragMode = DragMode.NONE
    private var lastTouchX = 0f
    private var lastTouchY = 0f

    // 最小選擇框大小
    private val minSelectionSize = 100f
    private val cornerRadius = 25f      // 圓角半徑
    private val cornerArcLength = 40f   // 圓弧長度
    private val touchTolerance = 60f    // 增大觸控區域
    private val edgeTouchTolerance = 30f // 邊線觸控區域

    enum class DragMode {
        NONE,
        MOVE,           // 移動整個選擇框
        RESIZE_LEFT,    // 調整左邊線（寬度）
        RESIZE_RIGHT,   // 調整右邊線（寬度）
        RESIZE_TOP,     // 調整上邊線（高度）
        RESIZE_BOTTOM,  // 調整下邊線（高度）
        RESIZE_CORNER   // 調整圓弧角點（四個方向）
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        if (!isInitialized && w > 0 && h > 0) {
            initializeSelection()
        }
    }

    /**
     * 初始化選擇框（默認為中央的矩形）
     */
    private fun initializeSelection() {
        val margin = minOf(width, height) * 0.1f
        selectionRect.set(
            margin,
            margin,
            width - margin,
            height - margin
        )
        isInitialized = true
        invalidate()
    }

    /**
     * 設置選擇框位置
     */
    fun setSelectionRect(rect: RectF) {
        selectionRect.set(rect)
        isInitialized = true
        invalidate()
    }

    /**
     * 獲取當前選擇框
     */
    fun getSelectionRect(): RectF = RectF(selectionRect)

    /**
     * 重置選擇框到默認位置
     */
    fun resetSelection() {
        if (width > 0 && height > 0) {
            initializeSelection()
        } else {
            // 如果尺寸還沒有設置，等待下次 onSizeChanged
            isInitialized = false
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (!isInitialized) return

        // 創建圓角矩形路徑
        createRoundedRectPath()

        // 繪製半透明遮罩（選擇框外的區域）
        drawOverlayWithRoundedCutout(canvas)

        // 只繪製四個角的圓角標記，不繪製完整邊框
        drawCornerMarkers(canvas)
    }

    /**
     * 創建圓角矩形路徑
     */
    private fun createRoundedRectPath() {
        selectionPath.reset()
        selectionPath.addRoundRect(
            selectionRect,
            cornerRadius,
            cornerRadius,
            Path.Direction.CW
        )
    }

    /**
     * 繪製帶圓角鏤空的半透明遮罩
     */
    private fun drawOverlayWithRoundedCutout(canvas: Canvas) {
        // 創建整個畫布的路徑
        overlayPath.reset()
        overlayPath.addRect(0f, 0f, width.toFloat(), height.toFloat(), Path.Direction.CW)

        // 減去圓角選擇框區域
        overlayPath.op(selectionPath, Path.Op.DIFFERENCE)

        // 繪製遮罩
        canvas.drawPath(overlayPath, overlayPaint)
    }

    /**
     * 繪製四個角的圓弧標記（圓弧+延伸直線）
     */
    private fun drawCornerMarkers(canvas: Canvas) {
        val arcRadius = 25f     // 圓弧半徑
        val arcOffset = 8f      // 往內偏移一些距離
        val lineExtension = 45f // 延伸直線的長度（15f * 3 = 45f）

        // 左上角 - 從180度到270度的圓弧+延伸線
        drawCornerArcWithExtension(canvas, selectionRect.left, selectionRect.top, arcRadius, arcOffset, lineExtension, 180f, 90f)

        // 右上角 - 從270度到360度的圓弧+延伸線
        drawCornerArcWithExtension(canvas, selectionRect.right, selectionRect.top, arcRadius, arcOffset, lineExtension, 270f, 90f)

        // 右下角 - 從0度到90度的圓弧+延伸線
        drawCornerArcWithExtension(canvas, selectionRect.right, selectionRect.bottom, arcRadius, arcOffset, lineExtension, 0f, 90f)

        // 左下角 - 從90度到180度的圓弧+延伸線
        drawCornerArcWithExtension(canvas, selectionRect.left, selectionRect.bottom, arcRadius, arcOffset, lineExtension, 90f, 90f)
    }

    /**
     * 繪製圓弧角標記加延伸直線（增強視覺效果）
     */
    private fun drawCornerArcWithExtension(canvas: Canvas, cornerX: Float, cornerY: Float, radius: Float, offset: Float, extension: Float, startAngle: Float, sweepAngle: Float) {
        // 計算圓弧的邊界矩形
        val left: Float
        val top: Float
        val right: Float
        val bottom: Float

        when (startAngle.toInt()) {
            180 -> { // 左上角 - 圓弧往內偏移
                left = cornerX + offset
                top = cornerY + offset
                right = cornerX + offset + radius * 2
                bottom = cornerY + offset + radius * 2
            }
            270 -> { // 右上角 - 圓弧往內偏移
                left = cornerX - offset - radius * 2
                top = cornerY + offset
                right = cornerX - offset
                bottom = cornerY + offset + radius * 2
            }
            0 -> { // 右下角 - 圓弧往內偏移
                left = cornerX - offset - radius * 2
                top = cornerY - offset - radius * 2
                right = cornerX - offset
                bottom = cornerY - offset
            }
            90 -> { // 左下角 - 圓弧往內偏移
                left = cornerX + offset
                top = cornerY - offset - radius * 2
                right = cornerX + offset + radius * 2
                bottom = cornerY - offset
            }
            else -> return // 無效角度
        }

        // 創建圓弧的邊界矩形
        val arcRect = RectF(left, top, right, bottom)

        // 繪製圓弧
        canvas.drawArc(arcRect, startAngle, sweepAngle, false, cornerPaint)

        // 繪製延伸直線
        drawExtensionLines(canvas, arcRect, startAngle, sweepAngle, extension)
    }

    /**
     * 繪製圓弧端點的延伸直線（沿選擇框邊緣方向）
     */
    private fun drawExtensionLines(canvas: Canvas, arcRect: RectF, startAngle: Float, sweepAngle: Float, extension: Float) {
        // 根據起始角度確定是哪個角，並直接繪製對應的延伸線（按正確方向延伸）
        when (startAngle.toInt()) {
            180 -> { // 左上角 (180° → 270°)
                // 圓弧左端點，向下延伸（垂直延伸）
                val leftX = arcRect.left
                val leftY = arcRect.centerY()
                canvas.drawLine(leftX, leftY, leftX, leftY + extension, cornerPaint)

                // 圓弧上端點，向右延伸（水平延伸）
                val topX = arcRect.centerX()
                val topY = arcRect.top
                canvas.drawLine(topX, topY, topX + extension, topY, cornerPaint)
            }
            270 -> { // 右上角 (270° → 360°)
                // 圓弧上端點，向左延伸（水平延伸）
                val topX = arcRect.centerX()
                val topY = arcRect.top
                canvas.drawLine(topX, topY, topX - extension, topY, cornerPaint)

                // 圓弧右端點，向下延伸（垂直延伸）
                val rightX = arcRect.right
                val rightY = arcRect.centerY()
                canvas.drawLine(rightX, rightY, rightX, rightY + extension, cornerPaint)
            }
            0 -> { // 右下角 (0° → 90°)
                // 圓弧右端點，向上延伸（垂直延伸）
                val rightX = arcRect.right
                val rightY = arcRect.centerY()
                canvas.drawLine(rightX, rightY, rightX, rightY - extension, cornerPaint)

                // 圓弧下端點，向左延伸（水平延伸）
                val bottomX = arcRect.centerX()
                val bottomY = arcRect.bottom
                canvas.drawLine(bottomX, bottomY, bottomX - extension, bottomY, cornerPaint)
            }
            90 -> { // 左下角 (90° → 180°)
                // 圓弧下端點，向右延伸（水平延伸）
                val bottomX = arcRect.centerX()
                val bottomY = arcRect.bottom
                canvas.drawLine(bottomX, bottomY, bottomX + extension, bottomY, cornerPaint)

                // 圓弧左端點，向上延伸（垂直延伸）
                val leftX = arcRect.left
                val leftY = arcRect.centerY()
                canvas.drawLine(leftX, leftY, leftX, leftY - extension, cornerPaint)
            }
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        val x = event.x
        val y = event.y

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                return handleTouchDown(x, y)
            }
            MotionEvent.ACTION_MOVE -> {
                return handleTouchMove(x, y)
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                return handleTouchUp()
            }
        }

        return super.onTouchEvent(event)
    }

    private fun handleTouchDown(x: Float, y: Float): Boolean {
        lastTouchX = x
        lastTouchY = y

        // 優先檢查圓弧角點（更大的觸控區域）
        dragMode = when {
            isNearCorner(x, y, selectionRect.left, selectionRect.top) -> DragMode.RESIZE_CORNER
            isNearCorner(x, y, selectionRect.right, selectionRect.top) -> DragMode.RESIZE_CORNER
            isNearCorner(x, y, selectionRect.left, selectionRect.bottom) -> DragMode.RESIZE_CORNER
            isNearCorner(x, y, selectionRect.right, selectionRect.bottom) -> DragMode.RESIZE_CORNER
            // 檢查邊線
            isNearLeftEdge(x, y) -> DragMode.RESIZE_LEFT
            isNearRightEdge(x, y) -> DragMode.RESIZE_RIGHT
            isNearTopEdge(x, y) -> DragMode.RESIZE_TOP
            isNearBottomEdge(x, y) -> DragMode.RESIZE_BOTTOM
            // 檢查是否在選擇框內部
            selectionRect.contains(x, y) -> DragMode.MOVE
            else -> DragMode.NONE
        }

        isDragging = dragMode != DragMode.NONE

        // 添加調試日誌
        android.util.Log.d("CropSelectionOverlay", "觸摸開始 - 位置: ($x, $y), 模式: $dragMode")
        android.util.Log.d("CropSelectionOverlay", "選擇框: ${selectionRect}")

        return isDragging
    }

    private fun handleTouchMove(x: Float, y: Float): Boolean {
        if (!isDragging) return false

        val deltaX = x - lastTouchX
        val deltaY = y - lastTouchY

        // 添加調試日誌
        android.util.Log.d("CropSelectionOverlay", "觸摸移動 - 位置: ($x, $y), 偏移: ($deltaX, $deltaY), 模式: $dragMode")

        when (dragMode) {
            DragMode.MOVE -> {
                moveSelection(deltaX, deltaY)
            }
            DragMode.RESIZE_LEFT -> {
                resizeLeftEdge(deltaX)
            }
            DragMode.RESIZE_RIGHT -> {
                resizeRightEdge(deltaX)
            }
            DragMode.RESIZE_TOP -> {
                resizeTopEdge(deltaY)
            }
            DragMode.RESIZE_BOTTOM -> {
                resizeBottomEdge(deltaY)
            }
            DragMode.RESIZE_CORNER -> {
                resizeCorner(deltaX, deltaY, x, y)
            }
            else -> return false
        }

        lastTouchX = x
        lastTouchY = y
        invalidate()
        return true
    }

    private fun handleTouchUp(): Boolean {
        isDragging = false
        dragMode = DragMode.NONE
        return true
    }

    private fun isNearCorner(x: Float, y: Float, cornerX: Float, cornerY: Float): Boolean {
        return abs(x - cornerX) <= touchTolerance && abs(y - cornerY) <= touchTolerance
    }

    /**
     * 檢查是否接近左邊線
     */
    private fun isNearLeftEdge(x: Float, y: Float): Boolean {
        return abs(x - selectionRect.left) <= edgeTouchTolerance &&
               y >= selectionRect.top - edgeTouchTolerance &&
               y <= selectionRect.bottom + edgeTouchTolerance
    }

    /**
     * 檢查是否接近右邊線
     */
    private fun isNearRightEdge(x: Float, y: Float): Boolean {
        return abs(x - selectionRect.right) <= edgeTouchTolerance &&
               y >= selectionRect.top - edgeTouchTolerance &&
               y <= selectionRect.bottom + edgeTouchTolerance
    }

    /**
     * 檢查是否接近上邊線
     */
    private fun isNearTopEdge(x: Float, y: Float): Boolean {
        return abs(y - selectionRect.top) <= edgeTouchTolerance &&
               x >= selectionRect.left - edgeTouchTolerance &&
               x <= selectionRect.right + edgeTouchTolerance
    }

    /**
     * 檢查是否接近下邊線
     */
    private fun isNearBottomEdge(x: Float, y: Float): Boolean {
        return abs(y - selectionRect.bottom) <= edgeTouchTolerance &&
               x >= selectionRect.left - edgeTouchTolerance &&
               x <= selectionRect.right + edgeTouchTolerance
    }

    private fun moveSelection(deltaX: Float, deltaY: Float) {
        val newRect = RectF(selectionRect)
        newRect.offset(deltaX, deltaY)

        // 確保選擇框不超出邊界
        if (newRect.left >= 0 && newRect.right <= width &&
            newRect.top >= 0 && newRect.bottom <= height) {
            selectionRect.set(newRect)
        }
    }

    /**
     * 調整左邊線（寬度）
     */
    private fun resizeLeftEdge(deltaX: Float) {
        val newLeft = selectionRect.left + deltaX
        if (newLeft >= 0 && selectionRect.right - newLeft >= minSelectionSize) {
            selectionRect.left = newLeft
        }
    }

    /**
     * 調整右邊線（寬度）
     */
    private fun resizeRightEdge(deltaX: Float) {
        val newRight = selectionRect.right + deltaX
        if (newRight <= width && newRight - selectionRect.left >= minSelectionSize) {
            selectionRect.right = newRight
        }
    }

    /**
     * 調整上邊線（高度）
     */
    private fun resizeTopEdge(deltaY: Float) {
        val newTop = selectionRect.top + deltaY
        if (newTop >= 0 && selectionRect.bottom - newTop >= minSelectionSize) {
            selectionRect.top = newTop
        }
    }

    /**
     * 調整下邊線（高度）
     */
    private fun resizeBottomEdge(deltaY: Float) {
        val newBottom = selectionRect.bottom + deltaY
        if (newBottom <= height && newBottom - selectionRect.top >= minSelectionSize) {
            selectionRect.bottom = newBottom
        }
    }

    /**
     * 調整圓弧角點（四個方向）
     */
    private fun resizeCorner(deltaX: Float, deltaY: Float, currentX: Float, currentY: Float) {
        // 判斷是哪個角點
        val isLeftSide = currentX < selectionRect.centerX()
        val isTopSide = currentY < selectionRect.centerY()

        val newRect = RectF(selectionRect)

        // 根據角點位置調整對應的邊
        if (isLeftSide) {
            newRect.left += deltaX
        } else {
            newRect.right += deltaX
        }

        if (isTopSide) {
            newRect.top += deltaY
        } else {
            newRect.bottom += deltaY
        }

        // 確保最小尺寸和邊界約束
        if (newRect.width() >= minSelectionSize && newRect.height() >= minSelectionSize &&
            newRect.left >= 0 && newRect.right <= width &&
            newRect.top >= 0 && newRect.bottom <= height) {
            selectionRect.set(newRect)
        }
    }
}
