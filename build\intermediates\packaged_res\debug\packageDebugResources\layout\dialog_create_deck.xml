<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="建立新卡組"
        android:textSize="20sp"
        android:textStyle="bold" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:tabMode="fixed" />

    <ScrollView
        android:id="@+id/layout_custom"
        android:layout_width="match_parent"
        android:layout_height="400dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="預覽"
                android:textSize="14sp"
                android:textStyle="bold" />

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_preview"
                android:layout_width="120dp"
                android:layout_height="160dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="12dp">

                    <TextView
                        android:id="@+id/text_preview_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📘"
                        android:textSize="32sp" />

                    <TextView
                        android:id="@+id/text_preview_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:ellipsize="end"
                        android:maxLines="2"
                        android:text="卡組名稱"
                        android:gravity="center"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/text_preview_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="0 張"
                        android:textColor="@color/white"
                        android:textSize="10sp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="卡組名稱">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_deck_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="學科模板"
                android:textSize="14sp"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_subject_templates"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="風格模板"
                android:textSize="14sp"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_style_templates"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="考試模板"
                android:textSize="14sp"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_exam_templates"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="顏色選擇"
                android:textSize="14sp"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_colors"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginBottom="16dp" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="科目">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_subject"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="標籤 (用逗號分隔)">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_tags"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="描述 (選填)">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:maxLines="3" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

    </ScrollView>

    <LinearLayout
        android:id="@+id/layout_quick"
        android:layout_width="match_parent"
        android:layout_height="400dp"
        android:orientation="vertical"
        android:visibility="gone">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_quick_templates"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="end"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/button_cancel"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="取消" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/button_create"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="建立" />

    </LinearLayout>

</LinearLayout>
