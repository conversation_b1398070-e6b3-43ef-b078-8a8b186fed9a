<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_card_viewer" modulePackage="com.erroranalysis.app" filePath="app_camera_refactor\src\main\res\layout-land\activity_card_viewer.xml" directory="layout-land" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout-land/activity_card_viewer_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="177" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="15" startOffset="8" endLine="118" endOffset="43"/></Target><Target id="@+id/tv_content_type" view="TextView"><Expressions/><location startLine="48" startOffset="20" endLine="58" endOffset="55"/></Target><Target id="@+id/btn_tts" view="ImageButton"><Expressions/><location startLine="70" startOffset="20" endLine="79" endOffset="57"/></Target><Target id="@+id/btn_edit" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="82" startOffset="20" endLine="95" endOffset="64"/></Target><Target id="@+id/btn_toggle" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="98" startOffset="20" endLine="112" endOffset="64"/></Target><Target id="@+id/content_frame" view="FrameLayout"><Expressions/><location startLine="123" startOffset="4" endLine="175" endOffset="17"/></Target><Target id="@+id/scroll_view" view="ScrollView"><Expressions/><location startLine="135" startOffset="8" endLine="165" endOffset="20"/></Target><Target id="@+id/tv_content" view="TextView"><Expressions/><location startLine="151" startOffset="16" endLine="161" endOffset="66"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="168" startOffset="8" endLine="173" endOffset="39"/></Target></Targets></Layout>