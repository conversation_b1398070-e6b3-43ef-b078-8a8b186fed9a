package com.erroranalysis.app.ui.camera

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.PointF
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.view.View
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.erroranalysis.app.R
import com.erroranalysis.app.databinding.ActivityCameraBinding
import com.erroranalysis.app.di.CameraModule
import com.erroranalysis.app.domain.camera.model.*
import com.erroranalysis.app.utils.OpenCVInitializer
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * 相機 Activity (MVVM 重構版)
 * 遵循 MVVM + Repository 模式
 */
class CameraActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityCameraBinding
    
    // MVVM 架構
    private val viewModel: CameraViewModel by viewModels {
        val repository = CameraModule.provideCameraRepository(this)
        val useCases = CameraModule.provideCameraUseCases(repository)
        CameraViewModelFactory(useCases)
    }
    
    // 相機相關
    private var imageCapture: ImageCapture? = null
    private lateinit var cameraExecutor: ExecutorService
    private var camera: Camera? = null
    private var cameraProvider: ProcessCameraProvider? = null
    
    // AI求解模式狀態
    private var isAiSolveMode = false
    
    companion object {
        private const val TAG = "CameraActivity"
        private const val FILENAME_FORMAT = "yyyy-MM-dd-HH-mm-ss-SSS"
        private const val REQUEST_CODE_PERMISSIONS = 10
        private const val REQUEST_PHOTO_EDIT = 11
        private const val REQUEST_GALLERY_PICK = 12
        private val REQUIRED_PERMISSIONS = arrayOf(Manifest.permission.CAMERA)
    }
    
    // 檢查是否為返回圖片模式
    private val isReturnImageMode by lazy {
        intent.getBooleanExtra("return_image", false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCameraBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // 初始化 OpenCV
        OpenCVInitializer.initialize(this) { success ->
            if (success) {
                Log.d(TAG, "OpenCV 初始化成功")
            } else {
                Log.e(TAG, "OpenCV 初始化失敗")
                Toast.makeText(this, "OpenCV 初始化失敗", Toast.LENGTH_SHORT).show()
            }
        }
        
        // 檢查是否為 AI 求解模式
        isAiSolveMode = intent.getBooleanExtra("ai_solve_mode", false)
        
        setupUI()
        observeViewModel()
        
        // 檢查權限
        if (allPermissionsGranted()) {
            startCamera()
        } else {
            ActivityCompat.requestPermissions(this, REQUIRED_PERMISSIONS, REQUEST_CODE_PERMISSIONS)
        }
        
        cameraExecutor = Executors.newSingleThreadExecutor()
    }
    
    private fun setupUI() {
        // 設置全螢幕模式
        setupFullscreen()
        
        // 設置按鈕點擊事件
        setupClickListeners()
        
        // 根據模式調整 UI
        if (isAiSolveMode) {
            // AI 求解模式 - 顯示 AI 指示器
            binding.ivAiIndicator.visibility = View.VISIBLE
        } else {
            // 普通拍攝模式
            binding.ivAiIndicator.visibility = View.GONE
        }
    }
    
    private fun setupFullscreen() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            window.insetsController?.let { controller ->
                controller.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
                controller.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            }
        } else {
            @Suppress("DEPRECATION")
            window.decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_FULLSCREEN
                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            )
        }
    }
    
    private fun setupClickListeners() {
        // 返回按鈕
        binding.btnBack.setOnClickListener {
            finish()
        }
        
        // 拍攝按鈕
        binding.btnCapture.setOnClickListener {
            takePhoto()
        }
        
        // 相簿按鈕
        binding.btnGallery?.setOnClickListener {
            openGallery()
        }
        
        // 注意：閃光燈和設定按鈕在當前布局中不存在
        // 如需要可以在布局中添加這些按鈕
    }
    
    private fun observeViewModel() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                // 觀察 UI 狀態
                launch {
                    viewModel.uiState.collect { uiState ->
                        updateUI(uiState)
                    }
                }
                
                // 觀察對焦狀態
                launch {
                    viewModel.focusState.collect { focusState ->
                        updateFocusIndicator(focusState)
                    }
                }
                
                // 觀察拍攝狀態
                launch {
                    viewModel.captureState.collect { captureState ->
                        updateCaptureButton(captureState)
                    }
                }
                
                // 觀察文檔檢測狀態
                launch {
                    viewModel.documentDetectionState.collect { detectionResult ->
                        updateDocumentOverlay(detectionResult)
                    }
                }
                
                // 觀察事件
                launch {
                    viewModel.events.collect { event ->
                        handleEvent(event)
                    }
                }
            }
        }
    }
    
    private fun updateUI(uiState: CameraUiState) {
        // 更新載入狀態 (當前布局中沒有 progressBar，可以用對焦指示器代替)
        // binding.progressBar?.visibility = if (uiState.isLoading) View.VISIBLE else View.GONE
        
        // 顯示錯誤訊息
        uiState.errorMessage?.let { message ->
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
            viewModel.clearError()
        }
    }
    
    private fun updateFocusIndicator(focusState: FocusState) {
        binding.tvFocusIndicator.let { indicator ->
            when (focusState) {
                FocusState.FOCUSED -> {
                    indicator.visibility = View.VISIBLE
                    indicator.setBackgroundResource(R.drawable.focus_indicator_capturing)
                }
                FocusState.TOO_CLOSE -> {
                    indicator.visibility = View.VISIBLE
                    indicator.setBackgroundResource(R.drawable.focus_indicator_too_close)
                }
                FocusState.TOO_FAR -> {
                    indicator.visibility = View.VISIBLE
                    indicator.setBackgroundResource(R.drawable.focus_indicator_too_far)
                }
                FocusState.UNFOCUSED -> {
                    indicator.visibility = View.GONE
                }
                FocusState.CAPTURING -> {
                    indicator.visibility = View.VISIBLE
                    indicator.setBackgroundResource(R.drawable.focus_indicator_capturing)
                }
            }
        }
    }
    
    private fun updateCaptureButton(captureState: CaptureState) {
        binding.btnCapture.isEnabled = captureState != CaptureState.CAPTURING
        
        when (captureState) {
            CaptureState.CAPTURING -> {
                binding.btnCapture.alpha = 0.5f
            }
            else -> {
                binding.btnCapture.alpha = 1.0f
            }
        }
    }
    
    private fun updateDocumentOverlay(detectionResult: DocumentDetectionResult) {
        // 當前布局中沒有 documentOverlay，可以在需要時添加
        // 這裡可以用其他方式顯示文檔檢測狀態，比如更新對焦指示器的文字
        when (detectionResult.state) {
            DocumentDetectionState.DETECTED -> {
                binding.tvFocusIndicator.text = "文檔已檢測到"
                binding.tvFocusIndicator.visibility = View.VISIBLE
            }
            DocumentDetectionState.DETECTING -> {
                binding.tvFocusIndicator.text = "正在檢測文檔..."
                binding.tvFocusIndicator.visibility = View.VISIBLE
            }
            DocumentDetectionState.NOT_DETECTED, DocumentDetectionState.ERROR -> {
                binding.tvFocusIndicator.visibility = View.GONE
            }
            else -> {
                binding.tvFocusIndicator.visibility = View.GONE
            }
        }
    }
    
    private fun handleEvent(event: CameraEvent) {
        when (event) {
            is CameraEvent.CaptureSuccess -> {
                onImageCaptured(event.imagePath)
            }
            is CameraEvent.CaptureError -> {
                Toast.makeText(this, "拍攝失敗: ${event.message}", Toast.LENGTH_SHORT).show()
            }
            is CameraEvent.DocumentBoundariesDetected -> {
                Log.d(TAG, "檢測到文檔邊界: ${event.boundaries.size} 個點")
            }
            is CameraEvent.DocumentDetectionFailed -> {
                Log.w(TAG, "文檔檢測失敗: ${event.message}")
            }
            else -> {
                Log.d(TAG, "處理事件: $event")
            }
        }
    }
    
    private fun startCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)
        
        cameraProviderFuture.addListener({
            cameraProvider = cameraProviderFuture.get()
            
            // 預覽
            val preview = Preview.Builder().build().also {
                it.setSurfaceProvider(binding.viewFinder.surfaceProvider)
            }
            
            // 圖像捕獲
            imageCapture = ImageCapture.Builder()
                .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
                .build()
            
            // 圖像分析
            val imageAnalyzer = ImageAnalysis.Builder()
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .build()
                .also {
                    it.setAnalyzer(cameraExecutor) { imageProxy ->
                        viewModel.analyzeImage(imageProxy)
                        imageProxy.close()
                    }
                }
            
            // 選擇後置相機
            val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA
            
            try {
                // 解綁所有用例
                cameraProvider?.unbindAll()
                
                // 綁定用例到相機
                camera = cameraProvider?.bindToLifecycle(
                    this, cameraSelector, preview, imageCapture, imageAnalyzer
                )
                
            } catch (exc: Exception) {
                Log.e(TAG, "用例綁定失敗", exc)
                Toast.makeText(this, "相機啟動失敗", Toast.LENGTH_SHORT).show()
            }
            
        }, ContextCompat.getMainExecutor(this))
    }
    
    private fun takePhoto() {
        val imageCapture = imageCapture ?: return
        
        // 創建輸出文件
        val photoFile = File(
            getOutputDirectory(),
            SimpleDateFormat(FILENAME_FORMAT, Locale.getDefault()).format(System.currentTimeMillis()) + ".jpg"
        )
        
        val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()
        
        // 使用 ViewModel 拍攝照片
        viewModel.captureImage(imageCapture, outputOptions)
    }
    
    private fun onImageCaptured(imagePath: String) {
        if (isAiSolveMode) {
            // AI 求解模式：直接跳轉到 AI 求解頁面
            val intent = Intent(this, AiSolveActivity::class.java).apply {
                putExtra("image_path", imagePath)
            }
            startActivity(intent)
            finish()
        } else if (isReturnImageMode) {
            // 返回圖片模式：返回圖片路徑
            val resultIntent = Intent().apply {
                putExtra("image_path", imagePath)
            }
            setResult(RESULT_OK, resultIntent)
            finish()
        } else {
            // 一般模式：跳轉到照片編輯頁面
            val intent = Intent(this, PhotoEditActivity::class.java).apply {
                putExtra("image_path", imagePath)
            }
            startActivityForResult(intent, REQUEST_PHOTO_EDIT)
        }
    }
    
    private fun openGallery() {
        try {
            val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
            intent.type = "image/*"
            startActivityForResult(intent, REQUEST_GALLERY_PICK)
            Log.d(TAG, "開啟圖庫選擇相片")
        } catch (e: Exception) {
            Log.e(TAG, "開啟圖庫失敗", e)
            Toast.makeText(this, "無法開啟圖庫", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 處理從圖庫選擇的圖片
     */
    private fun handleGalleryResult(data: Intent) {
        try {
            val selectedImageUri = data.data
            if (selectedImageUri != null) {
                Log.d(TAG, "從圖庫選擇的圖片URI: $selectedImageUri")

                // 將選擇的圖片複製到應用的臨時目錄
                val inputStream = contentResolver.openInputStream(selectedImageUri)
                if (inputStream != null) {
                    val photoFile = File(
                        getOutputDirectory(),
                        "gallery_" + SimpleDateFormat(FILENAME_FORMAT, Locale.US).format(System.currentTimeMillis()) + ".jpg"
                    )

                    photoFile.outputStream().use { outputStream ->
                        inputStream.copyTo(outputStream)
                    }
                    inputStream.close()

                    Log.d(TAG, "圖片已複製到: ${photoFile.absolutePath}")

                    // 根據AI模式決定跳轉到哪個界面
                    if (isAiSolveMode) {
                        // AI求解模式：跳轉到AI求解界面
                        val intent = Intent(this, AiSolveActivity::class.java)
                        intent.putExtra("image_path", photoFile.absolutePath)
                        startActivity(intent)
                        Log.d(TAG, "AI模式：從圖庫跳轉到AiSolveActivity")
                    } else {
                        // 普通模式：跳轉到照片編輯界面
                        val intent = Intent(this, PhotoEditActivity::class.java)
                        intent.putExtra("image_path", photoFile.absolutePath)

                        // 如果是返回圖片模式，傳遞參數
                        if (isReturnImageMode) {
                            intent.putExtra("return_image", true)
                            startActivityForResult(intent, REQUEST_PHOTO_EDIT)
                        } else {
                            startActivity(intent)
                        }
                        Log.d(TAG, "普通模式：從圖庫跳轉到PhotoEditActivity")
                    }

                } else {
                    Log.e(TAG, "無法開啟選擇的圖片")
                    Toast.makeText(this, "無法讀取選擇的圖片", Toast.LENGTH_SHORT).show()
                }
            } else {
                Log.e(TAG, "選擇的圖片URI為空")
                Toast.makeText(this, "未選擇有效的圖片", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "處理圖庫選擇結果失敗", e)
            Toast.makeText(this, "處理選擇的圖片時發生錯誤", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun toggleFlash() {
        camera?.let { camera ->
            val currentFlashMode = camera.cameraInfo.torchState.value
            val newFlashMode = currentFlashMode != TorchState.ON
            camera.cameraControl.enableTorch(newFlashMode)
            
            // 更新 UI (當前布局中沒有 btnFlash)
            // binding.btnFlash?.setImageResource(
            //     if (newFlashMode) android.R.drawable.ic_menu_camera else android.R.drawable.ic_menu_gallery
            // )
        }
    }
    
    private fun showSettingsMenu() {
        // 顯示設定選項，例如切換文檔檢測、對焦分析等
        viewModel.toggleDocumentDetection()
        Toast.makeText(this, "已切換文檔檢測設定", Toast.LENGTH_SHORT).show()
    }
    
    private fun getOutputDirectory(): File {
        val mediaDir = externalMediaDirs.firstOrNull()?.let {
            File(it, resources.getString(R.string.app_name)).apply { mkdirs() }
        }
        return if (mediaDir != null && mediaDir.exists()) mediaDir else filesDir
    }
    
    private fun allPermissionsGranted() = REQUIRED_PERMISSIONS.all {
        ContextCompat.checkSelfPermission(baseContext, it) == PackageManager.PERMISSION_GRANTED
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<String>, grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == REQUEST_CODE_PERMISSIONS) {
            if (allPermissionsGranted()) {
                startCamera()
            } else {
                Toast.makeText(this, "需要相機權限才能使用此功能", Toast.LENGTH_SHORT).show()
                finish()
            }
        }
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        when (requestCode) {
            REQUEST_PHOTO_EDIT -> {
                if (resultCode == RESULT_OK) {
                    finish()
                }
            }
            REQUEST_GALLERY_PICK -> {
                if (resultCode == RESULT_OK && data != null) {
                    handleGalleryResult(data)
                }
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        cameraExecutor.shutdown()
    }
}