package com.erroranalysis.app.ui.study

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.erroranalysis.app.data.IDeckRepository
import com.erroranalysis.app.di.ServiceLocator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

import android.net.Uri
import androidx.core.content.FileProvider

class DeckDetailViewModel(application: Application, private val deckId: String) : AndroidViewModel(application) {

    private val deckRepository: IDeckRepository = ServiceLocator.provideDeckRepository(application)

    private val _cards = MutableLiveData<List<StudyCard>>()
    val cards: LiveData<List<StudyCard>> = _cards

    private val _filteredCards = MutableLiveData<List<StudyCard>>()
    val filteredCards: LiveData<List<StudyCard>> = _filteredCards

    private val _shareResult = MutableLiveData<Result<Uri>>()
    val shareResult: LiveData<Result<Uri>> = _shareResult

    private var allCards: List<StudyCard> = emptyList()
    internal var currentFilter = CardFilter()
    private var currentSortType = CardSortType.CREATED_TIME_DESC

    init {
        loadCards()
    }

    fun shareDeck() {
        viewModelScope.launch {
            val result = deckRepository.shareDeck(deckId)
            result.onSuccess {
                val uri = FileProvider.getUriForFile(getApplication(), "${getApplication<Application>().packageName}.fileprovider", it)
                _shareResult.postValue(Result.success(uri))
            }.onFailure {
                _shareResult.postValue(Result.failure(it))
            }
        }
    }


    fun loadCards() {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                allCards = deckRepository.loadCardsByDeckId(deckId)
                applyFilter()
            }
        }
    }

    fun applyFilter(filter: CardFilter = currentFilter, sortType: CardSortType = currentSortType) {
        currentFilter = filter
        currentSortType = sortType

        val filteredList = if (currentFilter.hasFilter()) {
            allCards.filter { currentFilter.matches(it) }
        } else {
            allCards
        }

        val sortedList = when (currentSortType) {
            CardSortType.CREATED_TIME_DESC -> filteredList.sortedByDescending { it.createdTime }
            CardSortType.CREATED_TIME_ASC -> filteredList.sortedBy { it.createdTime }
            CardSortType.STARRED_FIRST -> filteredList.sortedWith(compareByDescending<StudyCard> { it.isStarred }.thenByDescending { it.createdTime })
            CardSortType.MASTERY_DESC -> filteredList.sortedByDescending { it.masteryLevel }
            CardSortType.MASTERY_ASC -> filteredList.sortedBy { it.masteryLevel }
            CardSortType.REVIEW_COUNT_DESC -> filteredList.sortedByDescending { it.reviewCount }
            CardSortType.REVIEW_COUNT_ASC -> filteredList.sortedBy { it.reviewCount }
        }
        _cards.postValue(allCards) // 設置所有卡片
        _filteredCards.postValue(sortedList)
    }

    // TODO: 將 DeckDetailActivity 中的其他卡片操作方法（addCard, updateCard, deleteCard, moveCard, toggleCardStar）遷移到這裡
    fun addCard(card: StudyCard) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                deckRepository.addCard(card)
                loadCards() // Refresh cards after adding
            }
        }
    }

    fun updateCard(updatedCard: StudyCard) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                deckRepository.updateCard(updatedCard)
                loadCards() // Refresh cards after updating
            }
        }
    }

    fun deleteCard(cardId: String) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                deckRepository.deleteCard(cardId)
                loadCards() // Refresh cards after deleting
            }
        }
    }

    fun moveCard(cardId: String, targetDeckId: String) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                deckRepository.moveCard(cardId, targetDeckId)
                loadCards() // Refresh cards after moving
            }
        }
    }

    fun toggleCardStar(cardId: String) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                deckRepository.toggleCardStar(cardId)
                loadCards() // Refresh cards after toggling star
            }
        }
    }

    fun loadAllDecks(): List<SimpleDeck> {
        return deckRepository.loadDecks()
    }
}
