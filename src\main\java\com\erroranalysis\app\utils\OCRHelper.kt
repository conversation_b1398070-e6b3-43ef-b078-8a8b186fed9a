package com.erroranalysis.app.utils

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.chinese.ChineseTextRecognizerOptions
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import com.google.android.gms.tasks.Tasks
import java.util.concurrent.TimeUnit

/**
 * OCR文字識別工具類
 * 支援中英文混合識別，特別優化數學符號識別
 */
class OCRHelper(private val context: Context) {
    
    companion object {
        private const val TAG = "OCRHelper"
    }
    
    // 中文文字識別器
    private val chineseRecognizer = TextRecognition.getClient(ChineseTextRecognizerOptions.Builder().build())
    
    // 英文文字識別器（對數學符號支援較好）
    private val latinRecognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)
    
    /**
     * 執行OCR文字識別
     * @param bitmap 要識別的圖片
     * @return 識別結果文字
     */
    fun recognizeText(bitmap: Bitmap): String {
        return try {
            Log.d(TAG, "開始OCR識別，圖片尺寸: ${bitmap.width}x${bitmap.height}")

            val inputImage = InputImage.fromBitmap(bitmap, 0)

            // 同時使用中文和英文識別器
            val chineseResult = recognizeWithChineseRecognizer(inputImage)
            val latinResult = recognizeWithLatinRecognizer(inputImage)

            // 合併結果，選擇較好的識別結果
            val finalResult = combineResults(chineseResult, latinResult)

            Log.d(TAG, "OCR識別完成，結果長度: ${finalResult.length}")
            Log.d(TAG, "識別結果: $finalResult")

            finalResult

        } catch (e: Exception) {
            Log.e(TAG, "OCR識別失敗", e)
            "OCR識別失敗：${e.message}"
        }
    }
    
    /**
     * 使用中文識別器
     */
    private fun recognizeWithChineseRecognizer(inputImage: InputImage): String {
        return try {
            val result = Tasks.await(chineseRecognizer.process(inputImage), 30, TimeUnit.SECONDS)
            result.text
        } catch (e: Exception) {
            Log.w(TAG, "中文識別器失敗", e)
            ""
        }
    }

    /**
     * 使用英文識別器（對數學符號較好）
     */
    private fun recognizeWithLatinRecognizer(inputImage: InputImage): String {
        return try {
            val result = Tasks.await(latinRecognizer.process(inputImage), 30, TimeUnit.SECONDS)
            result.text
        } catch (e: Exception) {
            Log.w(TAG, "英文識別器失敗", e)
            ""
        }
    }
    
    /**
     * 合併兩個識別結果，選擇較好的
     */
    private fun combineResults(chineseResult: String, latinResult: String): String {
        // 如果其中一個為空，返回另一個
        if (chineseResult.isEmpty()) return latinResult
        if (latinResult.isEmpty()) return chineseResult
        
        // 如果兩個結果都有內容，選擇較長的（通常識別更完整）
        return if (chineseResult.length >= latinResult.length) {
            chineseResult
        } else {
            latinResult
        }
    }
    
    /**
     * 預處理圖片以提高OCR準確度
     */
    fun preprocessImage(bitmap: Bitmap): Bitmap {
        // 這裡可以添加圖片預處理邏輯
        // 例如：調整對比度、銳化、去噪等
        // 目前先返回原圖
        return bitmap
    }
    
    /**
     * 後處理識別結果
     */
    fun postprocessText(text: String): String {
        return text
            .trim()
            .replace(Regex("\\s+"), " ") // 合併多個空格
            .replace(Regex("\\n+"), "\n") // 合併多個換行
    }
    
    /**
     * 釋放資源
     */
    fun release() {
        try {
            chineseRecognizer.close()
            latinRecognizer.close()
        } catch (e: Exception) {
            Log.w(TAG, "釋放OCR資源時出錯", e)
        }
    }
}
