package com.erroranalysis.app.utils

import android.content.Context
import android.util.Log
import org.opencv.android.OpenCVLoader

/**
 * OpenCV 初始化工具類
 * 基於 app_refactor 的成功實現
 */
object OpenCVInitializer {

    private const val TAG = "OpenCVInitializer"
    private var isInitialized = false
    private var initializationCallbacks = mutableListOf<(Boolean) -> Unit>()

    /**
     * 初始化 OpenCV
     * @param context 應用上下文
     * @param callback 初始化完成回調
     */
    fun initialize(context: Context, callback: (Boolean) -> Unit) {
        if (isInitialized) {
            callback(true)
            return
        }

        initializationCallbacks.add(callback)

        // 使用本地 OpenCV 庫初始化（推薦方法）
        try {
            if (OpenCVLoader.initLocal()) {
                Log.d(TAG, "✅ OpenCV library loaded successfully from local module")
                isInitialized = true
                notifyCallbacks(true)
            } else {
                Log.e(TAG, "❌ OpenCV local initialization failed")
                notifyCallbacks(false)
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ OpenCV initialization error: ${e.message}", e)
            notifyCallbacks(false)
        }
    }

    /**
     * 初始化 OpenCV (兼容舊版本調用)
     */
    fun initializeOpenCV(context: Context, callback: (Boolean) -> Unit) {
        initialize(context, callback)
    }

    private fun notifyCallbacks(success: Boolean) {
        initializationCallbacks.forEach { it(success) }
        initializationCallbacks.clear()
    }

    /**
     * 檢查 OpenCV 是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized
}