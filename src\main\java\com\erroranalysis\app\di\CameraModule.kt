package com.erroranalysis.app.di

import android.content.Context
import com.erroranalysis.app.data.camera.CameraRepository
import com.erroranalysis.app.data.camera.ICameraRepository
import com.erroranalysis.app.domain.camera.usecase.*

/**
 * 相機模組的依賴注入
 * 提供相機相關的依賴
 */
object CameraModule {
    
    fun provideCameraRepository(context: Context): ICameraRepository {
        return CameraRepository(context)
    }
    
    fun provideCameraUseCases(repository: ICameraRepository): CameraUseCases {
        return CameraUseCases(
            getFocusState = GetFocusStateUseCase(repository),
            getCaptureState = GetCaptureStateUseCase(repository),
            getDocumentDetectionState = GetDocumentDetectionStateUseCase(repository),
            captureImage = CaptureImageUseCase(repository),
            analyzeImage = AnalyzeImageUseCase(repository),
            detectDocumentBoundaries = DetectDocumentBoundariesUseCase(repository),
            correctPerspective = CorrectPerspectiveUseCase(repository),
            processDocument = ProcessDocumentUseCase(repository),
            performOCR = PerformOCRUseCase(repository),
            solveWithAI = SolveWithAIUseCase(repository),
            saveImageToGallery = SaveImageToGalleryUseCase(repository),
            deleteImage = DeleteImageUseCase(repository),
            getImageInfo = GetImageInfoUseCase(repository),
            getCameraSettings = GetCameraSettingsUseCase(repository),
            updateCameraSettings = UpdateCameraSettingsUseCase(repository)
        )
    }
}