package com.erroranalysis.app.ui.theme

import android.graphics.Color

/**
 * 應用主題數據類
 */
data class AppTheme(
    val id: String,
    val name: String,
    val description: String,
    val primaryColor: String,
    val primaryDarkColor: String,
    val accentColor: String,
    val gradientStart: String,
    val gradientEnd: String,
    val backgroundColor: String,
    val cardBackgroundColor: String,
    val emoji: String
) {
    /**
     * 獲取主色調的Color值
     */
    fun getPrimaryColorInt(): Int = Color.parseColor(primaryColor)
    
    /**
     * 獲取深色主色調的Color值
     */
    fun getPrimaryDarkColorInt(): Int = Color.parseColor(primaryDarkColor)
    
    /**
     * 獲取強調色的Color值
     */
    fun getAccentColorInt(): Int = Color.parseColor(accentColor)
    
    /**
     * 獲取漸變起始色的Color值
     */
    fun getGradientStartColorInt(): Int = Color.parseColor(gradientStart)
    
    /**
     * 獲取漸變結束色的Color值
     */
    fun getGradientEndColorInt(): Int = Color.parseColor(gradientEnd)

    /**
     * 獲取背景色的Color值
     */
    fun getBackgroundColorInt(): Int = Color.parseColor(backgroundColor)

    /**
     * 獲取卡片背景色的Color值
     */
    fun getCardBackgroundColorInt(): Int = Color.parseColor(cardBackgroundColor)
}

/**
 * 預定義主題集合
 */
object ThemeCollection {
    
    val defaultTheme = AppTheme(
        id = "default",
        name = "經典藍",
        description = "沉穩專業的藍色主題",
        primaryColor = "#667eea",
        primaryDarkColor = "#5a6fd8",
        accentColor = "#764ba2",
        gradientStart = "#667eea",
        gradientEnd = "#764ba2",
        backgroundColor = "#e8efff",
        cardBackgroundColor = "#f8fbff",
        emoji = "💙"
    )
    
    val sunsetTheme = AppTheme(
        id = "sunset",
        name = "日落橙",
        description = "溫暖活力的橙色主題",
        primaryColor = "#ff6b6b",
        primaryDarkColor = "#ee5a52",
        accentColor = "#ffa726",
        gradientStart = "#ff6b6b",
        gradientEnd = "#ffa726",
        backgroundColor = "#ffebeb",
        cardBackgroundColor = "#fff5f5",
        emoji = "🧡"
    )

    val forestTheme = AppTheme(
        id = "forest",
        name = "森林綠",
        description = "清新自然的綠色主題",
        primaryColor = "#4ecdc4",
        primaryDarkColor = "#45b7aa",
        accentColor = "#26de81",
        gradientStart = "#4ecdc4",
        gradientEnd = "#26de81",
        backgroundColor = "#e8fff0",
        cardBackgroundColor = "#f4fff8",
        emoji = "💚"
    )

    val lavenderTheme = AppTheme(
        id = "lavender",
        name = "薰衣草紫",
        description = "優雅夢幻的紫色主題",
        primaryColor = "#9c88ff",
        primaryDarkColor = "#7c4dff",
        accentColor = "#b39ddb",
        gradientStart = "#9c88ff",
        gradientEnd = "#b39ddb",
        backgroundColor = "#ede8ff",
        cardBackgroundColor = "#f5f2ff",
        emoji = "💜"
    )
    
    val cherryTheme = AppTheme(
        id = "cherry",
        name = "櫻花粉",
        description = "甜美可愛的粉色主題",
        primaryColor = "#ff9a9e",
        primaryDarkColor = "#f687b3",
        accentColor = "#fecfef",
        gradientStart = "#ff9a9e",
        gradientEnd = "#fecfef",
        backgroundColor = "#ffe8f0",
        cardBackgroundColor = "#fff2f7",
        emoji = "🌸"
    )

    val shibaTheme = AppTheme(
        id = "shiba",
        name = "柴柴醬",
        description = "溫暖可愛的咖啡色系",
        primaryColor = "#8d6e63",
        primaryDarkColor = "#5d4037",
        accentColor = "#a1887f",
        gradientStart = "#8d6e63",
        gradientEnd = "#a1887f",
        backgroundColor = "#f5f2ed",
        cardBackgroundColor = "#faf7f2",
        emoji = "🐕"
    )

    val goldenTheme = AppTheme(
        id = "golden",
        name = "黃金時光",
        description = "奢華溫暖的金色主題",
        primaryColor = "#f7b733",
        primaryDarkColor = "#fc4a1a",
        accentColor = "#f7b733",
        gradientStart = "#f7b733",
        gradientEnd = "#fc4a1a",
        backgroundColor = "#fff5e6",
        cardBackgroundColor = "#fffaf2",
        emoji = "✨"
    )

    val mintTheme = AppTheme(
        id = "mint",
        name = "薄荷清新",
        description = "清涼舒適的薄荷主題",
        primaryColor = "#00d2ff",
        primaryDarkColor = "#3a7bd5",
        accentColor = "#00d2ff",
        gradientStart = "#00d2ff",
        gradientEnd = "#3a7bd5",
        backgroundColor = "#e8ffff",
        cardBackgroundColor = "#f2ffff",
        emoji = "🍃"
    )
    
    /**
     * 獲取所有可用主題
     */
    fun getAllThemes(): List<AppTheme> {
        return listOf(
            defaultTheme,
            sunsetTheme,
            forestTheme,
            lavenderTheme,
            cherryTheme,
            shibaTheme,
            goldenTheme,
            mintTheme
        )
    }
    
    /**
     * 根據ID獲取主題
     */
    fun getThemeById(id: String): AppTheme {
        return getAllThemes().find { it.id == id } ?: defaultTheme
    }
}
