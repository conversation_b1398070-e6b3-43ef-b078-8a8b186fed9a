Sapp_camera_refactor/src/main/java/com/erroranalysis/app/ErrorAnalysisApplication.ktOapp_camera_refactor/src/main/java/com/erroranalysis/app/data/DeckDataManager.ktOapp_camera_refactor/src/main/java/com/erroranalysis/app/data/IDeckRepository.ktWapp_camera_refactor/src/main/java/com/erroranalysis/app/data/camera/CameraRepository.ktXapp_camera_refactor/src/main/java/com/erroranalysis/app/data/camera/ICameraRepository.ktJapp_camera_refactor/src/main/java/com/erroranalysis/app/di/CameraModule.ktLapp_camera_refactor/src/main/java/com/erroranalysis/app/di/ServiceLocator.kt[app_camera_refactor/src/main/java/com/erroranalysis/app/domain/camera/model/CameraModels.kt_app_camera_refactor/src/main/java/com/erroranalysis/app/domain/camera/usecase/CameraUseCases.ktQapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/base/ThemedActivity.ktTapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/AiSolveActivity.ktSapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CameraActivity.ktTapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CameraViewModel.kt[app_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CameraViewModelFactory.ktZapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CombinedImageAnalyzer.kt\app_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CropOverlayTestActivity.ktYapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CropSelectionOverlay.ktYapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/DeckSelectionAdapter.kt\app_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/DocumentBoundaryOverlay.kt^app_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/DocumentDetectionAnalyzer.ktRapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/FocusAnalyzer.ktVapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/PhotoEditActivity.ktYapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/SimpleCameraActivity.ktUapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/main/SimpleMainActivity.kt\app_camera_refactor/src/main/java/com/erroranalysis/app/ui/settings/BackupRestoreActivity.ktXapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/settings/FontOptionAdapter.ktWapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/settings/SettingsActivity.kt\app_camera_refactor/src/main/java/com/erroranalysis/app/ui/settings/adapters/ThemeAdapter.ktWapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/BatchImportActivity.ktTapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/CardEditActivity.ktNapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/CardFilter.ktVapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/CardViewerActivity.ktVapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailActivity.ktWapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailViewModel.kt^app_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailViewModelFactory.ktNapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckFilter.ktWapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/SimpleStudyActivity.ktMapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/StudyCard.ktMapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/StudyDeck.ktYapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/adapters/ColorAdapter.ktXapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/adapters/IconAdapter.kt^app_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/adapters/SimpleDeckAdapter.kt]app_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/adapters/StudyCardAdapter.ktaapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/test/DocumentCorrectionTestActivity.ktLapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/AppTheme.ktRapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/FontExtensions.ktOapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/FontManager.ktWapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/FontSelectorAdapter.ktVapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/FontSelectorDialog.ktPapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/ThemeManager.ktVapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/widgets/RichTextEditText.ktSapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/BatchImportManager.ktYapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/DocumentBoundaryDetector.ktRapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/DocumentProcessor.ktPapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/GeminiAIService.ktZapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/GridSpacingItemDecoration.ktTapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/ImageStorageManager.ktQapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/MarkdownRenderer.ktQapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/MathFormatHelper.ktJapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/OCRHelper.ktRapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/OpenCVInitializer.ktNapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/OpenCVManager.ktLapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/PdfExporter.ktUapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/PerspectiveCorrector.ktTapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/SimpleBackupManager.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      