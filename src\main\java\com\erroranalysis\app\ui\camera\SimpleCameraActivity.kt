package com.erroranalysis.app.ui.camera

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.erroranalysis.app.databinding.ActivityCameraBinding
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * 簡化的相機活動，用於測試基本相機功能
 * 移除了所有複雜的功能，只保留基本的相機預覽和拍照
 */
class SimpleCameraActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityCameraBinding
    private var imageCapture: ImageCapture? = null
    private lateinit var cameraExecutor: ExecutorService
    
    companion object {
        private const val TAG = "SimpleCameraActivity"
        private const val REQUEST_CODE_PERMISSIONS = 10
        private val REQUIRED_PERMISSIONS = arrayOf(Manifest.permission.CAMERA)
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCameraBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        Log.d(TAG, "SimpleCameraActivity onCreate")
        
        // 初始化執行器
        cameraExecutor = Executors.newSingleThreadExecutor()
        
        // 設置簡單的UI
        setupSimpleUI()
        
        // 檢查權限並啟動相機
        if (allPermissionsGranted()) {
            Log.d(TAG, "權限已授予，啟動相機")
            startSimpleCamera()
        } else {
            Log.d(TAG, "請求相機權限")
            ActivityCompat.requestPermissions(
                this, REQUIRED_PERMISSIONS, REQUEST_CODE_PERMISSIONS
            )
        }
    }
    
    private fun setupSimpleUI() {
        // 返回按鈕
        binding.btnBack.setOnClickListener {
            finish()
        }
        
        // 拍照按鈕
        binding.btnCapture.setOnClickListener {
            Log.d(TAG, "拍照按鈕被點擊")
            takePhoto()
        }
    }
    
    private fun startSimpleCamera() {
        Log.d(TAG, "開始啟動簡化相機")
        
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)
        
        cameraProviderFuture.addListener({
            try {
                val cameraProvider: ProcessCameraProvider = cameraProviderFuture.get()
                Log.d(TAG, "獲取相機提供者成功")
                
                // 創建預覽
                val preview = Preview.Builder()
                    .build()
                    .also {
                        Log.d(TAG, "設置預覽Surface Provider")
                        it.setSurfaceProvider(binding.viewFinder.surfaceProvider)
                    }
                
                // 創建圖像捕獲
                imageCapture = ImageCapture.Builder()
                    .build()
                
                // 選擇後置相機
                val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA
                
                try {
                    // 解綁所有用例
                    cameraProvider.unbindAll()
                    Log.d(TAG, "解綁所有相機用例")
                    
                    // 綁定用例到生命週期
                    val camera = cameraProvider.bindToLifecycle(
                        this, cameraSelector, preview, imageCapture
                    )
                    
                    Log.d(TAG, "相機綁定成功")
                    
                    // 顯示成功消息
                    runOnUiThread {
                        Toast.makeText(this, "相機啟動成功", Toast.LENGTH_SHORT).show()
                    }
                    
                } catch (exc: Exception) {
                    Log.e(TAG, "相機綁定失敗", exc)
                    runOnUiThread {
                        Toast.makeText(this, "相機綁定失敗: ${exc.message}", Toast.LENGTH_LONG).show()
                    }
                }
                
            } catch (exc: Exception) {
                Log.e(TAG, "獲取相機提供者失敗", exc)
                runOnUiThread {
                    Toast.makeText(this, "無法獲取相機: ${exc.message}", Toast.LENGTH_LONG).show()
                }
            }
            
        }, ContextCompat.getMainExecutor(this))
    }

    private fun takePhoto() {
        Log.d(TAG, "開始拍照")

        // 檢查imageCapture是否已初始化
        val imageCapture = imageCapture ?: run {
            Log.e(TAG, "拍照失敗：imageCapture未初始化")
            Toast.makeText(this, "相機未準備就緒", Toast.LENGTH_SHORT).show()
            return
        }

        // 創建輸出文件
        val photoFile = createPhotoFile()

        // 創建輸出選項
        val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()

        // 執行拍照
        imageCapture.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(this),
            object : ImageCapture.OnImageSavedCallback {
                override fun onError(exception: ImageCaptureException) {
                    Log.e(TAG, "拍照失敗", exception)
                    Toast.makeText(
                        this@SimpleCameraActivity,
                        "拍照失敗: ${exception.message}",
                        Toast.LENGTH_LONG
                    ).show()
                }

                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    val savedUri = output.savedUri ?: return
                    Log.d(TAG, "拍照成功: $savedUri")
                    Toast.makeText(
                        this@SimpleCameraActivity,
                        "照片已保存: ${photoFile.name}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        )
    }

    private fun createPhotoFile(): File {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val fileName = "ErrorAnalysis_$timeStamp.jpg"

        // 使用應用的外部文件目錄
        val photoDir = File(getExternalFilesDir(null), "photos")
        if (!photoDir.exists()) {
            photoDir.mkdirs()
        }

        val photoFile = File(photoDir, fileName)
        Log.d(TAG, "創建照片文件: ${photoFile.absolutePath}")
        return photoFile
    }

    private fun allPermissionsGranted() = REQUIRED_PERMISSIONS.all {
        ContextCompat.checkSelfPermission(baseContext, it) == PackageManager.PERMISSION_GRANTED
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<String>, grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == REQUEST_CODE_PERMISSIONS) {
            Log.d(TAG, "權限請求結果: ${grantResults.contentToString()}")
            if (allPermissionsGranted()) {
                Log.d(TAG, "相機權限已授予，啟動相機")
                startSimpleCamera()
            } else {
                Log.e(TAG, "相機權限被拒絕")
                Toast.makeText(this, "需要相機權限才能使用此功能", Toast.LENGTH_SHORT).show()
                finish()
            }
        }
    }
    
    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume")
    }
    
    override fun onPause() {
        super.onPause()
        Log.d(TAG, "onPause")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy - 關閉相機執行器")
        cameraExecutor.shutdown()
    }
}
