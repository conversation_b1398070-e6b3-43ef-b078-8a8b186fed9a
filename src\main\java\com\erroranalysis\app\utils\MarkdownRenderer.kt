package com.erroranalysis.app.utils

import android.content.Context
import android.graphics.Typeface
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.StyleSpan
import android.text.style.TypefaceSpan
import android.widget.TextView
import io.noties.markwon.Markwon
import io.noties.markwon.html.HtmlPlugin

/**
 * Markdown渲染工具類
 * 支援LaTeX數學公式渲染
 */
class MarkdownRenderer(private val context: Context) {

    private val markwon: Markwon by lazy {
        Markwon.builder(context)
            .usePlugin(HtmlPlugin.create())
            .build()
    }

    /**
     * 渲染Markdown文本到TextView
     */
    fun renderToTextView(textView: TextView, markdown: String) {
        try {
            // 預處理Markdown文本
            val processedMarkdown = preprocessMarkdown(markdown)

            // 檢查是否包含數學公式
            if (MarkdownRenderer.containsLatex(processedMarkdown)) {
                // 使用自定義數學公式渲染
                renderWithMath(textView, processedMarkdown)
            } else {
                // 使用標準Markdown渲染
                markwon.setMarkdown(textView, processedMarkdown)
            }
        } catch (e: Exception) {
            // 如果渲染失敗，使用簡化渲染
            renderSimple(textView, markdown)
        }
    }

    /**
     * 預處理Markdown文本
     * 處理特殊格式和LaTeX公式
     */
    private fun preprocessMarkdown(markdown: String): String {
        var processed = markdown

        // 處理LaTeX公式：將 $...$ 轉換為 $$...$$ (行內公式)
        processed = processed.replace(Regex("\\$([^$]+)\\$")) { matchResult ->
            "$$${matchResult.groupValues[1]}$$"
        }

        // 處理LaTeX公式：保持 $$...$$ (塊級公式)
        // 已經是正確格式，不需要處理

        // 處理特殊字符
        processed = processed.replace("\\\\", "\n") // 處理換行
        processed = processed.replace("\\n", "\n")   // 處理換行

        // 處理數學符號
        processed = processed.replace("\\times", "×")
        processed = processed.replace("\\div", "÷")
        processed = processed.replace("\\pm", "±")
        processed = processed.replace("\\mp", "∓")
        processed = processed.replace("\\leq", "≤")
        processed = processed.replace("\\geq", "≥")
        processed = processed.replace("\\neq", "≠")
        processed = processed.replace("\\approx", "≈")
        processed = processed.replace("\\infty", "∞")

        // 處理希臘字母
        processed = processed.replace("\\alpha", "α")
        processed = processed.replace("\\beta", "β")
        processed = processed.replace("\\gamma", "γ")
        processed = processed.replace("\\delta", "δ")
        processed = processed.replace("\\epsilon", "ε")
        processed = processed.replace("\\theta", "θ")
        processed = processed.replace("\\lambda", "λ")
        processed = processed.replace("\\mu", "μ")
        processed = processed.replace("\\pi", "π")
        processed = processed.replace("\\sigma", "σ")
        processed = processed.replace("\\phi", "φ")
        processed = processed.replace("\\omega", "ω")

        return processed
    }

    /**
     * 渲染包含數學公式的文本
     */
    private fun renderWithMath(textView: TextView, markdown: String) {
        try {
            val spannableBuilder = SpannableStringBuilder()
            var lastIndex = 0

            // 處理 $$...$$（塊級公式）
            val blockMathRegex = Regex("\\$\\$([^$]+)\\$\\$")
            val blockMatches = blockMathRegex.findAll(markdown)

            for (match in blockMatches) {
                // 添加公式前的文本
                if (match.range.first > lastIndex) {
                    val beforeText = markdown.substring(lastIndex, match.range.first)
                    spannableBuilder.append(processBasicMarkdown(beforeText))
                }

                // 添加數學公式（使用特殊格式）
                val mathContent = match.groupValues[1]
                val formattedMath = formatMathExpression(mathContent)
                val start = spannableBuilder.length
                spannableBuilder.append("\n")
                spannableBuilder.append(formattedMath)
                spannableBuilder.append("\n")

                // 為數學公式添加樣式
                spannableBuilder.setSpan(
                    TypefaceSpan("monospace"),
                    start + 1,
                    spannableBuilder.length - 1,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )

                lastIndex = match.range.last + 1
            }

            // 處理 $...$ (行內公式)
            val inlineMathRegex = Regex("\\$([^$]+)\\$")
            var workingText = if (lastIndex < markdown.length) {
                markdown.substring(lastIndex)
            } else {
                ""
            }

            if (workingText.isNotEmpty()) {
                val inlineMatches = inlineMathRegex.findAll(workingText)
                var inlineLastIndex = 0

                for (match in inlineMatches) {
                    // 添加公式前的文本
                    if (match.range.first > inlineLastIndex) {
                        val beforeText = workingText.substring(inlineLastIndex, match.range.first)
                        spannableBuilder.append(processBasicMarkdown(beforeText))
                    }

                    // 添加行內數學公式
                    val mathContent = match.groupValues[1]
                    val formattedMath = formatMathExpression(mathContent)
                    val start = spannableBuilder.length
                    spannableBuilder.append(formattedMath)

                    // 為行內公式添加樣式
                    spannableBuilder.setSpan(
                        TypefaceSpan("monospace"),
                        start,
                        spannableBuilder.length,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )

                    inlineLastIndex = match.range.last + 1
                }

                // 添加剩餘文本
                if (inlineLastIndex < workingText.length) {
                    val remainingText = workingText.substring(inlineLastIndex)
                    spannableBuilder.append(processBasicMarkdown(remainingText))
                }
            }

            textView.text = spannableBuilder

        } catch (e: Exception) {
            // 備用方案
            renderSimple(textView, markdown)
        }
    }

    /**
     * 格式化數學表達式
     */
    private fun formatMathExpression(mathContent: String): String {
        var formatted = mathContent

        // 處理分數 \frac{a}{b}
        formatted = formatted.replace(Regex("\\\\frac\\{([^}]+)\\}\\{([^}]+)\\}")) { match ->
            "(${match.groupValues[1]})/(${match.groupValues[2]})"
        }

        // 處理平方根 \sqrt{x}
        formatted = formatted.replace(Regex("\\\\sqrt\\{([^}]+)\\}")) { match ->
            "√(${match.groupValues[1]})"
        }

        // 處理上標 x^{n}
        formatted = formatted.replace(Regex("([a-zA-Z0-9])\\^\\{([^}]+)\\}")) { match ->
            "${match.groupValues[1]}^${match.groupValues[2]}"
        }

        // 處理下標 x_{n}
        formatted = formatted.replace(Regex("([a-zA-Z0-9])_\\{([^}]+)\\}")) { match ->
            "${match.groupValues[1]}_${match.groupValues[2]}"
        }

        // 處理求和 \sum
        formatted = formatted.replace("\\sum", "Σ")

        // 處理積分 \int
        formatted = formatted.replace("\\int", "∫")

        // 處理極限 \lim
        formatted = formatted.replace("\\lim", "lim")

        return formatted
    }

    /**
     * 處理基本Markdown標記
     */
    private fun processBasicMarkdown(text: String): SpannableStringBuilder {
        val builder = SpannableStringBuilder(text)

        // 處理粗體 **text**
        val boldRegex = Regex("\\*\\*(.*?)\\*\\*")
        val boldMatches = boldRegex.findAll(text)
        var offset = 0

        for (match in boldMatches) {
            val start = match.range.first - offset
            val end = match.range.last + 1 - offset
            val content = match.groupValues[1]

            builder.replace(start, end, content)
            builder.setSpan(
                StyleSpan(Typeface.BOLD),
                start,
                start + content.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            offset += match.value.length - content.length
        }

        return builder
    }

    /**
     * 簡化版渲染（如果LaTeX渲染失敗時使用）
     */
    fun renderSimple(textView: TextView, markdown: String) {
        try {
            var processed = markdown

            // 移除Markdown標記
            processed = processed.replace(Regex("\\*\\*(.*?)\\*\\*"), "$1") // 粗體
            processed = processed.replace(Regex("\\*(.*?)\\*"), "$1")       // 斜體
            processed = processed.replace(Regex("`(.*?)`"), "$1")           // 代碼
            processed = processed.replace(Regex("#{1,6}\\s*(.*)"), "$1")    // 標題

            // 處理列表
            processed = processed.replace(Regex("^\\s*[-*+]\\s+(.*)$", RegexOption.MULTILINE), "• $1")
            processed = processed.replace(Regex("^\\s*\\d+\\.\\s+(.*)$", RegexOption.MULTILINE), "$1")

            // 處理LaTeX公式為簡單文本
            processed = processed.replace(Regex("\\$\\$([^$]+)\\$\\$")) { matchResult ->
                "[數學公式: ${matchResult.groupValues[1]}]"
            }
            processed = processed.replace(Regex("\\$([^$]+)\\$")) { matchResult ->
                matchResult.groupValues[1]
            }

            // 處理數學符號（同上）
            processed = processed.replace("\\times", "×")
            processed = processed.replace("\\div", "÷")
            processed = processed.replace("\\pm", "±")
            processed = processed.replace("\\leq", "≤")
            processed = processed.replace("\\geq", "≥")
            processed = processed.replace("\\neq", "≠")
            processed = processed.replace("\\approx", "≈")
            processed = processed.replace("\\infty", "∞")

            textView.text = processed

        } catch (e: Exception) {
            // 最後的備用方案
            textView.text = markdown
        }
    }

    companion object {
        /**
         * 檢查文本是否包含LaTeX公式
         */
        fun containsLatex(text: String): Boolean {
            return text.contains(Regex("\\$.*?\\$")) || 
                   text.contains(Regex("\\\\[a-zA-Z]+")) ||
                   text.contains("\\frac") ||
                   text.contains("\\sqrt") ||
                   text.contains("\\sum") ||
                   text.contains("\\int")
        }

        /**
         * 檢查文本是否包含Markdown標記
         */
        fun containsMarkdown(text: String): Boolean {
            return text.contains(Regex("\\*\\*.*?\\*\\*")) ||  // 粗體
                   text.contains(Regex("\\*.*?\\*")) ||        // 斜體
                   text.contains(Regex("`.*?`")) ||            // 代碼
                   text.contains(Regex("#{1,6}\\s+")) ||       // 標題
                   text.contains(Regex("^\\s*[-*+]\\s+", RegexOption.MULTILINE)) || // 列表
                   text.contains(Regex("^\\s*\\d+\\.\\s+", RegexOption.MULTILINE))  // 數字列表
        }
    }
}
