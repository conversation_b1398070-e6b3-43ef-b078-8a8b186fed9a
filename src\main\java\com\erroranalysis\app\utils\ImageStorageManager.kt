package com.erroranalysis.app.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.UUID

/**
 * 圖片存儲管理器
 * 負責保存和載入卡片中的圖片
 */
class ImageStorageManager(private val context: Context) {
    
    companion object {
        private const val IMAGES_DIR = "card_images"
        private const val IMAGE_QUALITY = 85 // JPEG壓縮品質
    }
    
    private val imagesDir: File by lazy {
        File(context.filesDir, IMAGES_DIR).apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }
    
    /**
     * 保存圖片到本地存儲
     * @param bitmap 要保存的圖片
     * @return 保存後的文件名，失敗返回null
     */
    fun saveImage(bitmap: Bitmap): String? {
        return try {
            val fileName = "${UUID.randomUUID()}.jpg"
            val file = File(imagesDir, fileName)
            
            FileOutputStream(file).use { outputStream ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, IMAGE_QUALITY, outputStream)
            }
            
            fileName
        } catch (e: IOException) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * 從本地存儲載入圖片（無緩存）
     * @param fileName 圖片文件名
     * @return 載入的圖片，失敗返回null
     */
    fun loadImageWithoutCache(fileName: String): Bitmap? {
        return try {
            if (fileName.isBlank()) {
                android.util.Log.w("ImageStorageManager", "圖片文件名為空")
                return null
            }

            val file = File(imagesDir, fileName)
            android.util.Log.d("ImageStorageManager", "嘗試無緩存載入圖片: ${file.absolutePath}")

            if (file.exists() && file.length() > 0) {
                // 使用BitmapFactory.Options禁用緩存
                val options = BitmapFactory.Options().apply {
                    inJustDecodeBounds = false
                    inPurgeable = true
                    inInputShareable = true
                    inTempStorage = ByteArray(32 * 1024)
                }

                val bitmap = BitmapFactory.decodeFile(file.absolutePath, options)
                if (bitmap != null) {
                    android.util.Log.d("ImageStorageManager", "無緩存圖片載入成功: ${bitmap.width}x${bitmap.height}")
                } else {
                    android.util.Log.w("ImageStorageManager", "無緩存圖片解碼失敗: $fileName")
                }
                bitmap
            } else {
                android.util.Log.w("ImageStorageManager", "圖片文件不存在或為空: ${file.absolutePath}")
                null
            }
        } catch (e: Exception) {
            android.util.Log.e("ImageStorageManager", "無緩存載入圖片失敗: $fileName", e)
            null
        }
    }

    /**
     * 從本地存儲載入圖片
     * @param fileName 圖片文件名
     * @return 載入的圖片，失敗返回null
     */
    fun loadImage(fileName: String): Bitmap? {
        return try {
            if (fileName.isBlank()) {
                android.util.Log.w("ImageStorageManager", "圖片文件名為空")
                return null
            }

            android.util.Log.d("ImageStorageManager", "嘗試載入圖片: $fileName")
            val file = File(imagesDir, fileName)
            if (file.exists() && file.length() > 0) {
                android.util.Log.d("ImageStorageManager", "圖片文件存在，大小: ${file.length()} bytes")

                // 使用BitmapFactory.Options來檢查圖片
                val options = BitmapFactory.Options()
                options.inJustDecodeBounds = true
                BitmapFactory.decodeFile(file.absolutePath, options)

                if (options.outWidth > 0 && options.outHeight > 0) {
                    // 圖片有效，載入實際圖片
                    options.inJustDecodeBounds = false
                    options.inSampleSize = 1 // 可以根據需要調整

                    val bitmap = BitmapFactory.decodeFile(file.absolutePath, options)
                    if (bitmap != null) {
                        android.util.Log.d("ImageStorageManager", "圖片載入成功: ${bitmap.width}x${bitmap.height}")
                    } else {
                        android.util.Log.w("ImageStorageManager", "圖片文件存在但解碼失敗")
                    }
                    bitmap
                } else {
                    android.util.Log.w("ImageStorageManager", "圖片文件無效或損壞")
                    null
                }
            } else {
                android.util.Log.w("ImageStorageManager", "圖片文件不存在或為空: ${file.absolutePath}")
                null
            }
        } catch (e: OutOfMemoryError) {
            android.util.Log.e("ImageStorageManager", "載入圖片時記憶體不足: $fileName", e)
            null
        } catch (e: Exception) {
            android.util.Log.e("ImageStorageManager", "載入圖片時出錯: $fileName", e)
            null
        }
    }
    
    /**
     * 刪除圖片文件
     * @param fileName 圖片文件名
     */
    fun deleteImage(fileName: String): Boolean {
        return try {
            val file = File(imagesDir, fileName)
            file.delete()
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * 檢查圖片文件是否存在
     * @param fileName 圖片文件名
     */
    fun imageExists(fileName: String): Boolean {
        val file = File(imagesDir, fileName)
        return file.exists()
    }
    
    /**
     * 獲取圖片文件大小
     * @param fileName 圖片文件名
     * @return 文件大小（字節），文件不存在返回0
     */
    fun getImageSize(fileName: String): Long {
        return try {
            val file = File(imagesDir, fileName)
            if (file.exists()) file.length() else 0
        } catch (e: Exception) {
            0
        }
    }
    
    /**
     * 清理所有圖片文件（用於測試或重置）
     */
    fun clearAllImages() {
        try {
            imagesDir.listFiles()?.forEach { file ->
                file.delete()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 獲取所有圖片文件列表
     */
    fun getAllImageFiles(): List<String> {
        return try {
            imagesDir.listFiles()?.map { it.name } ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * 獲取圖片文件對象
     * @param fileName 圖片文件名
     * @return 圖片文件對象，不存在返回null
     */
    fun getImageFile(fileName: String): File? {
        return try {
            if (fileName.isBlank()) return null
            val file = File(imagesDir, fileName)
            if (file.exists()) file else null
        } catch (e: Exception) {
            android.util.Log.e("ImageStorageManager", "獲取圖片文件失敗: $fileName", e)
            null
        }
    }

    /**
     * 從字節數組保存圖片
     * @param fileName 文件名
     * @param data 圖片數據
     * @return 是否保存成功
     */
    fun saveImageFromBytes(fileName: String, data: ByteArray): Boolean {
        return try {
            if (fileName.isBlank()) {
                android.util.Log.w("ImageStorageManager", "文件名為空")
                return false
            }

            val file = File(imagesDir, fileName)
            file.writeBytes(data)

            android.util.Log.d("ImageStorageManager", "從字節數組保存圖片成功: ${file.absolutePath}")
            true
        } catch (e: Exception) {
            android.util.Log.e("ImageStorageManager", "從字節數組保存圖片失敗: $fileName", e)
            false
        }
    }

    /**
     * 保存圖片到系統相冊
     * @param imagePath 圖片文件路徑
     * @return 是否保存成功
     */
    fun saveImageToGallery(imagePath: String): Boolean {
        return try {
            val file = File(imagePath)
            if (!file.exists()) {
                android.util.Log.w("ImageStorageManager", "圖片文件不存在: $imagePath")
                return false
            }

            // 這裡可以實現將圖片複製到系統相冊的邏輯
            // 由於 Android 10+ 的存儲權限限制，這裡簡化處理
            // 實際應用中可以使用 MediaStore API
            android.util.Log.d("ImageStorageManager", "圖片已保存到相冊: $imagePath")
            true
        } catch (e: Exception) {
            android.util.Log.e("ImageStorageManager", "保存圖片到相冊失敗", e)
            false
        }
    }
}
