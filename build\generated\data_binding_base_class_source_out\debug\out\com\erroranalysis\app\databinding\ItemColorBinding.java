// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemColorBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final View viewColor;

  @NonNull
  public final View viewColorBorder;

  private ItemColorBinding(@NonNull FrameLayout rootView, @NonNull View viewColor,
      @NonNull View viewColorBorder) {
    this.rootView = rootView;
    this.viewColor = viewColor;
    this.viewColorBorder = viewColorBorder;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemColorBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemColorBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_color, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemColorBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.view_color;
      View viewColor = ViewBindings.findChildViewById(rootView, id);
      if (viewColor == null) {
        break missingId;
      }

      id = R.id.view_color_border;
      View viewColorBorder = ViewBindings.findChildViewById(rootView, id);
      if (viewColorBorder == null) {
        break missingId;
      }

      return new ItemColorBinding((FrameLayout) rootView, viewColor, viewColorBorder);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
