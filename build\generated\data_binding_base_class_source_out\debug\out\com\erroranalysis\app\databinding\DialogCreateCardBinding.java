// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogCreateCardBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton buttonAddAnswerImage;

  @NonNull
  public final ImageButton buttonAddQuestionImage;

  @NonNull
  public final ImageButton buttonCancel;

  @NonNull
  public final ImageButton buttonSave;

  @NonNull
  public final TextInputEditText editAnswer;

  @NonNull
  public final TextInputEditText editQuestion;

  @NonNull
  public final TextInputEditText editTags;

  private DialogCreateCardBinding(@NonNull LinearLayout rootView,
      @NonNull ImageButton buttonAddAnswerImage, @NonNull ImageButton buttonAddQuestionImage,
      @NonNull ImageButton buttonCancel, @NonNull ImageButton buttonSave,
      @NonNull TextInputEditText editAnswer, @NonNull TextInputEditText editQuestion,
      @NonNull TextInputEditText editTags) {
    this.rootView = rootView;
    this.buttonAddAnswerImage = buttonAddAnswerImage;
    this.buttonAddQuestionImage = buttonAddQuestionImage;
    this.buttonCancel = buttonCancel;
    this.buttonSave = buttonSave;
    this.editAnswer = editAnswer;
    this.editQuestion = editQuestion;
    this.editTags = editTags;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogCreateCardBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogCreateCardBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_create_card, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogCreateCardBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_add_answer_image;
      ImageButton buttonAddAnswerImage = ViewBindings.findChildViewById(rootView, id);
      if (buttonAddAnswerImage == null) {
        break missingId;
      }

      id = R.id.button_add_question_image;
      ImageButton buttonAddQuestionImage = ViewBindings.findChildViewById(rootView, id);
      if (buttonAddQuestionImage == null) {
        break missingId;
      }

      id = R.id.button_cancel;
      ImageButton buttonCancel = ViewBindings.findChildViewById(rootView, id);
      if (buttonCancel == null) {
        break missingId;
      }

      id = R.id.button_save;
      ImageButton buttonSave = ViewBindings.findChildViewById(rootView, id);
      if (buttonSave == null) {
        break missingId;
      }

      id = R.id.edit_answer;
      TextInputEditText editAnswer = ViewBindings.findChildViewById(rootView, id);
      if (editAnswer == null) {
        break missingId;
      }

      id = R.id.edit_question;
      TextInputEditText editQuestion = ViewBindings.findChildViewById(rootView, id);
      if (editQuestion == null) {
        break missingId;
      }

      id = R.id.edit_tags;
      TextInputEditText editTags = ViewBindings.findChildViewById(rootView, id);
      if (editTags == null) {
        break missingId;
      }

      return new DialogCreateCardBinding((LinearLayout) rootView, buttonAddAnswerImage,
          buttonAddQuestionImage, buttonCancel, buttonSave, editAnswer, editQuestion, editTags);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
