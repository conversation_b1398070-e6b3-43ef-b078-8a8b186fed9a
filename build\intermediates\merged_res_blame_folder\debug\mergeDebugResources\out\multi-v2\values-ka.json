{"logs": [{"outputFile": "com.erroranalysis.app.app_camera_refactor-mergeDebugResources-45:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b749962aa03682a0d5ec949dd6944f8\\transformed\\play-services-basement-18.2.0\\res\\values-ka\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5495", "endColumns": "142", "endOffsets": "5633"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7a170d33460fdd9db3f9af4fb8b90c6b\\transformed\\play-services-base-18.2.0\\res\\values-ka\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,439,563,669,819,949,1067,1171,1340,1444,1595,1719,1876,2011,2073,2130", "endColumns": "100,144,123,105,149,129,117,103,168,103,150,123,156,134,61,56,71", "endOffsets": "293,438,562,668,818,948,1066,1170,1339,1443,1594,1718,1875,2010,2072,2129,2201"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4485,4590,4739,4867,4977,5131,5265,5387,5638,5811,5919,6074,6202,6363,6502,6568,6629", "endColumns": "104,148,127,109,153,133,121,107,172,107,154,127,160,138,65,60,75", "endOffsets": "4585,4734,4862,4972,5126,5260,5382,5490,5806,5914,6069,6197,6358,6497,6563,6624,6700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\38790647496f2de6b4f9690090db3cca\\transformed\\material-1.11.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,425,509,602,696,795,920,1008,1075,1172,1241,1304,1391,1455,1521,1581,1650,1711,1765,1880,1939,1999,2053,2125,2255,2343,2427,2565,2643,2719,2858,2952,3032,3088,3142,3208,3281,3359,3445,3529,3602,3680,3758,3833,3943,4033,4108,4202,4300,4374,4451,4551,4604,4688,4756,4845,4934,4996,5061,5124,5194,5301,5401,5501,5597,5657,5715", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,73,83,92,93,98,124,87,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,83,137,77,75,138,93,79,55,53,65,72,77,85,83,72,77,77,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79", "endOffsets": "268,346,420,504,597,691,790,915,1003,1070,1167,1236,1299,1386,1450,1516,1576,1645,1706,1760,1875,1934,1994,2048,2120,2250,2338,2422,2560,2638,2714,2853,2947,3027,3083,3137,3203,3276,3354,3440,3524,3597,3675,3753,3828,3938,4028,4103,4197,4295,4369,4446,4546,4599,4683,4751,4840,4929,4991,5056,5119,5189,5296,5396,5496,5592,5652,5710,5790"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3026,3104,3178,3262,3355,4173,4272,4397,6705,6772,6869,6938,7001,7088,7152,7218,7278,7347,7408,7462,7577,7636,7696,7750,7822,7952,8040,8124,8262,8340,8416,8555,8649,8729,8785,8839,8905,8978,9056,9142,9226,9299,9377,9455,9530,9640,9730,9805,9899,9997,10071,10148,10248,10301,10385,10453,10542,10631,10693,10758,10821,10891,10998,11098,11198,11294,11354,11412", "endLines": "5,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "12,77,73,83,92,93,98,124,87,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,83,137,77,75,138,93,79,55,53,65,72,77,85,83,72,77,77,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79", "endOffsets": "318,3099,3173,3257,3350,3444,4267,4392,4480,6767,6864,6933,6996,7083,7147,7213,7273,7342,7403,7457,7572,7631,7691,7745,7817,7947,8035,8119,8257,8335,8411,8550,8644,8724,8780,8834,8900,8973,9051,9137,9221,9294,9372,9450,9525,9635,9725,9800,9894,9992,10066,10143,10243,10296,10380,10448,10537,10626,10688,10753,10816,10886,10993,11093,11193,11289,11349,11407,11487"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d3e25ec97e005582fa7c282bb4a9e4a2\\transformed\\appcompat-1.6.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,534,645,731,836,949,1032,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2243,2349,2447,2560,2665,2769,2927,11492", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "426,529,640,726,831,944,1027,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2238,2344,2442,2555,2660,2764,2922,3021,11569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2087ae0d0678f91d06664b94633b22db\\transformed\\core-1.12.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "38,39,40,41,42,43,44,127", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3449,3545,3647,3746,3845,3951,4055,11574", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3540,3642,3741,3840,3946,4050,4168,11670"}}]}]}