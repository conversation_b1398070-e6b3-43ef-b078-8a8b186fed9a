// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.erroranalysis.app.ui.camera.CropSelectionOverlay;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAiSolveBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ScrollView aiAnswerContainer;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final Button btnSaveToCard;

  @NonNull
  public final CropSelectionOverlay cropOverlay;

  @NonNull
  public final FrameLayout imageContainer;

  @NonNull
  public final ImageView ivPhoto;

  @NonNull
  public final LinearLayout loadingContainer;

  @NonNull
  public final TextView tvAiAnswer;

  private ActivityAiSolveBinding(@NonNull ConstraintLayout rootView,
      @NonNull ScrollView aiAnswerContainer, @NonNull ImageButton btnBack,
      @NonNull Button btnSaveToCard, @NonNull CropSelectionOverlay cropOverlay,
      @NonNull FrameLayout imageContainer, @NonNull ImageView ivPhoto,
      @NonNull LinearLayout loadingContainer, @NonNull TextView tvAiAnswer) {
    this.rootView = rootView;
    this.aiAnswerContainer = aiAnswerContainer;
    this.btnBack = btnBack;
    this.btnSaveToCard = btnSaveToCard;
    this.cropOverlay = cropOverlay;
    this.imageContainer = imageContainer;
    this.ivPhoto = ivPhoto;
    this.loadingContainer = loadingContainer;
    this.tvAiAnswer = tvAiAnswer;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAiSolveBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAiSolveBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_ai_solve, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAiSolveBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ai_answer_container;
      ScrollView aiAnswerContainer = ViewBindings.findChildViewById(rootView, id);
      if (aiAnswerContainer == null) {
        break missingId;
      }

      id = R.id.btn_back;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_save_to_card;
      Button btnSaveToCard = ViewBindings.findChildViewById(rootView, id);
      if (btnSaveToCard == null) {
        break missingId;
      }

      id = R.id.crop_overlay;
      CropSelectionOverlay cropOverlay = ViewBindings.findChildViewById(rootView, id);
      if (cropOverlay == null) {
        break missingId;
      }

      id = R.id.image_container;
      FrameLayout imageContainer = ViewBindings.findChildViewById(rootView, id);
      if (imageContainer == null) {
        break missingId;
      }

      id = R.id.iv_photo;
      ImageView ivPhoto = ViewBindings.findChildViewById(rootView, id);
      if (ivPhoto == null) {
        break missingId;
      }

      id = R.id.loading_container;
      LinearLayout loadingContainer = ViewBindings.findChildViewById(rootView, id);
      if (loadingContainer == null) {
        break missingId;
      }

      id = R.id.tv_ai_answer;
      TextView tvAiAnswer = ViewBindings.findChildViewById(rootView, id);
      if (tvAiAnswer == null) {
        break missingId;
      }

      return new ActivityAiSolveBinding((ConstraintLayout) rootView, aiAnswerContainer, btnBack,
          btnSaveToCard, cropOverlay, imageContainer, ivPhoto, loadingContainer, tvAiAnswer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
