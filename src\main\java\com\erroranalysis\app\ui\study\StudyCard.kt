package com.erroranalysis.app.ui.study

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 學習卡片資料模型
 */
@Parcelize
data class StudyCard(
    val id: String,
    val deckId: String,
    val question: String,
    val answer: String,
    val aiAnswer: String = "", // AI解答內容
    val questionImagePath: String? = null,
    val answerImagePath: String? = null,
    val tags: List<String> = emptyList(),
    val difficulty: CardDifficulty = CardDifficulty.NORMAL, // 保留向後兼容
    val mastery: CardMastery = CardMastery.LEVEL_3, // 新的熟練度系統，默認為普通
    val masteryLevel: Int = 3, // 1-5，對應五級熟悉程度
    val reviewCount: Int = 0,
    val correctCount: Int = 0,
    val lastReviewTime: Long = 0,
    val nextReviewTime: Long = 0,
    val createdTime: Long = System.currentTimeMillis(),
    val updatedTime: Long = System.currentTimeMillis(),
    val isStarred: Boolean = false
) : Parcelable {
    
    /**
     * 計算正確率
     */
    val accuracyRate: Float
        get() = if (reviewCount > 0) correctCount.toFloat() / reviewCount else 0f
    
    /**
     * 是否需要複習
     */
    val needsReview: Boolean
        get() = System.currentTimeMillis() >= nextReviewTime
    
    /**
     * 是否已掌握（有點熟或非常熟）
     */
    val isMastered: Boolean
        get() = masteryLevel >= 4
}

/**
 * 卡片熟練度枚舉 - 五級數字系統
 */
enum class CardMastery(val displayName: String, val color: String, val level: Int) {
    LEVEL_1("完全不熟", "#F44336", 1),
    LEVEL_2("有點不熟", "#FF9800", 2),
    LEVEL_3("普通", "#2196F3", 3),
    LEVEL_4("有點熟", "#4CAF50", 4),
    LEVEL_5("非常熟", "#8BC34A", 5);

    companion object {
        /**
         * 根據數字級別獲取對應的熟練度
         */
        fun fromLevel(level: Int): CardMastery {
            return when (level) {
                1 -> LEVEL_1
                2 -> LEVEL_2
                3 -> LEVEL_3
                4 -> LEVEL_4
                5 -> LEVEL_5
                else -> LEVEL_3 // 默認為普通
            }
        }

        /**
         * 獲取所有級別列表
         */
        fun getAllLevels(): List<CardMastery> {
            return listOf(LEVEL_1, LEVEL_2, LEVEL_3, LEVEL_4, LEVEL_5)
        }
    }
}

/**
 * 向後兼容的難度枚舉（保留以支援舊數據）
 */
@Deprecated("請使用CardMastery代替")
enum class CardDifficulty(val displayName: String, val color: String) {
    EASY("簡單", "#4CAF50"),
    NORMAL("普通", "#2196F3"),
    HARD("困難", "#FF9800"),
    VERY_HARD("很困難", "#F44336");

    /**
     * 轉換為新的熟練度系統
     */
    fun toMastery(): CardMastery {
        return when (this) {
            EASY -> CardMastery.LEVEL_5      // 簡單 -> 非常熟
            NORMAL -> CardMastery.LEVEL_3    // 普通 -> 普通
            HARD -> CardMastery.LEVEL_2      // 困難 -> 有點不熟
            VERY_HARD -> CardMastery.LEVEL_1 // 很困難 -> 完全不熟
        }
    }
}

/**
 * 複習結果枚舉
 */
enum class ReviewResult {
    AGAIN,      // 再次複習
    HARD,       // 困難
    GOOD,       // 良好
    EASY        // 簡單
}
