package com.erroranalysis.app.ui.theme

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.erroranalysis.app.databinding.ItemFontOptionBinding

/**
 * 字體選擇適配器
 */
class FontSelectorAdapter(
    private val fonts: List<FontManager.FontType>,
    private var selectedFont: FontManager.FontType,
    private val onFontSelected: (FontManager.FontType) -> Unit
) : RecyclerView.Adapter<FontSelectorAdapter.FontViewHolder>() {

    inner class FontViewHolder(private val binding: ItemFontOptionBinding) : 
        RecyclerView.ViewHolder(binding.root) {
        
        fun bind(fontType: FontManager.FontType) {
            // 設置字體名稱
            binding.textFontName.text = fontType.displayName

            // 確保文字可見
            binding.textFontName.visibility = android.view.View.VISIBLE
            
            // 設置選中狀態
            binding.radioFont.isChecked = fontType == selectedFont
            
            // 設置點擊事件
            binding.root.setOnClickListener {
                if (selectedFont != fontType) {
                    val oldSelected = selectedFont
                    selectedFont = fontType
                    
                    // 更新UI
                    notifyItemChanged(fonts.indexOf(oldSelected))
                    notifyItemChanged(fonts.indexOf(fontType))
                    
                    // 回調
                    onFontSelected(fontType)
                }
            }
            
            binding.radioFont.setOnClickListener {
                binding.root.performClick()
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FontViewHolder {
        val binding = ItemFontOptionBinding.inflate(
            LayoutInflater.from(parent.context), 
            parent, 
            false
        )
        return FontViewHolder(binding)
    }

    override fun onBindViewHolder(holder: FontViewHolder, position: Int) {
        holder.bind(fonts[position])
    }

    override fun getItemCount(): Int = fonts.size
    
    /**
     * 更新選中的字體
     */
    fun updateSelectedFont(fontType: FontManager.FontType) {
        if (selectedFont != fontType) {
            val oldSelected = selectedFont
            selectedFont = fontType
            
            notifyItemChanged(fonts.indexOf(oldSelected))
            notifyItemChanged(fonts.indexOf(fontType))
        }
    }
}
