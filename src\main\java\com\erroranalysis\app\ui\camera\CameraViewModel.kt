package com.erroranalysis.app.ui.camera

import android.graphics.PointF
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageProxy
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.erroranalysis.app.domain.camera.model.*
import com.erroranalysis.app.domain.camera.usecase.CameraUseCases
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * 相機 ViewModel (MVVM 模式)
 * 管理相機相關的 UI 狀態和業務邏輯
 */
class CameraViewModel(
    private val cameraUseCases: CameraUseCases
) : ViewModel() {
    
    // UI 狀態
    private val _uiState = MutableStateFlow(CameraUiState())
    val uiState: StateFlow<CameraUiState> = _uiState.asStateFlow()
    
    // 相機狀態
    val focusState: Flow<FocusState> = cameraUseCases.getFocusState()
    val captureState: Flow<CaptureState> = cameraUseCases.getCaptureState()
    val documentDetectionState: Flow<DocumentDetectionResult> = cameraUseCases.getDocumentDetectionState()
    
    // 事件
    private val _events = MutableSharedFlow<CameraEvent>()
    val events: SharedFlow<CameraEvent> = _events.asSharedFlow()
    
    init {
        // 監聽相機狀態變化
        observeCameraStates()
    }
    
    /**
     * 拍攝照片
     */
    fun captureImage(imageCapture: ImageCapture, outputFileOptions: ImageCapture.OutputFileOptions) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                val result = cameraUseCases.captureImage(imageCapture, outputFileOptions)
                when (result) {
                    is CaptureResult.Success -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            lastCapturedImagePath = result.imagePath
                        )
                        _events.emit(CameraEvent.CaptureSuccess(result.imagePath))
                    }
                    is CaptureResult.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            errorMessage = result.message
                        )
                        _events.emit(CameraEvent.CaptureError(result.message))
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "拍攝失敗: ${e.message}"
                )
                _events.emit(CameraEvent.CaptureError("拍攝失敗: ${e.message}"))
            }
        }
    }
    
    /**
     * 分析圖像
     */
    fun analyzeImage(imageProxy: ImageProxy) {
        viewModelScope.launch {
            try {
                cameraUseCases.analyzeImage(imageProxy)
                // 結果會通過 Flow 自動更新 UI
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "圖像分析失敗: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 檢測文檔邊界
     */
    fun detectDocumentBoundaries(imagePath: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                val boundaries = cameraUseCases.detectDocumentBoundaries(imagePath)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    detectedBoundaries = boundaries
                )
                
                if (boundaries != null) {
                    _events.emit(CameraEvent.DocumentBoundariesDetected(boundaries))
                } else {
                    _events.emit(CameraEvent.DocumentDetectionFailed("無法檢測到文檔邊界"))
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "文檔檢測失敗: ${e.message}"
                )
                _events.emit(CameraEvent.DocumentDetectionFailed("文檔檢測失敗: ${e.message}"))
            }
        }
    }
    
    /**
     * 校正透視
     */
    fun correctPerspective(imagePath: String, boundaries: List<PointF>) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                val correctedImagePath = cameraUseCases.correctPerspective(imagePath, boundaries)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    correctedImagePath = correctedImagePath
                )
                
                if (correctedImagePath != null) {
                    _events.emit(CameraEvent.PerspectiveCorrected(correctedImagePath))
                } else {
                    _events.emit(CameraEvent.PerspectiveCorrectionFailed("透視校正失敗"))
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "透視校正失敗: ${e.message}"
                )
                _events.emit(CameraEvent.PerspectiveCorrectionFailed("透視校正失敗: ${e.message}"))
            }
        }
    }
    
    /**
     * 處理文檔
     */
    fun processDocument(imagePath: String, manualBoundaries: List<PointF>? = null) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                val result = cameraUseCases.processDocument(imagePath, manualBoundaries)
                _uiState.value = _uiState.value.copy(isLoading = false)
                
                when (result) {
                    is DocumentProcessResult.Success -> {
                        _uiState.value = _uiState.value.copy(
                            correctedImagePath = result.correctedImagePath,
                            detectedBoundaries = result.detectedBoundaries
                        )
                        _events.emit(CameraEvent.DocumentProcessed(result))
                    }
                    is DocumentProcessResult.Error -> {
                        _uiState.value = _uiState.value.copy(
                            errorMessage = result.message
                        )
                        _events.emit(CameraEvent.DocumentProcessingFailed(result.message))
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "文檔處理失敗: ${e.message}"
                )
                _events.emit(CameraEvent.DocumentProcessingFailed("文檔處理失敗: ${e.message}"))
            }
        }
    }
    
    /**
     * 執行 OCR
     */
    fun performOCR(imagePath: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                val result = cameraUseCases.performOCR(imagePath)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    ocrResult = result
                )
                _events.emit(CameraEvent.OCRCompleted(result))
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "OCR 處理失敗: ${e.message}"
                )
                _events.emit(CameraEvent.OCRFailed("OCR 處理失敗: ${e.message}"))
            }
        }
    }
    
    /**
     * AI 求解
     */
    fun solveWithAI(imagePath: String, questionText: String? = null) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                val result = cameraUseCases.solveWithAI(imagePath, questionText)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    aiResult = result
                )
                _events.emit(CameraEvent.AISolutionCompleted(result))
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "AI 求解失敗: ${e.message}"
                )
                _events.emit(CameraEvent.AISolutionFailed("AI 求解失敗: ${e.message}"))
            }
        }
    }
    
    /**
     * 保存圖片到相簿
     */
    fun saveImageToGallery(imagePath: String) {
        viewModelScope.launch {
            try {
                val success = cameraUseCases.saveImageToGallery(imagePath)
                if (success) {
                    _events.emit(CameraEvent.ImageSavedToGallery)
                } else {
                    _events.emit(CameraEvent.ImageSaveFailed("保存到相簿失敗"))
                }
            } catch (e: Exception) {
                _events.emit(CameraEvent.ImageSaveFailed("保存到相簿失敗: ${e.message}"))
            }
        }
    }
    
    /**
     * 切換相機設定
     */
    fun toggleDocumentDetection() {
        val currentSettings = cameraUseCases.getCameraSettings()
        val newSettings = currentSettings.copy(
            enableDocumentDetection = !currentSettings.enableDocumentDetection
        )
        cameraUseCases.updateCameraSettings(newSettings)
        _uiState.value = _uiState.value.copy(cameraSettings = newSettings)
    }
    
    fun toggleFocusAnalysis() {
        val currentSettings = cameraUseCases.getCameraSettings()
        val newSettings = currentSettings.copy(
            enableFocusAnalysis = !currentSettings.enableFocusAnalysis
        )
        cameraUseCases.updateCameraSettings(newSettings)
        _uiState.value = _uiState.value.copy(cameraSettings = newSettings)
    }
    
    /**
     * 清除錯誤訊息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
    
    /**
     * 重置狀態
     */
    fun resetState() {
        _uiState.value = CameraUiState()
    }
    
    private fun observeCameraStates() {
        // 這裡可以添加對相機狀態的額外處理邏輯
        // 例如根據對焦狀態調整 UI 提示等
    }
}

/**
 * 相機 UI 狀態
 */
data class CameraUiState(
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val lastCapturedImagePath: String? = null,
    val correctedImagePath: String? = null,
    val detectedBoundaries: List<PointF>? = null,
    val ocrResult: OCRResult? = null,
    val aiResult: AIResult? = null,
    val cameraSettings: CameraSettings = CameraSettings()
)

/**
 * 相機事件
 */
sealed class CameraEvent {
    data class CaptureSuccess(val imagePath: String) : CameraEvent()
    data class CaptureError(val message: String) : CameraEvent()
    data class DocumentBoundariesDetected(val boundaries: List<PointF>) : CameraEvent()
    data class DocumentDetectionFailed(val message: String) : CameraEvent()
    data class PerspectiveCorrected(val imagePath: String) : CameraEvent()
    data class PerspectiveCorrectionFailed(val message: String) : CameraEvent()
    data class DocumentProcessed(val result: DocumentProcessResult.Success) : CameraEvent()
    data class DocumentProcessingFailed(val message: String) : CameraEvent()
    data class OCRCompleted(val result: OCRResult) : CameraEvent()
    data class OCRFailed(val message: String) : CameraEvent()
    data class AISolutionCompleted(val result: AIResult) : CameraEvent()
    data class AISolutionFailed(val message: String) : CameraEvent()
    object ImageSavedToGallery : CameraEvent()
    data class ImageSaveFailed(val message: String) : CameraEvent()
}