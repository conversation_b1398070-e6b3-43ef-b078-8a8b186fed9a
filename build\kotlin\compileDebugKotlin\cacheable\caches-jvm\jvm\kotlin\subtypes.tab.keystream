7com.erroranalysis.app.domain.camera.model.CaptureResult?com.erroranalysis.app.domain.camera.model.DocumentProcessResult+com.erroranalysis.app.ui.camera.CameraEventandroid.view.View+androidx.camera.core.ImageAnalysis.Analyzer?com.erroranalysis.app.ui.theme.ThemeManager.ThemeChangeListener,androidx.lifecycle.ViewModelProvider.Factory,com.erroranalysis.app.ui.base.ThemedActivityandroid.os.Parcelable(androidx.recyclerview.widget.ListAdapter2androidx.recyclerview.widget.DiffUtil.ItemCallback(androidx.appcompat.app.AppCompatActivitykotlin.Enum1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolder(com.erroranalysis.app.utils.ImportResult<com.erroranalysis.app.utils.SimpleBackupManager.BackupResult=com.erroranalysis.app.utils.SimpleBackupManager.RestoreResult androidx.viewbinding.ViewBindingandroid.app.Application*com.erroranalysis.app.data.IDeckRepository3com.erroranalysis.app.data.camera.ICameraRepositoryandroidx.lifecycle.ViewModel#androidx.lifecycle.AndroidViewModelandroid.app.Dialog7com.google.android.material.textfield.TextInputEditTextandroid.text.style.ImageSpan8androidx.recyclerview.widget.RecyclerView.ItemDecoration                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        