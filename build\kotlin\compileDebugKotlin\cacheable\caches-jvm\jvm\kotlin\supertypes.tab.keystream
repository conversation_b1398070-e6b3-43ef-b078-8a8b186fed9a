.com.erroranalysis.app.ErrorAnalysisApplication*<EMAIL>?com.erroranalysis.app.domain.camera.model.CaptureResult.Success=com.erroranalysis.app.domain.camera.model.CaptureResult.ErrorGcom.erroranalysis.app.domain.camera.model.DocumentProcessResult.SuccessEcom.erroranalysis.app.domain.camera.model.DocumentProcessResult.Error,com.erroranalysis.app.ui.base.ThemedActivity/com.erroranalysis.app.ui.camera.AiSolveActivity.com.erroranalysis.app.ui.camera.CameraActivity/com.erroranalysis.app.ui.camera.CameraViewModel:<EMAIL>=com.erroranalysis.app.ui.camera.CameraEvent.DocumentProcessedDcom.erroranalysis.app.ui.camera.CameraEvent.DocumentProcessingFailed8com.erroranalysis.app.ui.camera.CameraEvent.OCRCompleted5com.erroranalysis.app.ui.camera.CameraEvent.OCRFailed?com.erroranalysis.app.ui.camera.CameraEvent.AISolutionCompleted<com.erroranalysis.app.ui.camera.CameraEvent.AISolutionFailed?com.erroranalysis.app.ui.camera.CameraEvent.ImageSavedToGallery;com.erroranalysis.app.ui.camera.CameraEvent.ImageSaveFailed6com.erroranalysis.app.ui.camera.CameraViewModelFactory5com.erroranalysis.app.ui.camera.CombinedImageAnalyzer7com.erroranalysis.app.ui.camera.CropOverlayTestActivity4com.erroranalysis.app.ui.camera.CropSelectionOverlay=com.erroranalysis.app.ui.camera.CropSelectionOverlay.DragMode4com.erroranalysis.app.ui.camera.DeckSelectionAdapterCcom.erroranalysis.app.ui.camera.DeckSelectionAdapter.DeckViewHolder7com.erroranalysis.app.ui.camera.DocumentBoundaryOverlay9com.erroranalysis.app.ui.camera.DocumentDetectionAnalyzer-com.erroranalysis.app.ui.camera.FocusAnalyzer1com.erroranalysis.app.ui.camera.PhotoEditActivity4com.erroranalysis.app.ui.camera.SimpleCameraActivity0com.erroranalysis.app.ui.main.SimpleMainActivity7com.erroranalysis.app.ui.settings.BackupRestoreActivityEcom.erroranalysis.app.ui.settings.BackupRestoreActivity.PendingAction3com.erroranalysis.app.ui.settings.FontOptionAdapterHcom.erroranalysis.app.ui.settings.FontOptionAdapter.FontOptionViewHolder2com.erroranalysis.app.ui.settings.SettingsActivity7com.erroranalysis.app.ui.settings.adapters.ThemeAdapterGcom.erroranalysis.app.ui.settings.adapters.ThemeAdapter.ThemeViewHolder2com.erroranalysis.app.ui.study.BatchImportActivity/com.erroranalysis.app.ui.study.CardEditActivity1com.erroranalysis.app.ui.study.CardViewerActivity1com.erroranalysis.app.ui.study.DeckDetailActivity+com.erroranalysis.app.ui.study.CardSortType2com.erroranalysis.app.ui.study.DeckDetailViewModel9com.erroranalysis.app.ui.study.DeckDetailViewModelFactory+com.erroranalysis.app.ui.study.DeckSortType2com.erroranalysis.app.ui.study.SimpleStudyActivity)com.erroranalysis.app.ui.study.SimpleDeck(com.erroranalysis.app.ui.study.StudyCard*com.erroranalysis.app.ui.study.CardMastery-com.erroranalysis.app.ui.study.CardDifficulty+com.erroranalysis.app.ui.study.ReviewResult(com.erroranalysis.app.ui.study.StudyDeck4com.erroranalysis.app.ui.study.adapters.ColorAdapterDcom.erroranalysis.app.ui.study.adapters.ColorAdapter.ColorViewHolderFcom.erroranalysis.app.ui.study.adapters.ColorAdapter.ColorDiffCallback3com.erroranalysis.app.ui.study.adapters.IconAdapterBcom.erroranalysis.app.ui.study.adapters.IconAdapter.IconViewHolder9com.erroranalysis.app.ui.study.adapters.SimpleDeckAdapterHcom.erroranalysis.app.ui.study.adapters.SimpleDeckAdapter.DeckViewHolderJcom.erroranalysis.app.ui.study.adapters.SimpleDeckAdapter.DeckDiffCallback8com.erroranalysis.app.ui.study.adapters.StudyCardAdapterGcom.erroranalysis.app.ui.study.adapters.StudyCardAdapter.CardViewHolderIcom.erroranalysis.app.ui.study.adapters.StudyCardAdapter.CardDiffCallback<com.erroranalysis.app.ui.test.DocumentCorrectionTestActivity3com.erroranalysis.app.ui.theme.FontManager.FontType2com.erroranalysis.app.ui.theme.FontSelectorAdapterAcom.erroranalysis.app.ui.theme.FontSelectorAdapter.FontViewHolder1com.erroranalysis.app.ui.theme.FontSelectorDialog1com.erroranalysis.app.ui.widgets.RichTextEditTextCcom.erroranalysis.app.ui.widgets.RichTextEditText.ZoomableImageSpan0com.erroranalysis.app.utils.ImportResult.Success.com.erroranalysis.app.utils.ImportResult.Error5com.erroranalysis.app.utils.GridSpacingItemDecorationDcom.erroranalysis.app.utils.SimpleBackupManager.BackupResult.SuccessBcom.erroranalysis.app.utils.SimpleBackupManager.BackupResult.ErrorEcom.erroranalysis.app.utils.SimpleBackupManager.RestoreResult.SuccessCcom.erroranalysis.app.utils.SimpleBackupManager.RestoreResult.Error2com.erroranalysis.app.databinding.ItemThemeBinding9com.erroranalysis.app.databinding.ActivityCardEditBinding6com.erroranalysis.app.databinding.ItemStudyCardBinding9com.erroranalysis.app.databinding.ActivitySettingsBinding2com.erroranalysis.app.databinding.ItemColorBinding;com.erroranalysis.app.databinding.ActivityDeckDetailBinding<com.erroranalysis.app.databinding.ActivityBatchImportBinding<com.erroranalysis.app.databinding.ActivitySimpleStudyBinding7com.erroranalysis.app.databinding.ItemFontOptionBinding9com.erroranalysis.app.databinding.DialogCardFilterBinding;com.erroranalysis.app.databinding.DialogFontSelectorBinding7com.erroranalysis.app.databinding.ActivityCameraBinding9com.erroranalysis.app.databinding.ItemIconSelectorBinding7com.erroranalysis.app.databinding.ItemSimpleDeckBinding8com.erroranalysis.app.databinding.ActivityAiSolveBinding>com.erroranalysis.app.databinding.ActivityBackupRestoreBinding;com.erroranalysis.app.databinding.ActivityCardViewerBinding5com.erroranalysis.app.databinding.ActivityMainBinding:com.erroranalysis.app.databinding.ItemDeckSelectionBinding:com.erroranalysis.app.databinding.ActivityPhotoEditBinding<com.erroranalysis.app.databinding.DialogDeckSelectionBinding6com.erroranalysis.app.databinding.PopupMenuItemBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          