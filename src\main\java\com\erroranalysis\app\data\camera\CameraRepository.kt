package com.erroranalysis.app.data.camera

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.PointF
import android.util.Log
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageProxy
import com.erroranalysis.app.domain.camera.model.*
import com.erroranalysis.app.utils.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Camera Repository 實作
 * 負責處理相機相關的資料操作
 */
class CameraRepository(
    private val context: Context
) : ICameraRepository {
    
    companion object {
        private const val TAG = "CameraRepository"
    }
    
    // 狀態管理
    private val _focusState = MutableStateFlow(FocusState.UNFOCUSED)
    private val _captureState = MutableStateFlow(CaptureState.IDLE)
    private val _documentDetectionState = MutableStateFlow(
        DocumentDetectionResult(null, DocumentDetectionState.DETECTING)
    )
    
    // 工具類別
    private val documentProcessor by lazy { DocumentProcessor() }
    private val ocrHelper by lazy { OCRHelper(context) }
    private val geminiAIService by lazy { GeminiAIService(context) }
    private val imageStorageManager by lazy { ImageStorageManager(context) }
    private val documentBoundaryDetector by lazy { DocumentBoundaryDetector() }
    private val perspectiveCorrector by lazy { PerspectiveCorrector() }
    
    // 設定管理
    private var cameraSettings = CameraSettings()
    
    override fun getFocusState(): Flow<FocusState> = _focusState.asStateFlow()
    
    override fun getCaptureState(): Flow<CaptureState> = _captureState.asStateFlow()
    
    override fun getDocumentDetectionState(): Flow<DocumentDetectionResult> = 
        _documentDetectionState.asStateFlow()
    
    override suspend fun captureImage(
        imageCapture: ImageCapture,
        outputFileOptions: ImageCapture.OutputFileOptions
    ): CaptureResult = withContext(Dispatchers.IO) {
        try {
            _captureState.value = CaptureState.CAPTURING
            
            // 使用 suspendCancellableCoroutine 來包裝回調
            val result = kotlinx.coroutines.suspendCancellableCoroutine<CaptureResult> { continuation ->
                imageCapture.takePicture(
                    outputFileOptions,
                    androidx.core.content.ContextCompat.getMainExecutor(context),
                    object : ImageCapture.OnImageSavedCallback {
                        override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                            val savedUri = output.savedUri
                            val imagePath = savedUri?.path ?: ""
                            _captureState.value = CaptureState.SUCCESS
                            continuation.resume(CaptureResult.Success(imagePath)) {}
                        }
                        
                        override fun onError(exception: androidx.camera.core.ImageCaptureException) {
                            _captureState.value = CaptureState.ERROR
                            continuation.resume(CaptureResult.Error("拍攝失敗", exception)) {}
                        }
                    }
                )
            }
            
            result
        } catch (e: Exception) {
            Log.e(TAG, "Error capturing image", e)
            _captureState.value = CaptureState.ERROR
            CaptureResult.Error("拍攝過程中發生錯誤", e)
        }
    }
    
    override suspend fun analyzeImage(imageProxy: ImageProxy): ImageAnalysisResult = withContext(Dispatchers.Default) {
        try {
            // 分析對焦狀態
            val focusState = analyzeFocus(imageProxy)
            _focusState.value = focusState
            
            // 分析文檔邊界（如果啟用）
            val documentDetection = if (cameraSettings.enableDocumentDetection) {
                analyzeDocumentBoundaries(imageProxy)
            } else {
                DocumentDetectionResult(null, DocumentDetectionState.NOT_DETECTED)
            }
            _documentDetectionState.value = documentDetection
            
            ImageAnalysisResult(focusState, documentDetection)
        } catch (e: Exception) {
            Log.e(TAG, "Error analyzing image", e)
            ImageAnalysisResult(
                FocusState.UNFOCUSED,
                DocumentDetectionResult(null, DocumentDetectionState.ERROR)
            )
        }
    }
    
    override suspend fun detectDocumentBoundaries(imagePath: String): List<PointF>? = withContext(Dispatchers.IO) {
        try {
            val bitmap = BitmapFactory.decodeFile(imagePath) ?: return@withContext null
            val boundaries = documentBoundaryDetector.detectDocumentBoundary(bitmap)
            bitmap.recycle()
            boundaries
        } catch (e: Exception) {
            Log.e(TAG, "Error detecting document boundaries", e)
            null
        }
    }
    
    override suspend fun correctPerspective(
        imagePath: String,
        boundaries: List<PointF>,
        outputWidth: Int?,
        outputHeight: Int?
    ): String? = withContext(Dispatchers.IO) {
        try {
            documentProcessor.correctPerspectiveOnly(imagePath, boundaries, outputWidth, outputHeight)
        } catch (e: Exception) {
            Log.e(TAG, "Error correcting perspective", e)
            null
        }
    }
    
    override suspend fun processDocument(
        imagePath: String,
        manualBoundaries: List<PointF>?
    ): DocumentProcessResult = withContext(Dispatchers.IO) {
        try {
            documentProcessor.processDocument(imagePath, manualBoundaries)
        } catch (e: Exception) {
            Log.e(TAG, "Error processing document", e)
            DocumentProcessResult.Error("處理文檔時發生錯誤: ${e.message}")
        }
    }
    
    override suspend fun performOCR(imagePath: String): OCRResult = withContext(Dispatchers.IO) {
        try {
            val bitmap = BitmapFactory.decodeFile(imagePath)
            if (bitmap == null) {
                return@withContext OCRResult("", 0f, false, "無法載入圖片")
            }
            
            val extractedText = ocrHelper.recognizeText(bitmap)
            bitmap.recycle()
            
            OCRResult(
                text = extractedText,
                confidence = 1.0f,
                isSuccess = extractedText.isNotEmpty() && !extractedText.contains("OCR識別失敗"),
                errorMessage = if (extractedText.isEmpty() || extractedText.contains("OCR識別失敗")) "OCR 處理失敗" else null
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error performing OCR", e)
            OCRResult("", 0f, false, "OCR 處理過程中發生錯誤: ${e.message}")
        }
    }
    
    override suspend fun solveWithAI(imagePath: String, questionText: String?): AIResult = withContext(Dispatchers.IO) {
        try {
            val bitmap = BitmapFactory.decodeFile(imagePath)
            if (bitmap == null) {
                return@withContext AIResult("", false, "無法載入圖片")
            }
            
            val prompt = questionText ?: "請分析這張圖片中的數學問題並提供詳細解答。"
            val solution = geminiAIService.analyzeImageWithPrompt(bitmap, prompt)
            bitmap.recycle()
            
            AIResult(solution, true)
        } catch (e: Exception) {
            Log.e(TAG, "Error solving with AI", e)
            AIResult("", false, "AI 求解過程中發生錯誤: ${e.message}")
        }
    }
    
    override suspend fun saveImageToGallery(imagePath: String): Boolean = withContext(Dispatchers.IO) {
        try {
            imageStorageManager.saveImageToGallery(imagePath)
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error saving image to gallery", e)
            false
        }
    }
    
    override suspend fun deleteImage(imagePath: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val file = File(imagePath)
            file.delete()
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting image", e)
            false
        }
    }
    
    override fun getImageInfo(imagePath: String): ImageInfo? {
        return try {
            val file = File(imagePath)
            if (!file.exists()) return null
            
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(imagePath, options)
            
            ImageInfo(
                width = options.outWidth,
                height = options.outHeight,
                mimeType = options.outMimeType ?: "image/jpeg",
                fileSize = file.length()
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error getting image info", e)
            null
        }
    }
    
    override fun getCameraSettings(): CameraSettings = cameraSettings
    
    override fun updateCameraSettings(settings: CameraSettings) {
        cameraSettings = settings
    }
    
    // 私有輔助方法
    private fun analyzeFocus(imageProxy: ImageProxy): FocusState {
        return try {
            // 簡化的對焦檢測邏輯
            val width = imageProxy.width
            val height = imageProxy.height
            val focusScore = (width * height) % 4
            
            when (focusScore) {
                0 -> FocusState.FOCUSED
                1 -> FocusState.TOO_FAR
                2 -> FocusState.TOO_CLOSE
                else -> FocusState.UNFOCUSED
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error analyzing focus", e)
            FocusState.UNFOCUSED
        }
    }
    
    private fun analyzeDocumentBoundaries(imageProxy: ImageProxy): DocumentDetectionResult {
        return try {
            // 生成模擬的文檔邊界
            val margin = 50f
            val width = imageProxy.width.toFloat()
            val height = imageProxy.height.toFloat()
            
            val boundaries = listOf(
                PointF(margin, margin),
                PointF(width - margin, margin),
                PointF(width - margin, height - margin),
                PointF(margin, height - margin)
            )
            
            DocumentDetectionResult(boundaries, DocumentDetectionState.DETECTED, 0.8f)
        } catch (e: Exception) {
            Log.e(TAG, "Error analyzing document boundaries", e)
            DocumentDetectionResult(null, DocumentDetectionState.ERROR)
        }
    }
}