package com.erroranalysis.app.utils

import android.content.Context
import android.net.Uri
import android.util.Log
import org.json.JSONObject
import org.json.JSONArray
import java.io.*
import java.util.zip.ZipInputStream
import com.erroranalysis.app.ui.study.StudyCard
import com.erroranalysis.app.ui.study.CardDifficulty
import com.erroranalysis.app.ui.study.CardMastery
import com.erroranalysis.app.data.DeckDataManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.erroranalysis.app.ui.study.SimpleDeck

/**
 * 批次匯入管理器
 */
class BatchImportManager(private val context: Context) {
    
    companion object {
        private const val TAG = "BatchImportManager"
    }
    
    /**
     * 匯入題庫檔案
     */
    suspend fun importQuestionBank(uri: Uri): ImportResult {
        return try {
            Log.i(TAG, "=== 開始匯入題庫檔案 ===")
            Log.i(TAG, "URI: $uri")

            val inputStream = context.contentResolver.openInputStream(uri)
            if (inputStream == null) {
                Log.e(TAG, "無法開啟檔案輸入流")
                return ImportResult.Error("無法讀取檔案")
            }

            // 檢查檔案類型
            val fileName = getFileName(uri)
            Log.i(TAG, "檔案名稱: '$fileName'")

            // 特別檢查"國小數學.csv"
            if (fileName == "國小數學.csv") {
                Log.w(TAG, "🚨 檢測到問題檔案：'國小數學.csv'")
                Log.w(TAG, "   建議用戶檢查：")
                Log.w(TAG, "   1. 是否選擇了正確的檔案")
                Log.w(TAG, "   2. 檔案是否來自其他應用程式")
                Log.w(TAG, "   3. 是否為雲端下載的檔案")
                Log.w(TAG, "   4. 清除應用程式快取後重試")
            }

            val result = when {
                fileName.endsWith(".zip") -> {
                    Log.i(TAG, "檔案類型：ZIP")
                    importFromZip(inputStream)
                }
                fileName.endsWith(".json") -> {
                    Log.i(TAG, "檔案類型：JSON")
                    importFromJson(inputStream)
                }
                fileName.endsWith(".csv") -> {
                    Log.i(TAG, "檔案類型：CSV")
                    importFromCsv(inputStream, fileName)
                }
                else -> {
                    Log.e(TAG, "不支援的檔案格式：$fileName")
                    ImportResult.Error("不支援的檔案格式，請使用ZIP、JSON或CSV檔案")
                }
            }

            Log.i(TAG, "=== 匯入結果 ===")
            when (result) {
                is ImportResult.Success -> {
                    Log.i(TAG, "✅ 匯入成功：${result.deckName}")
                    Log.i(TAG, "   成功：${result.successCount}/${result.totalCount}")
                    if (result.errors.isNotEmpty()) {
                        Log.w(TAG, "   錯誤：${result.errors.size}個")
                    }
                }
                is ImportResult.Error -> {
                    Log.e(TAG, "❌ 匯入失敗：${result.message}")
                }
            }
            Log.i(TAG, "=== 匯入完成 ===")

            result

        } catch (e: Exception) {
            Log.e(TAG, "匯入過程發生異常", e)
            ImportResult.Error("匯入失敗：${e.message}")
        }
    }
    
    /**
     * 從ZIP檔案匯入
     */
    private suspend fun importFromZip(inputStream: InputStream): ImportResult {
        val tempDir = File(context.cacheDir, "import_temp")
        tempDir.mkdirs()

        try {
            Log.d(TAG, "開始解壓ZIP檔案")

            // 解壓ZIP檔案
            val zipStream = ZipInputStream(inputStream)
            var entry = zipStream.nextEntry

            while (entry != null) {
                val file = File(tempDir, entry.name)
                if (entry.isDirectory) {
                    file.mkdirs()
                } else {
                    file.parentFile?.mkdirs()
                    val output = FileOutputStream(file)
                    zipStream.copyTo(output)
                    output.close()
                }
                zipStream.closeEntry()
                entry = zipStream.nextEntry
            }
            zipStream.close()

            Log.d(TAG, "ZIP檔案解壓完成，檢查內容")

            // 優先尋找CSV檔案
            val csvFiles = tempDir.listFiles { file ->
                file.isFile && file.name.endsWith(".csv", ignoreCase = true)
            }?.toList() ?: emptyList()

            if (csvFiles.isNotEmpty()) {
                Log.d(TAG, "找到${csvFiles.size}個CSV檔案，開始匯入")
                return importMultipleCsvFiles(csvFiles, File(tempDir, "images"))
            }

            // 如果沒有CSV檔案，尋找questions.json（向後兼容）
            val jsonFile = File(tempDir, "questions.json")
            if (jsonFile.exists()) {
                Log.d(TAG, "找到questions.json，使用JSON匯入")
                val jsonContent = jsonFile.readText()
                val result = parseLegacyJson(JSONObject(jsonContent), File(tempDir, "images"))
                tempDir.deleteRecursively()
                return result
            }

            // 都沒找到
            tempDir.deleteRecursively()
            return ImportResult.Error(
                "ZIP檔案中找不到可匯入的檔案\n\n" +
                "支援的檔案：\n" +
                "• CSV檔案（推薦）\n" +
                "• questions.json（舊格式）\n\n" +
                "請確認ZIP檔案包含正確的檔案格式"
            )

        } catch (e: Exception) {
            Log.e(TAG, "ZIP檔案匯入失敗", e)
            tempDir.deleteRecursively()
            return ImportResult.Error("ZIP檔案處理失敗：${e.message}")
        }
    }

    /**
     * 匯入多個CSV檔案
     */
    private suspend fun importMultipleCsvFiles(csvFiles: List<File>, imagesDir: File?): ImportResult {
        try {
            Log.d(TAG, "開始匯入${csvFiles.size}個CSV檔案")
            Log.d(TAG, "圖片目錄: ${imagesDir?.absolutePath}")
            Log.d(TAG, "圖片目錄是否存在: ${imagesDir?.exists()}")

            // 檢查圖片目錄內容
            if (imagesDir?.exists() == true) {
                val imageFiles = imagesDir.listFiles()
                Log.d(TAG, "圖片目錄包含 ${imageFiles?.size ?: 0} 個檔案:")
                imageFiles?.forEach { file ->
                    Log.d(TAG, "  - ${file.name} (${file.length()} bytes)")
                }
            } else {
                Log.w(TAG, "⚠️ 圖片目錄不存在或無法訪問")
            }

            val allResults = mutableListOf<ImportResult.Success>()
            val allErrors = mutableListOf<String>()

            for (csvFile in csvFiles) {
                try {
                    Log.d(TAG, "匯入CSV檔案: ${csvFile.name}（完整路徑: ${csvFile.absolutePath}）")

                    val result = csvFile.inputStream().use { inputStream ->
                        importFromCsvWithImages(inputStream, csvFile.name, imagesDir)
                    }

                    when (result) {
                        is ImportResult.Success -> {
                            allResults.add(result)
                            Log.d(TAG, "CSV檔案 ${csvFile.name} 匯入成功")
                        }
                        is ImportResult.Error -> {
                            allErrors.add("檔案 ${csvFile.name}：${result.message}")
                            Log.e(TAG, "CSV檔案 ${csvFile.name} 匯入失敗：${result.message}")
                        }
                    }

                } catch (e: Exception) {
                    val errorMsg = "檔案 ${csvFile.name} 處理失敗：${e.message}"
                    allErrors.add(errorMsg)
                    Log.e(TAG, errorMsg, e)
                }
            }

            // 處理結果
            return when {
                allResults.isEmpty() -> {
                    ImportResult.Error(
                        "所有CSV檔案匯入失敗\n\n錯誤詳情：\n${allErrors.joinToString("\n")}"
                    )
                }
                allResults.size == 1 -> {
                    // 只有一個成功的結果，直接返回
                    val result = allResults[0]
                    if (allErrors.isNotEmpty()) {
                        // 有部分錯誤，添加到錯誤列表中
                        result.copy(errors = result.errors + allErrors)
                    } else {
                        result
                    }
                }
                else -> {
                    // 多個成功結果，合併為一個總結
                    val totalSuccess = allResults.sumOf { it.successCount }
                    val totalErrors = allResults.sumOf { it.errorCount } + allErrors.size
                    val totalCount = totalSuccess + totalErrors

                    ImportResult.Success(
                        deckId = "multiple_decks",
                        deckName = "批次匯入結果",
                        totalCount = totalCount,
                        successCount = totalSuccess,
                        errorCount = totalErrors,
                        errors = allErrors + allResults.flatMap { it.errors }
                    )
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "多檔案CSV匯入失敗", e)
            return ImportResult.Error("批次匯入失敗：${e.message}")
        }
    }

    /**
     * 從JSON檔案匯入
     */
    private suspend fun importFromJson(inputStream: InputStream): ImportResult {
        val jsonContent = inputStream.bufferedReader().use { it.readText() }
        return parseJsonAndImport(jsonContent, null)
    }

    /**
     * 從CSV檔案匯入（帶圖片目錄支援）
     */
    private suspend fun importFromCsvWithImages(inputStream: InputStream, fileName: String = "", imagesDir: File?): ImportResult {
        try {
            Log.d(TAG, "=== 開始CSV匯入（帶圖片支援）===")
            Log.d(TAG, "檔案名稱: $fileName")
            Log.d(TAG, "圖片目錄: ${imagesDir?.absolutePath}")

            val csvContent = inputStream.bufferedReader().use { it.readText() }
            Log.d(TAG, "CSV內容長度: ${csvContent.length}")
            Log.d(TAG, "CSV前100字符: ${csvContent.take(100)}")

            // 檢查編碼問題
            val hasEncodingIssues = csvContent.contains("") ||
                                   csvContent.contains("??") ||
                                   csvContent.take(100).count { it.code > 127 } > csvContent.take(100).length * 0.5

            if (hasEncodingIssues) {
                Log.w(TAG, "檢測到可能的編碼問題")
                return ImportResult.Error(
                    "CSV檔案「$fileName」可能有編碼問題\n\n" +
                    "❌ 檔案內容包含亂碼字符\n\n" +
                    "💡 請確認：\n" +
                    "   • 檔案是否使用UTF-8編碼保存\n" +
                    "   • 檔案是否在傳輸過程中損壞\n" +
                    "   • 嘗試重新創建CSV檔案\n\n" +
                    "📋 檔案前100字符：\n${csvContent.take(100)}"
                )
            }

            if (csvContent.isBlank()) {
                return ImportResult.Error("CSV檔案「$fileName」為空或無法讀取")
            }

            // 解析CSV內容
            val lines = csvContent.lines().filter { it.isNotBlank() }
            if (lines.isEmpty()) {
                return ImportResult.Error("CSV檔案沒有有效內容")
            }

            if (lines.size < 2) {
                return ImportResult.Error("CSV檔案至少需要標題行和一行資料")
            }

            // 解析標題行
            val headers = try {
                parseCsvLine(lines[0])
            } catch (e: Exception) {
                Log.e(TAG, "解析CSV標題行失敗", e)
                return ImportResult.Error("CSV檔案「$fileName」標題行格式錯誤：${e.message}\n\n標題行內容：${lines[0]}")
            }

            Log.d(TAG, "CSV標題: $headers")

            if (headers.isEmpty()) {
                return ImportResult.Error("CSV檔案「$fileName」標題行為空")
            }

            // 檢查是否至少有題目相關欄位（題目文字或題目圖片）
            val questionTextVariants = listOf("題目文字", "題目", "question", "questionText", "問題")
            val questionImageVariants = listOf("題目圖片", "圖片檔名", "questionImage", "image", "圖片")

            val hasQuestionText = questionTextVariants.any { variant ->
                headers.any { it.equals(variant, ignoreCase = true) }
            }
            val hasQuestionImage = questionImageVariants.any { variant ->
                headers.any { it.equals(variant, ignoreCase = true) }
            }

            if (!hasQuestionText && !hasQuestionImage) {
                return ImportResult.Error(
                    "CSV檔案「$fileName」缺少題目欄位\n\n" +
                    "❌ 至少需要以下欄位之一：\n" +
                    "   • 題目文字（或題目、question、questionText、問題）\n" +
                    "   • 題目圖片（或圖片檔名、questionImage、image、圖片）\n\n" +
                    "📋 實際欄位：${headers.joinToString(", ")}"
                )
            }

            // 檢查必要欄位
            val answerVariants = listOf("答案", "answer")
            val hasAnswer = answerVariants.any { variant ->
                headers.any { it.equals(variant, ignoreCase = true) }
            }

            if (!hasAnswer) {
                return ImportResult.Error(
                    "CSV檔案「$fileName」缺少必要欄位：答案\n\n" +
                    "❌ 缺少的欄位：答案（或answer）\n\n" +
                    "📋 實際欄位：${headers.joinToString(", ")}\n\n" +
                    "💡 請確認CSV檔案第1行包含以下必要欄位：\n" +
                    "   • 答案（或answer）"
                )
            }

            // 建立欄位索引映射
            val fieldMap = createFieldMap(headers)
            Log.d(TAG, "欄位映射: $fieldMap")

            // 直接解析CSV行並創建卡片，完全跳過JSON
            return importCsvRowsDirectly(lines, headers, fieldMap, fileName, imagesDir)

        } catch (e: Exception) {
            Log.e(TAG, "CSV匯入失敗", e)
            val errorMessage = when (e) {
                is java.io.FileNotFoundException -> "找不到檔案"
                is java.io.IOException -> "檔案讀取錯誤：${e.message}"
                is org.json.JSONException -> "資料格式錯誤：${e.message}"
                is java.lang.IllegalArgumentException -> "參數錯誤：${e.message}"
                else -> "未知錯誤：${e.message}"
            }
            return ImportResult.Error("CSV匯入失敗：$errorMessage\n\n請檢查：\n1. 檔案格式是否正確\n2. 是否包含必要欄位（題目ID、答案）\n3. 檔案編碼是否為UTF-8")
        }
    }

    /**
     * 從CSV檔案匯入（無圖片支援）
     */
    private suspend fun importFromCsv(inputStream: InputStream, fileName: String = ""): ImportResult {
        try {
            Log.d(TAG, "=== 開始CSV匯入 ===")
            Log.d(TAG, "檔案名稱: $fileName")

            val csvContent = inputStream.bufferedReader().use { it.readText() }
            Log.d(TAG, "CSV內容長度: ${csvContent.length}")
            Log.d(TAG, "CSV前100字符: ${csvContent.take(100)}")

            // 檢查編碼問題
            val hasEncodingIssues = csvContent.contains("") ||
                                   csvContent.contains("??") ||
                                   csvContent.take(100).count { it.code > 127 } > csvContent.take(100).length * 0.5

            if (hasEncodingIssues) {
                Log.w(TAG, "檢測到可能的編碼問題")
                return ImportResult.Error(
                    "CSV檔案「$fileName」可能有編碼問題\n\n" +
                    "❌ 檔案內容包含亂碼字符\n\n" +
                    "💡 請確認：\n" +
                    "   • 檔案是否使用UTF-8編碼保存\n" +
                    "   • 檔案是否在傳輸過程中損壞\n" +
                    "   • 嘗試重新創建CSV檔案\n\n" +
                    "📋 檔案前100字符：\n${csvContent.take(100)}"
                )
            }

            if (csvContent.isBlank()) {
                return ImportResult.Error("CSV檔案「$fileName」為空或無法讀取")
            }

            // 解析CSV內容
            val lines = csvContent.lines().filter { it.isNotBlank() }
            if (lines.isEmpty()) {
                return ImportResult.Error("CSV檔案沒有有效內容")
            }

            if (lines.size < 2) {
                return ImportResult.Error("CSV檔案至少需要標題行和一行資料")
            }

            // 解析標題行
            val headers = try {
                parseCsvLine(lines[0])
            } catch (e: Exception) {
                Log.e(TAG, "解析CSV標題行失敗", e)
                return ImportResult.Error("CSV檔案「$fileName」標題行格式錯誤：${e.message}\n\n標題行內容：${lines[0]}")
            }

            Log.d(TAG, "CSV標題: $headers")

            if (headers.isEmpty()) {
                return ImportResult.Error("CSV檔案「$fileName」標題行為空")
            }

            // 檢查是否至少有題目相關欄位（題目文字或題目圖片）
            val questionTextVariants = listOf("題目文字", "題目", "question", "questionText", "問題")
            val questionImageVariants = listOf("題目圖片", "圖片檔名", "questionImage", "image", "圖片")

            val hasQuestionText = questionTextVariants.any { variant ->
                headers.any { it.equals(variant, ignoreCase = true) }
            }
            val hasQuestionImage = questionImageVariants.any { variant ->
                headers.any { it.equals(variant, ignoreCase = true) }
            }

            if (!hasQuestionText && !hasQuestionImage) {
                val errorMessage = buildString {
                    append("CSV檔案格式錯誤：缺少題目相關欄位\n\n")
                    append("❌ 至少需要以下其中一個欄位：\n")
                    append("   • 題目文字（或題目、question、questionText、問題）\n")
                    append("   • 題目圖片（或圖片檔名、questionImage、image、圖片）\n\n")
                    append("📋 實際欄位（第1行）：\n")
                    headers.forEachIndexed { index, header ->
                        append("   第${index + 1}列：$header\n")
                    }
                    append("\n💡 其他欄位都是可選的：\n")
                    append("   • 題目ID - 可選（系統會自動生成）\n")
                    append("   • 答案 - 可選（可以後續用AI生成）\n")
                    append("   • 標籤、難度、說明 - 可選")
                }
                return ImportResult.Error(errorMessage)
            }

            // 建立欄位索引映射
            val fieldMap = createFieldMap(headers)
            Log.d(TAG, "欄位映射: $fieldMap")

            // 解析資料行
            val questions = mutableListOf<JSONObject>()
            val errors = mutableListOf<String>()

            for (i in 1 until lines.size) {
                try {
                    val values = parseCsvLine(lines[i])
                    if (values.size != headers.size) {
                        errors.add("第${i+1}行欄位數量不匹配：期望${headers.size}個欄位，實際${values.size}個欄位")
                        continue
                    }

                    val questionObj = createQuestionFromCsv(values, fieldMap)
                    questions.add(questionObj)

                } catch (e: Exception) {
                    val errorMsg = when {
                        e.message?.contains("至少需要題目文字或題目圖片") == true ->
                            "第${i+1}行錯誤：題目文字和題目圖片都為空，至少需要其中一個"
                        e.message?.contains("題目ID") == true ->
                            "第${i+1}行錯誤：題目ID欄位格式錯誤"
                        else ->
                            "第${i+1}行解析失敗：${e.message}"
                    }
                    errors.add(errorMsg)
                    Log.e(TAG, "解析第${i+1}行失敗", e)
                }
            }

            if (questions.isEmpty()) {
                val errorMessage = buildString {
                    append("CSV檔案「$fileName」沒有有效的題目資料\n\n")
                    append("📊 處理統計：\n")
                    append("   • 總行數：${lines.size}\n")
                    append("   • 標題行：1行\n")
                    append("   • 資料行：${lines.size - 1}行\n")
                    append("   • 成功解析：0行\n")
                    append("   • 解析錯誤：${errors.size}行\n\n")

                    if (errors.isNotEmpty()) {
                        append("❌ 錯誤詳情：\n")
                        errors.take(5).forEach { error ->
                            append("   • $error\n")
                        }
                        if (errors.size > 5) {
                            append("   • ...還有${errors.size - 5}個錯誤\n")
                        }
                        append("\n")
                    }

                    append("💡 請檢查：\n")
                    append("   • 檔案編碼是否為UTF-8\n")
                    append("   • 每行至少要有題目文字或題目圖片其中一個\n")
                    append("   • CSV格式是否正確\n")
                    append("   • 題目ID和答案都是可選的，系統會自動處理")
                }
                return ImportResult.Error(errorMessage)
            }

            // 建立題庫JSON物件
            val deckName = extractDeckNameFromFileName(fileName)
            val questionBankJson = JSONObject().apply {
                put("version", "1.0")
                put("deckName", deckName)
                put("deckDescription", "從CSV檔案「${fileName}」匯入的題庫")
                put("subject", detectSubjectFromName(deckName))
                put("createdAt", System.currentTimeMillis())
                put("questions", JSONArray(questions))
            }

            Log.d(TAG, "CSV解析完成，共${questions.size}個題目，${errors.size}個錯誤")

            // 對於單獨的CSV檔案，檢查是否有同名的圖片目錄
            // 例如：數學-基礎代數.csv 對應 數學-基礎代數_images/ 目錄
            val csvBaseName = fileName.substringBeforeLast(".")
            val possibleImageDirs = listOf(
                "${csvBaseName}_images",
                "${csvBaseName}_圖片",
                "images",
                "圖片"
            )

            var imagesDir: File? = null

            // 嘗試在CSV檔案的同級目錄中尋找圖片目錄
            // 注意：對於單獨的CSV檔案，我們無法直接訪問其父目錄
            // 所以這裡主要是為ZIP檔案中的CSV提供支援
            Log.d(TAG, "檢查可能的圖片目錄：$possibleImageDirs")

            // 直接解析CSV行並創建卡片，完全跳過JSON
            return importCsvRowsDirectly(lines, headers, fieldMap, fileName, imagesDir)

        } catch (e: Exception) {
            Log.e(TAG, "CSV匯入失敗", e)
            val errorMessage = when (e) {
                is java.io.FileNotFoundException -> "找不到檔案"
                is java.io.IOException -> "檔案讀取錯誤：${e.message}"
                is org.json.JSONException -> "資料格式錯誤：${e.message}"
                is java.lang.IllegalArgumentException -> "參數錯誤：${e.message}"
                else -> "未知錯誤：${e.message}"
            }
            return ImportResult.Error("CSV匯入失敗：$errorMessage\n\n請檢查：\n1. 檔案格式是否正確\n2. 是否包含必要欄位（題目ID、答案）\n3. 檔案編碼是否為UTF-8")
        }
    }
    
    /**
     * 解析並匯入題庫
     */
    private fun parseJsonAndImport(jsonContent: String, imagesDir: File?): ImportResult {
        try {
            val jsonObject = JSONObject(jsonContent)

            // 檢查是單一卡組備份還是舊格式
            if (jsonObject.has("type") && jsonObject.getString("type") == "deck_backup") {
                // 新的單一卡組分享格式
                return parseSingleDeckBackup(jsonObject, imagesDir)
            } else {
                // 舊的或通用的JSON格式
                return parseLegacyJson(jsonObject, imagesDir)
            }

        } catch (e: Exception) {
            Log.e(TAG, "JSON解析失敗", e)
            return ImportResult.Error("JSON格式錯誤：${e.message}")
        }
    }

    /**
     * 解析新的單一卡組備份格式
     */
    private fun parseSingleDeckBackup(jsonObject: JSONObject, imagesDir: File?): ImportResult {
        val deckObject = jsonObject.getJSONObject("deck")
        val deckName = deckObject.optString("name", "匯入的卡組")
        val deckDescription = deckObject.optString("description", "分享匯入的卡組")
        val cardsArray = deckObject.getJSONArray("cards")

        val dataManager = DeckDataManager(context)
        val imageManager = ImageStorageManager(context)

        // 檢查同名卡組
        val finalDeckName = findAvailableDeckName(deckName, dataManager)
        val deckId = dataManager.addDeck(finalDeckName, deckDescription)
        Log.d(TAG, "✅ 創建新卡組：$finalDeckName (ID: $deckId)")

        var successCount = 0
        var errorCount = 0
        val errors = mutableListOf<String>()

        for (i in 0 until cardsArray.length()) {
            try {
                val cardJson = cardsArray.getJSONObject(i)
                val card = createCardDirectlyFromJson(cardJson, deckId, imagesDir, imageManager)
                dataManager.addCard(card)
                successCount++
            } catch (e: Exception) {
                errorCount++
                errors.add("卡片 ${i + 1}: ${e.message}")
                Log.e(TAG, "解析卡片失敗", e)
            }
        }

        // 更新卡組數量
        dataManager.loadDecks().find { it.id == deckId }?.let {
            dataManager.updateDeck(it.copy(cardCount = successCount))
        }

        return ImportResult.Success(
            deckId = deckId,
            deckName = finalDeckName,
            totalCount = cardsArray.length(),
            successCount = successCount,
            errorCount = errorCount,
            errors = errors
        )
    }

    /**
     * 尋找一個可用的卡組名稱，如果同名已存在則添加後綴
     */
    private fun findAvailableDeckName(baseName: String, dataManager: DeckDataManager): String {
        val existingDecks = dataManager.loadDecks()
        if (existingDecks.none { it.name == baseName }) {
            return baseName
        }

        var newName: String
        var count = 1
        do {
            newName = "$baseName (${count++})"
        } while (existingDecks.any { it.name == newName })

        Log.w(TAG, "⚠️ 發現同名卡組 '$baseName'，已重新命名為 '$newName'")
        return newName
    }


    /**
     * 解析舊的或通用的JSON格式
     */
    private fun parseLegacyJson(jsonObject: JSONObject, imagesDir: File?): ImportResult {
        val deckName = jsonObject.optString("deckName", "匯入的題庫")
        val deckDescription = jsonObject.optString("deckDescription", "批次匯入的題庫")
        val questionsArray = jsonObject.getJSONArray("questions")

        val dataManager = DeckDataManager(context)
        val imageManager = ImageStorageManager(context)

        // 檢查是否已存在同名卡組
        val finalDeckName = findAvailableDeckName(deckName, dataManager)
        val deckId = dataManager.addDeck(finalDeckName, if (deckDescription.isNotEmpty()) deckDescription else "批次匯入：$finalDeckName")
        Log.d(TAG, "✅ 創建新卡組：$finalDeckName (ID: $deckId)")

        var successCount = 0
        var errorCount = 0
        val errors = mutableListOf<String>()

        // 處理每個題目
        for (i in 0 until questionsArray.length()) {
            try {
                val questionObj = questionsArray.getJSONObject(i)
                val card = parseQuestionToCard(questionObj, deckId, imagesDir, imageManager)
                dataManager.addCard(card)
                successCount++
            } catch (e: Exception) {
                errorCount++
                errors.add("題目 ${i + 1}: ${e.message}")
                Log.e(TAG, "解析題目失敗", e)
            }
        }

        // 更新卡組卡片數量
        dataManager.loadDecks().find { it.id == deckId }?.let {
             dataManager.updateDeck(it.copy(cardCount = successCount))
        }

        return ImportResult.Success(
            deckId = deckId,
            deckName = finalDeckName,
            totalCount = questionsArray.length(),
            successCount = successCount,
            errorCount = errorCount,
            errors = errors
        )
    }
    
    /**
     * 將JSON題目轉換為StudyCard
     */
    private fun parseQuestionToCard(
        questionObj: JSONObject,
        deckId: String,
        imagesDir: File?,
        imageManager: ImageStorageManager
    ): StudyCard {
        val questionText = questionObj.optString("questionText", "")
        val questionImageName = questionObj.optString("questionImage", "").takeIf { it.isNotEmpty() }
        val answer = questionObj.optString("answer", "")
        val answerImageName = questionObj.optString("answerImage", "").takeIf { it.isNotEmpty() }

        Log.d(TAG, "=== parseQuestionToCard ===")
        Log.d(TAG, "questionText: '$questionText'")
        Log.d(TAG, "questionImageName: '$questionImageName'")
        Log.d(TAG, "answer: '$answer'")
        Log.d(TAG, "answerImageName: '$answerImageName'")
        Log.d(TAG, "imagesDir: ${imagesDir?.absolutePath}")

        // 處理標籤
        val tagsArray = questionObj.optJSONArray("tags")
        val tags = mutableListOf<String>()
        if (tagsArray != null) {
            for (i in 0 until tagsArray.length()) {
                tags.add(tagsArray.getString(i))
            }
        }

        // 處理難度
        val difficultyStr = questionObj.optString("difficulty", "NORMAL")
        val difficulty = try {
            CardDifficulty.valueOf(difficultyStr)
        } catch (e: Exception) {
            CardDifficulty.NORMAL
        }

        // 構建圖文混合內容格式
        val questionContent = buildRichContent(questionText, questionImageName, imagesDir, imageManager)
        val answerContent = buildRichContent(answer, answerImageName, imagesDir, imageManager)

        return StudyCard(
            id = DeckDataManager(context).generateNewCardId(),
            deckId = deckId,
            question = questionContent,
            answer = answerContent,
            aiAnswer = "", // 批次匯入時AI解答為空
            tags = tags,
            difficulty = difficulty
        )
    }

    /**
     * 直接從CSV行創建卡片，完全跳過JSON
     */
    private suspend fun importCsvRowsDirectly(
        lines: List<String>,
        headers: List<String>,
        fieldMap: Map<String, Int>,
        fileName: String,
        imagesDir: File?
    ): ImportResult {
        try {
            Log.d(TAG, "=== 完全直接CSV匯入開始 ===")
            Log.d(TAG, "檔案名稱: $fileName")
            Log.d(TAG, "資料行數: ${lines.size - 1}")
            Log.d(TAG, "圖片目錄: ${imagesDir?.absolutePath}")

            val dataManager = DeckDataManager(context)
            val imageManager = ImageStorageManager(context)

            // 創建卡組
            val deckName = extractDeckNameFromFileName(fileName)
            val existingDecks = dataManager.loadDecks()
            val existingDeck = existingDecks.find { it.name == deckName }

            val deckId: String
            val finalDeckName: String

            if (existingDeck != null) {
                val timestamp = java.text.SimpleDateFormat("yyyyMMdd_HHmm", java.util.Locale.getDefault())
                    .format(java.util.Date())
                finalDeckName = "$deckName ($timestamp)"
                deckId = dataManager.addDeck(finalDeckName, "從CSV檔案「${fileName}」匯入的題庫")
                Log.i(TAG, "✅ 創建新卡組避免重複：$finalDeckName")
            } else {
                finalDeckName = deckName
                deckId = dataManager.addDeck(finalDeckName, "從CSV檔案「${fileName}」匯入的題庫")
                Log.d(TAG, "✅ 創建新卡組：$finalDeckName")
            }

            var successCount = 0
            var errorCount = 0
            val errors = mutableListOf<String>()

            // 直接處理每一行CSV資料
            for (i in 1 until lines.size) {
                var values: List<String> = emptyList()
                try {
                    Log.d(TAG, "=== 處理第${i}行 ===")

                    values = parseCsvLine(lines[i])
                    Log.d(TAG, "CSV行值: $values")

                    if (values.size != headers.size) {
                        errors.add("第${i+1}行欄位數量不匹配：期望${headers.size}個欄位，實際${values.size}個欄位")
                        errorCount++
                        continue
                    }

                    // 直接從CSV行創建卡片
                    val card = createCardDirectlyFromCsvRow(values, fieldMap, deckId, imagesDir, imageManager)

                    dataManager.addCard(card)
                    successCount++

                    Log.d(TAG, "✅ 第${i}行匯入成功")

                } catch (e: Exception) {
                    errorCount++

                    // 提供詳細的診斷信息
                    val detailedErrorMsg = buildDetailedErrorMessage(i+1, values, fieldMap, imagesDir, e)
                    errors.add(detailedErrorMsg)
                    Log.e(TAG, "❌ 第${i}行匯入失敗: ${e.message}", e)
                }
            }

            // 更新卡組卡片數量
            val deck = dataManager.loadDecks().find { it.id == deckId }
            if (deck != null) {
                val updatedDeck = deck.copy(cardCount = successCount)
                dataManager.updateDeck(updatedDeck)
            }

            // 如果創建了新名稱，添加說明
            val finalErrors = if (finalDeckName != deckName) {
                errors + listOf("ℹ️ 因同名卡組已存在，已創建新卡組：$finalDeckName")
            }
            else {
                errors
            }

            Log.d(TAG, "=== 完全直接CSV匯入完成 ===")
            Log.d(TAG, "成功: $successCount, 失敗: $errorCount")

            return ImportResult.Success(
                deckId = deckId,
                deckName = finalDeckName,
                totalCount = lines.size - 1,
                successCount = successCount,
                errorCount = errorCount,
                errors = finalErrors
            )

        } catch (e: Exception) {
            Log.e(TAG, "完全直接CSV匯入失敗", e)
            return ImportResult.Error("匯入失敗：${e.message}")
        }
    }

    /**
     * 直接從CSV行創建卡片
     */
    private fun createCardDirectlyFromCsvRow(
        values: List<String>,
        fieldMap: Map<String, Int>,
        deckId: String,
        imagesDir: File?,
        imageManager: ImageStorageManager
    ): StudyCard {
        Log.d(TAG, "=== 直接從CSV行創建卡片 ===")

        // 直接從CSV行提取值
        Log.d(TAG, "fieldMap內容: $fieldMap")
        Log.d(TAG, "values內容: $values")
        Log.d(TAG, "values大小: ${values.size}")

        // 詳細顯示每個欄位的原始值
        values.forEachIndexed { index, value ->
            Log.d(TAG, "原始欄位[$index]: '$value'")
        }

        val questionText = getValue(values, fieldMap, "questionText") ?: ""
        val questionImage = getValue(values, fieldMap, "questionImage") ?: ""
        val answer = getValue(values, fieldMap, "answer") ?: ""
        val answerImage = getValue(values, fieldMap, "answerImage") ?: ""

        Log.d(TAG, "提取的值:")
        Log.d(TAG, "  questionText: '$questionText'")
        Log.d(TAG, "  questionImage: '$questionImage'")
        Log.d(TAG, "  answer: '$answer'")
        Log.d(TAG, "  answerImage: '$answerImage'")

        // 驗證至少有題目文字或圖片
        Log.d(TAG, "驗證題目內容：")
        Log.d(TAG, "  questionText.isEmpty() = ${questionText.isEmpty()}")
        Log.d(TAG, "  questionImage.isEmpty() = ${questionImage.isEmpty()}")

        if (questionText.isEmpty() && questionImage.isEmpty()) {
            Log.e(TAG, "❌ 題目文字和圖片都為空")
            throw IllegalArgumentException("至少需要題目文字或題目圖片其中一個")
        }

        // 特別處理只有圖片的情況
        if (questionText.isEmpty() && questionImage.isNotEmpty()) {
            Log.i(TAG, "✅ 檢測到只有圖片的題目，圖片檔案：$questionImage")
            Log.i(TAG, "   這個題目應該會被正常處理")
        }

        // 記錄題目類型
        when {
            questionText.isNotEmpty() && questionImage.isNotEmpty() -> {
                Log.i(TAG, "✅ 文字+圖片題目")
            }
            questionText.isNotEmpty() && questionImage.isEmpty() -> {
                Log.i(TAG, "✅ 純文字題目")
            }
            questionText.isEmpty() && questionImage.isNotEmpty() -> {
                Log.i(TAG, "✅ 純圖片題目: $questionImage")
            }
        }

        // 處理標籤
        val tagsStr = getValue(values, fieldMap, "tags") ?: ""
        val tags = if (tagsStr.isNotEmpty()) {
            tagsStr.split(",", "，", ";", "；").map { it.trim() }.filter { it.isNotEmpty() }
        } else {
            emptyList()
        }

        // 處理熟練度（支援數字1-5和文字描述）
        val masteryStr = getValue(values, fieldMap, "mastery") ?: getValue(values, fieldMap, "difficulty") ?: "3"
        val mastery = when (masteryStr.uppercase()) {
            // 數字級別 1-5
            "1" -> CardMastery.LEVEL_1
            "2" -> CardMastery.LEVEL_2
            "3" -> CardMastery.LEVEL_3
            "4" -> CardMastery.LEVEL_4
            "5" -> CardMastery.LEVEL_5
            // 文字描述
            "完全不熟" -> CardMastery.LEVEL_1
            "有點不熟" -> CardMastery.LEVEL_2
            "普通" -> CardMastery.LEVEL_3
            "有點熟" -> CardMastery.LEVEL_4
            "非常熟" -> CardMastery.LEVEL_5
            // 向後兼容舊的描述
            "未學習", "NOT_LEARNED" -> CardMastery.LEVEL_1
            "初學", "BEGINNER" -> CardMastery.LEVEL_1
            "學習中", "LEARNING" -> CardMastery.LEVEL_2
            "熟悉", "FAMILIAR" -> CardMastery.LEVEL_3
            "熟練", "PROFICIENT" -> CardMastery.LEVEL_4
            "精通", "MASTERED" -> CardMastery.LEVEL_5
            // 向後兼容舊的難度值
            "簡單", "EASY" -> CardMastery.LEVEL_5
            "NORMAL" -> CardMastery.LEVEL_3  // 移除重複的"普通"
            "困難", "HARD" -> CardMastery.LEVEL_2
            "非常困難", "VERY_HARD" -> CardMastery.LEVEL_1
            else -> CardMastery.LEVEL_3 // 默認為普通
        }

        // 保持向後兼容的難度
        val difficulty = when (mastery) {
            CardMastery.LEVEL_1 -> CardDifficulty.VERY_HARD
            CardMastery.LEVEL_2 -> CardDifficulty.HARD
            CardMastery.LEVEL_3 -> CardDifficulty.NORMAL
            CardMastery.LEVEL_4, CardMastery.LEVEL_5 -> CardDifficulty.EASY
        }

        // 直接構建內容
        val questionContent = buildRichContentDirectly(questionText, questionImage.takeIf { it.isNotEmpty() }, imagesDir, imageManager)
        val answerContent = buildRichContentDirectly(answer, answerImage.takeIf { it.isNotEmpty() }, imagesDir, imageManager)

        Log.d(TAG, "構建的內容:")
        Log.d(TAG, "  questionContent: $questionContent")
        Log.d(TAG, "  answerContent: $answerContent")

        return StudyCard(
            id = DeckDataManager(context).generateNewCardId(),
            deckId = deckId,
            question = questionContent,
            answer = answerContent,
            aiAnswer = "",
            tags = tags,
            difficulty = difficulty,
            mastery = mastery
        )
    }

    /**
     * 直接從JSON物件創建卡片
     */
    private fun createCardDirectlyFromJson(
        questionObj: JSONObject,
        deckId: String,
        imagesDir: File?,
        imageManager: ImageStorageManager
    ): StudyCard {
        val questionText = questionObj.optString("questionText", "")
        val questionImageName = questionObj.optString("questionImage", "").takeIf { it.isNotEmpty() }
        val answer = questionObj.optString("answer", "")
        val answerImageName = questionObj.optString("answerImage", "").takeIf { it.isNotEmpty() }

        Log.d(TAG, "=== 直接創建卡片 ===")
        Log.d(TAG, "questionText: '$questionText'")
        Log.d(TAG, "questionImageName: '$questionImageName'")
        Log.d(TAG, "answer: '$answer'")
        Log.d(TAG, "answerImageName: '$answerImageName'")
        Log.d(TAG, "imagesDir: ${imagesDir?.absolutePath}")

        // 處理標籤
        val tagsArray = questionObj.optJSONArray("tags")
        val tags = mutableListOf<String>()
        if (tagsArray != null) {
            for (i in 0 until tagsArray.length()) {
                tags.add(tagsArray.getString(i))
            }
        }

        // 處理難度
        val difficultyStr = questionObj.optString("difficulty", "NORMAL")
        val difficulty = try {
            CardDifficulty.valueOf(difficultyStr)
        } catch (e: Exception) {
            CardDifficulty.NORMAL
        }

        // 直接構建圖文混合內容格式
        val questionContent = buildRichContentDirectly(questionText, questionImageName, imagesDir, imageManager)
        val answerContent = buildRichContentDirectly(answer, answerImageName, imagesDir, imageManager)

        Log.d(TAG, "questionContent: $questionContent")
        Log.d(TAG, "answerContent: $answerContent")

        return StudyCard(
            id = DeckDataManager(context).generateNewCardId(),
            deckId = deckId,
            question = questionContent,
            answer = answerContent,
            aiAnswer = "", // 批次匯入時AI解答為空
            tags = tags,
            difficulty = difficulty
        )
    }

    /**
     * 構建詳細的錯誤診斷信息
     */
    private fun buildDetailedErrorMessage(
        rowNumber: Int,
        values: List<String>,
        fieldMap: Map<String, Int>,
        imagesDir: File?,
        exception: Exception
    ): String {
        val diagnosis = StringBuilder()
        diagnosis.append("第${rowNumber}行匯入失敗\n")

        // 顯示原始資料
        diagnosis.append("\n📋 原始資料：\n")
        values.forEachIndexed { index, value ->
            diagnosis.append("  欄位${index + 1}: '$value'\n")
        }

        // 解析欄位值
        val questionText = getValue(values, fieldMap, "questionText") ?: ""
        val questionImage = getValue(values, fieldMap, "questionImage") ?: ""
        val answer = getValue(values, fieldMap, "answer") ?: ""

        diagnosis.append("\n🔍 解析結果：\n")
        diagnosis.append("  題目文字: '$questionText'\n")
        diagnosis.append("  題目圖片: '$questionImage'\n")
        diagnosis.append("  答案: '$answer'\n")

        // 檢查題目內容
        diagnosis.append("\n✅ 內容檢查：\n")
        if (questionText.isEmpty() && questionImage.isEmpty()) {
            diagnosis.append("  ❌ 題目文字和圖片都為空\n")
        } else if (questionText.isEmpty() && questionImage.isNotEmpty()) {
            diagnosis.append("  ✅ 只有圖片的題目（應該有效）\n")

            // 詳細檢查圖片檔案
            diagnosis.append("\n🖼️ 圖片檔案檢查：\n")
            diagnosis.append("  圖片檔名: '$questionImage'\n")

            if (imagesDir != null) {
                diagnosis.append("  圖片目錄: ${imagesDir.absolutePath}\n")
                diagnosis.append("  目錄存在: ${imagesDir.exists()}\n")

                if (imagesDir.exists()) {
                    val imageFile = File(imagesDir, questionImage)
                    diagnosis.append("  完整路徑: ${imageFile.absolutePath}\n")
                    diagnosis.append("  檔案存在: ${imageFile.exists()}\n")

                    if (imageFile.exists()) {
                        diagnosis.append("  檔案大小: ${imageFile.length()} bytes\n")

                        // 嘗試解碼圖片
                        try {
                            val bitmap = BitmapFactory.decodeFile(imageFile.absolutePath)
                            if (bitmap != null) {
                                diagnosis.append("  圖片解碼: ✅ 成功 (${bitmap.width}x${bitmap.height})\n")
                                diagnosis.append("  ❓ 圖片檔案正常，但匯入失敗的原因：${exception.message}\n")
                            } else {
                                diagnosis.append("  圖片解碼: ❌ 失敗（檔案可能損壞）\n")
                            }
                        } catch (e: Exception) {
                            diagnosis.append("  圖片解碼: ❌ 異常 - ${e.message}\n")
                        }
                    } else {
                        diagnosis.append("  ❌ 找不到圖片檔案！\n")

                        // 列出目錄中的所有檔案
                        val files = imagesDir.listFiles()
                        if (files != null && files.isNotEmpty()) {
                            diagnosis.append("  📁 目錄中的檔案：\n")
                            files.forEach { file ->
                                diagnosis.append("    - ${file.name}\n")
                            }
                        } else {
                            diagnosis.append("  📁 圖片目錄為空\n")
                        }
                    }
                } else {
                    diagnosis.append("  ❌ 圖片目錄不存在\n")
                }
            } else {
                diagnosis.append("  ❌ 沒有提供圖片目錄\n")
            }
        } else if (questionText.isNotEmpty() && questionImage.isEmpty()) {
            diagnosis.append("  ✅ 只有文字的題目（應該有效）\n")
        } else {
            diagnosis.append("  ✅ 有文字和圖片的題目（應該有效）\n")
        }

        // 顯示具體錯誤
        diagnosis.append("\n❌ 具體錯誤：\n")
        diagnosis.append("  ${exception.message}\n")

        return diagnosis.toString()
    }

    /**
     * 直接構建圖文混合內容格式
     */
    private fun buildRichContentDirectly(
        text: String,
        imageName: String?,
        imagesDir: File?,
        imageManager: ImageStorageManager
    ): String {
        Log.d(TAG, "=== 直接構建內容 ===")
        Log.d(TAG, "text: '$text'")
        Log.d(TAG, "imageName: '$imageName'")

        val contentList = mutableListOf<Map<String, String>>()

        // 添加文字內容
        if (text.isNotEmpty()) {
            contentList.add(mapOf("type" to "text", "content" to text))
            Log.d(TAG, "✅ 添加文字內容")
        }

        // 添加圖片內容
        if (!imageName.isNullOrEmpty()) {
            Log.d(TAG, "開始處理圖片：$imageName")

            if (imagesDir != null && imagesDir.exists()) {
                val imageFile = File(imagesDir, imageName)
                Log.d(TAG, "圖片路徑：${imageFile.absolutePath}")
                Log.d(TAG, "圖片存在：${imageFile.exists()}")

                if (imageFile.exists()) {
                    val bitmap = BitmapFactory.decodeFile(imageFile.absolutePath)
                    if (bitmap != null) {
                        val savedImagePath = imageManager.saveImage(bitmap)
                        if (savedImagePath != null) {
                            val fileName = savedImagePath.substringAfterLast("/")
                            contentList.add(mapOf("type" to "image", "content" to fileName))
                            Log.d(TAG, "✅ 圖片處理成功：$fileName")
                        } else {
                            contentList.add(mapOf("type" to "text", "content" to "[圖片保存失敗: $imageName]"))
                            Log.e(TAG, "❌ 圖片保存失敗")
                        }
                    } else {
                        contentList.add(mapOf("type" to "text", "content" to "[圖片解碼失敗: $imageName]"))
                        Log.e(TAG, "❌ 圖片解碼失敗")
                    }
                } else {
                    contentList.add(mapOf("type" to "text", "content" to "[圖片檔案不存在: $imageName]"))
                    Log.w(TAG, "⚠️ 圖片檔案不存在")
                }
            } else {
                contentList.add(mapOf("type" to "text", "content" to "[圖片目錄不存在: $imageName]"))
                Log.w(TAG, "⚠️ 圖片目錄不存在")
            }
        }

        // 確保至少有一些內容
        if (contentList.isEmpty()) {
            Log.w(TAG, "⚠️ 沒有任何內容，創建空佔位符")
            contentList.add(mapOf("type" to "text", "content" to "[空內容]"))
        }

        // 轉換為JSON字串
        return try {
            val jsonArray = org.json.JSONArray()
            contentList.forEach { item ->
                val jsonObject = org.json.JSONObject()
                jsonObject.put("type", item["type"])
                jsonObject.put("content", item["content"])
                jsonArray.put(jsonObject)
            }
            val result = jsonArray.toString()
            Log.d(TAG, "✅ 內容構建完成：$result")
            result
        } catch (e: Exception) {
            Log.e(TAG, "❌ JSON構建失敗", e)
            "[{\"type\":\"text\",\"content\":\"[內容構建失敗]\"}]"
        }
    }

    /**
     * 構建圖文混合內容格式
     */
    private fun buildRichContent(
        text: String,
        imageName: String?,
        imagesDir: File?,
        imageManager: ImageStorageManager
    ): String {
        val contentList = mutableListOf<Map<String, String>>()

        // 添加文字內容
        if (text.isNotEmpty()) {
            contentList.add(mapOf("type" to "text", "content" to text))
        }

        // 添加圖片內容
        if (!imageName.isNullOrEmpty()) {
            Log.d(TAG, "處理圖片：$imageName")
            Log.d(TAG, "圖片目錄：${imagesDir?.absolutePath}")

            if (imagesDir != null) {
                val imageFile = File(imagesDir, imageName)
                Log.d(TAG, "圖片完整路徑：${imageFile.absolutePath}")
                Log.d(TAG, "圖片檔案是否存在：${imageFile.exists()}")

                if (imageFile.exists()) {
                    Log.d(TAG, "圖片檔案大小：${imageFile.length()} bytes")

                    val bitmap = BitmapFactory.decodeFile(imageFile.absolutePath)
                    if (bitmap != null) {
                        Log.d(TAG, "圖片解碼成功：${bitmap.width}x${bitmap.height}")

                        val savedImagePath = imageManager.saveImage(bitmap)
                        if (savedImagePath != null) {
                            // 提取檔案名稱（不含路徑）
                            val fileName = savedImagePath.substringAfterLast("/")
                            contentList.add(mapOf("type" to "image", "content" to fileName))
                            Log.d(TAG, "✅ 成功添加圖片：$fileName")
                        } else {
                            Log.e(TAG, "❌ 圖片保存失敗：$imageName")
                            // 圖片保存失敗時也添加佔位符
                            contentList.add(mapOf("type" to "text", "content" to "[圖片保存失敗: $imageName]"))
                        }
                    } else {
                        Log.e(TAG, "❌ 圖片解碼失敗：$imageName")
                        // 圖片解碼失敗時也添加佔位符
                        contentList.add(mapOf("type" to "text", "content" to "[圖片解碼失敗: $imageName]"))
                    }
                } else {
                    Log.w(TAG, "⚠️ 圖片檔案不存在：$imageName")
                    Log.w(TAG, "   檢查圖片目錄內容：")
                    imagesDir.listFiles()?.forEach { file ->
                        Log.w(TAG, "   - ${file.name}")
                    }

                    // 即使圖片檔案不存在，也要添加佔位符，確保只有圖片的題目不會被忽略
                    contentList.add(mapOf("type" to "text", "content" to "[圖片檔案不存在: $imageName]"))
                    Log.w(TAG, "⚠️ 添加圖片佔位符：[圖片檔案不存在: $imageName]")
                }
            } else {
                Log.w(TAG, "⚠️ 圖片目錄為null，無法處理圖片：$imageName")
                // 圖片目錄為null時也添加佔位符
                contentList.add(mapOf("type" to "text", "content" to "[圖片目錄不存在: $imageName]"))
            }
        }

        // 如果沒有任何內容，根據原始輸入創建佔位符
        if (contentList.isEmpty()) {
            Log.w(TAG, "⚠️ 沒有任何內容（文字或圖片）")

            // 如果有圖片名稱但處理失敗，創建佔位符
            if (!imageName.isNullOrEmpty()) {
                Log.w(TAG, "⚠️ 圖片處理完全失敗，創建佔位符：$imageName")
                return "[{\"type\":\"text\",\"content\":\"[圖片處理失敗: $imageName]\"}]"
            }

            // 如果有文字但為空，創建佔位符
            if (text.isEmpty() && !imageName.isNullOrEmpty()) {
                Log.w(TAG, "⚠️ 只有圖片但處理失敗，創建佔位符")
                return "[{\"type\":\"text\",\"content\":\"[圖片: $imageName]\"}]"
            }

            // 完全沒有內容
            Log.w(TAG, "⚠️ 完全沒有內容，返回空佔位符")
            return "[{\"type\":\"text\",\"content\":\"[空內容]\"}]"
        }

        // 轉換為JSON字串
        return try {
            val jsonArray = org.json.JSONArray()
            contentList.forEach { item ->
                val jsonObject = org.json.JSONObject()
                jsonObject.put("type", item["type"])
                jsonObject.put("content", item["content"])
                jsonArray.put(jsonObject)
            }
            val result = jsonArray.toString()
            Log.d(TAG, "✅ 內容構建完成：$result")
            result
        } catch (e: Exception) {
            Log.e(TAG, "❌ 構建圖文混合內容失敗", e)
            // 降級處理：根據可用內容返回
            when {
                text.isNotEmpty() -> {
                    Log.d(TAG, "降級處理：返回純文字內容")
                    "[{\"type\":\"text\",\"content\":\"$text\"}]"
                }
                !imageName.isNullOrEmpty() -> {
                    Log.d(TAG, "降級處理：返回圖片佔位符")
                    "[{\"type\":\"text\",\"content\":\"[圖片: $imageName]\"}]"
                }
                else -> {
                    Log.w(TAG, "降級處理：返回空內容")
                    ""
                }
            }
        }
    }
    
    /**
     * 根據主題獲取圖標
     */
    private fun getSubjectIcon(subject: String): String {
        return when (subject.lowercase()) {
            "數學", "math" -> "🧮"          // 算盤，數學計算
            "物理", "physics" -> "⚡"        // 閃電，物理現象
            "化學", "chemistry" -> "🧪"      // 試管，化學實驗
            "生物", "biology" -> "🌱"        // 幼苗，生命科學
            "英文", "english" -> "🌍"        // 地球，國際語言
            "國文", "chinese" -> "🖋️"        // 鋼筆，文學書寫
            "歷史", "history" -> "🏛️"        // 古建築，歷史文明
            "地理", "geography" -> "🗺️"      // 地圖，地理探索
            "程式", "programming", "computer" -> "💻"  // 電腦，程式設計
            else -> "🎯"                    // 靶心，學習目標
        }
    }

    /**
     * 根據主題獲取顏色
     */
    private fun getSubjectColor(subject: String): String {
        return when (subject.lowercase()) {
            "數學", "math" -> "#4A90E2"
            "物理", "physics" -> "#F5A623"
            "化學", "chemistry" -> "#7ED321"
            "生物", "biology" -> "#50E3C2"
            "英文", "english" -> "#D0021B"
            "國文", "chinese" -> "#9013FE"
            "歷史", "history" -> "#8D6E63"
            "地理", "geography" -> "#4CAF50"
            "程式", "programming", "computer" -> "#FF5722"
            else -> "#6B7280"
        }
    }

    /**
     * 解析CSV行（處理逗號分隔和引號包圍）
     */
    private fun parseCsvLine(line: String): List<String> {
        val result = mutableListOf<String>()
        var current = StringBuilder()
        var inQuotes = false
        var i = 0

        while (i < line.length) {
            val char = line[i]
            when {
                char == '"' -> {
                    if (inQuotes && i + 1 < line.length && line[i + 1] == '"') {
                        // 雙引號轉義
                        current.append('"')
                        i++ // 跳過下一個引號
                    } else {
                        // 切換引號狀態
                        inQuotes = !inQuotes
                    }
                }
                char == ',' && !inQuotes -> {
                    // 欄位分隔符
                    result.add(current.toString().trim())
                    current.clear()
                }
                else -> {
                    current.append(char)
                }
            }
            i++
        }

        // 添加最後一個欄位
        result.add(current.toString().trim())

        return result
    }

    /**
     * 建立欄位索引映射
     */
    private fun createFieldMap(headers: List<String>): Map<String, Int> {
        val fieldMap = mutableMapOf<String, Int>()

        Log.d(TAG, "=== 建立固定欄位映射 ===")
        Log.d(TAG, "原始標題: $headers")
        Log.d(TAG, "標題數量: ${headers.size}")

        // 固定映射：根據您的CSV格式
        // 題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
        if (headers.size >= 8) {
            fieldMap["id"] = 0              // 題目ID
            fieldMap["questionText"] = 1    // 題目文字
            fieldMap["questionImage"] = 2   // 題目圖片
            fieldMap["answer"] = 3          // 答案
            fieldMap["answerImage"] = 4     // 答案圖片
            fieldMap["tags"] = 5            // 標籤
            fieldMap["difficulty"] = 6      // 難度
            fieldMap["explanation"] = 7     // 說明

            Log.d(TAG, "✅ 使用固定映射模式")
        } else {
            Log.w(TAG, "⚠️ 欄位數量不足8個，回退到動態映射")
            // 如果欄位數量不對，回退到原來的動態映射邏輯
            headers.forEachIndexed { index, header ->
                val normalizedHeader = header.trim().lowercase()
                when {
                    normalizedHeader.contains("題目id") || normalizedHeader.contains("id") -> {
                        fieldMap["id"] = index
                        Log.d(TAG, "  ✅ 映射到 id")
                    }

                    normalizedHeader.contains("題目文字") || normalizedHeader.contains("題目") -> {
                        fieldMap["questionText"] = index
                        Log.d(TAG, "  ✅ 映射到 questionText")
                    }
                    normalizedHeader.contains("題目圖片") || normalizedHeader.contains("圖片") -> {
                        fieldMap["questionImage"] = index
                        Log.d(TAG, "  ✅ 映射到 questionImage")
                    }
                    normalizedHeader.contains("答案") -> {
                        fieldMap["answer"] = index
                        Log.d(TAG, "  ✅ 映射到 answer")
                    }
                    normalizedHeader.contains("答案圖片") -> {
                        fieldMap["answerImage"] = index
                        Log.d(TAG, "  ✅ 映射到 answerImage")
                    }
                    normalizedHeader.contains("標籤") -> {
                        fieldMap["tags"] = index
                        Log.d(TAG, "  ✅ 映射到 tags")
                    }
                    normalizedHeader.contains("難度") -> {
                        fieldMap["difficulty"] = index
                        Log.d(TAG, "  ✅ 映射到 difficulty")
                    }
                    normalizedHeader.contains("熟練度") -> {
                        fieldMap["mastery"] = index
                        Log.d(TAG, "  ✅ 映射到 mastery")
                    }
                    normalizedHeader.contains("說明") -> {
                        fieldMap["explanation"] = index
                        Log.d(TAG, "  ✅ 映射到 explanation")
                    }
                }
            }
        }

        Log.d(TAG, "最終欄位映射: $fieldMap")
        Log.d(TAG, "映射結果詳細:")
        fieldMap.forEach { (field, index) ->
            Log.d(TAG, "  $field -> 欄位$index (${if (index < headers.size) headers[index] else "超出範圍"})")
        }
        return fieldMap
    }

    /**
     * 從CSV資料建立題目JSON物件
     */
    private fun createQuestionFromCsv(values: List<String>, fieldMap: Map<String, Int>): JSONObject {
        val questionObj = JSONObject()

        // 題目ID（可選，系統自動生成）
        val id = getValue(values, fieldMap, "id") ?: "q${System.currentTimeMillis()}_${(Math.random() * 1000).toInt()}"
        questionObj.put("id", id)

        // 題目內容（至少需要題目文字或題目圖片其中一個）
        val questionText = getValue(values, fieldMap, "questionText") ?: ""
        val questionImage = getValue(values, fieldMap, "questionImage") ?: ""

        Log.d(TAG, "=== 題目解析詳情 ===")
        Log.d(TAG, "欄位映射: $fieldMap")
        Log.d(TAG, "CSV行值: $values")
        Log.d(TAG, "questionText索引: ${fieldMap["questionText"]}")
        Log.d(TAG, "questionImage索引: ${fieldMap["questionImage"]}")
        Log.d(TAG, "解析結果 - 文字: '$questionText', 圖片: '$questionImage'")

        // 檢查具體的欄位值
        fieldMap["questionText"]?.let { index ->
            if (index < values.size) {
                Log.d(TAG, "題目文字原始值: '${values[index]}'")
            }
        }
        fieldMap["questionImage"]?.let { index ->
            if (index < values.size) {
                Log.d(TAG, "題目圖片原始值: '${values[index]}'")
            }
        }

        // 檢查是否至少有題目文字或題目圖片
        Log.d(TAG, "驗證題目內容：")
        Log.d(TAG, "  questionText.isEmpty() = ${questionText.isEmpty()}")
        Log.d(TAG, "  questionImage.isEmpty() = ${questionImage.isEmpty()}")
        Log.d(TAG, "  questionText = '$questionText'")
        Log.d(TAG, "  questionImage = '$questionImage'")

        if (questionText.isEmpty() && questionImage.isEmpty()) {
            Log.e(TAG, "❌ 題目文字和圖片都為空，跳過此題目")
            Log.e(TAG, "   這個題目將不會被匯入！")
            throw IllegalArgumentException("至少需要題目文字或題目圖片其中一個")
        }

        // 特別記錄只有圖片的情況
        if (questionText.isEmpty() && questionImage.isNotEmpty()) {
            Log.i(TAG, "✅ 檢測到只有圖片的題目：$questionImage")
            Log.i(TAG, "   這個題目應該會被正常匯入")
        }

        // 記錄有文字和圖片的情況
        if (questionText.isNotEmpty() && questionImage.isNotEmpty()) {
            Log.i(TAG, "✅ 檢測到有文字和圖片的題目：文字='$questionText', 圖片='$questionImage'")
        }

        // 記錄只有文字的情況
        if (questionText.isNotEmpty() && questionImage.isEmpty()) {
            Log.i(TAG, "✅ 檢測到只有文字的題目：$questionText")
        }

        questionObj.put("questionText", questionText)
        questionObj.put("questionImage", questionImage)

        // 答案（可選，可以為空等待AI生成）
        val answer = getValue(values, fieldMap, "answer") ?: ""
        questionObj.put("answer", answer)

        // 其他可選欄位
        questionObj.put("answerImage", getValue(values, fieldMap, "answerImage") ?: "")
        questionObj.put("explanation", getValue(values, fieldMap, "explanation") ?: "")

        // 處理標籤
        val tagsStr = getValue(values, fieldMap, "tags") ?: ""
        val tags = if (tagsStr.isNotEmpty()) {
            tagsStr.split(",", "，", ";", "；").map { it.trim() }.filter { it.isNotEmpty() }
        } else {
            emptyList()
        }
        questionObj.put("tags", JSONArray(tags))

        // 處理難度
        val difficultyStr = getValue(values, fieldMap, "difficulty") ?: "NORMAL"
        val difficulty = when (difficultyStr.uppercase()) {
            "簡單", "EASY" -> "EASY"
            "困難", "HARD" -> "HARD"
            "非常困難", "VERY_HARD" -> "VERY_HARD"
            else -> "NORMAL"
        }
        questionObj.put("difficulty", difficulty)

        return questionObj
    }

    /**
     * 從欄位映射中獲取值
     */
    private fun getValue(values: List<String>, fieldMap: Map<String, Int>, field: String): String? {
        val index = fieldMap[field] ?: return null
        return if (index < values.size) {
            val value = values[index].trim()
            Log.d(TAG, "getValue: field='$field', index=$index, rawValue='${values[index]}', trimmedValue='$value'")
            value // 去除前後空白，但保留空字串
        } else {
            Log.d(TAG, "getValue: field='$field', index=$index 超出範圍 (size=${values.size})")
            null
        }
    }

    /**
     * 從檔案名稱提取卡組名稱
     */
    private fun extractDeckNameFromFileName(fileName: String): String {
        if (fileName.isBlank()) {
            return "CSV匯入題庫"
        }

        // 移除副檔名
        val nameWithoutExtension = fileName.substringBeforeLast(".")

        // 美化檔案名稱
        val cleanName = nameWithoutExtension
            .replace("_", " ")           // 底線轉空格
            .replace("-", " - ")         // 連字號轉分隔符
            .replace(Regex("\\s+"), " ") // 多個空格合併為一個
            .trim()

        // 如果處理後為空或太短，使用預設名稱
        return if (cleanName.length >= 2) {
            cleanName
        } else {
            "CSV匯入題庫"
        }
    }

    /**
     * 從卡組名稱檢測科目
     */
    private fun detectSubjectFromName(deckName: String): String {
        val lowerName = deckName.lowercase()

        return when {
            lowerName.contains("數學") || lowerName.contains("math") -> "數學"
            lowerName.contains("物理") || lowerName.contains("physics") -> "物理"
            lowerName.contains("化學") || lowerName.contains("chemistry") -> "化學"
            lowerName.contains("生物") || lowerName.contains("biology") -> "生物"
            lowerName.contains("英文") || lowerName.contains("english") -> "英文"
            lowerName.contains("國文") || lowerName.contains("chinese") -> "國文"
            lowerName.contains("歷史") || lowerName.contains("history") -> "歷史"
            lowerName.contains("地理") || lowerName.contains("geography") -> "地理"
            lowerName.contains("程式") || lowerName.contains("programming") || lowerName.contains("computer") -> "程式"
            lowerName.contains("代數") || lowerName.contains("algebra") -> "數學"
            lowerName.contains("幾何") || lowerName.contains("geometry") -> "數學"
            lowerName.contains("微積分") || lowerName.contains("calculus") -> "數學"
            lowerName.contains("統計") || lowerName.contains("statistics") -> "數學"
            lowerName.contains("力學") || lowerName.contains("mechanics") -> "物理"
            lowerName.contains("電磁") || lowerName.contains("electromagnetic") -> "物理"
            lowerName.contains("有機") || lowerName.contains("organic") -> "化學"
            lowerName.contains("無機") || lowerName.contains("inorganic") -> "化學"
            else -> ""
        }
    }

    /**
     * 獲取檔案名稱
     */
    private fun getFileName(uri: Uri): String {
        try {
            Log.d(TAG, "=== 檔案名稱追蹤開始 ===")
            Log.d(TAG, "URI: $uri")
            Log.d(TAG, "URI Scheme: ${uri.scheme}")
            Log.d(TAG, "URI Authority: ${uri.authority}")
            Log.d(TAG, "URI Path: ${uri.path}")
            Log.d(TAG, "URI LastPathSegment: ${uri.lastPathSegment}")

            val cursor = context.contentResolver.query(uri, null, null, null, null)
            val fileName = cursor?.use {
                if (it.moveToFirst()) {
                    val nameIndex = it.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                    if (nameIndex >= 0) {
                        val name = it.getString(nameIndex)
                        Log.d(TAG, "✅ 從ContentResolver獲取檔案名稱: '$name'")

                        // 特別檢查是否為"國小數學.csv"
                        if (name == "國小數學.csv") {
                            Log.w(TAG, "⚠️ 檢測到'國小數學.csv'檔案！")
                            Log.w(TAG, "   這可能是：")
                            Log.w(TAG, "   1. 用戶實際選擇了此檔案")
                            Log.w(TAG, "   2. 檔案選擇器快取問題")
                            Log.w(TAG, "   3. 從其他應用分享的檔案")
                            Log.w(TAG, "   4. 雲端下載的檔案")

                            // 記錄更多詳細信息
                            Log.w(TAG, "   URI詳細信息:")
                            Log.w(TAG, "   - Scheme: ${uri.scheme}")
                            Log.w(TAG, "   - Authority: ${uri.authority}")
                            Log.w(TAG, "   - Path: ${uri.path}")

                            // 檢查是否有其他可用的欄位
                            for (i in 0 until it.columnCount) {
                                val columnName = it.getColumnName(i)
                                val columnValue = it.getString(i)
                                Log.w(TAG, "   - Column[$i]: $columnName = '$columnValue'")
                            }
                        }

                        name
                    } else {
                        Log.w(TAG, "無法獲取DISPLAY_NAME欄位")
                        "unknown"
                    }
                } else {
                    Log.w(TAG, "Cursor為空")
                    "unknown"
                }
            } ?: run {
                Log.w(TAG, "無法查詢ContentResolver")
                "unknown"
            }

            // 如果無法從ContentResolver獲取，嘗試從URI路徑獲取
            if (fileName == "unknown") {
                val pathSegments = uri.pathSegments
                val lastSegment = pathSegments?.lastOrNull()
                Log.d(TAG, "嘗試從URI路徑獲取檔案名稱: $lastSegment")
                Log.d(TAG, "=== 檔案名稱追蹤結束 ===")
                return lastSegment ?: "unknown"
            }

            Log.d(TAG, "最終檔案名稱: '$fileName'")
            Log.d(TAG, "=== 檔案名稱追蹤結束 ===")
            return fileName

        } catch (e: Exception) {
            Log.e(TAG, "獲取檔案名稱失敗", e)
            Log.e(TAG, "=== 檔案名稱追蹤結束（異常）===")
            return "unknown"
        }
    }
}

/**
 * 匯入結果
 */
sealed class ImportResult {
    data class Success(
        val deckId: String,
        val deckName: String,
        val totalCount: Int,
        val successCount: Int,
        val errorCount: Int,
        val errors: List<String>
    ) : ImportResult()

    data class Error(val message: String) : ImportResult()
}