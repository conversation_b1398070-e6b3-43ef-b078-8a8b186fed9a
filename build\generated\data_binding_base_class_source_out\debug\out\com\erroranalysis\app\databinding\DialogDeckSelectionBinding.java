// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogDeckSelectionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnCancel;

  @NonNull
  public final Button btnConfirm;

  @NonNull
  public final Button btnCreateNewDeck;

  @NonNull
  public final TextInputEditText etSearchDeck;

  @NonNull
  public final RecyclerView rvDecks;

  private DialogDeckSelectionBinding(@NonNull LinearLayout rootView, @NonNull Button btnCancel,
      @NonNull Button btnConfirm, @NonNull Button btnCreateNewDeck,
      @NonNull TextInputEditText etSearchDeck, @NonNull RecyclerView rvDecks) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnConfirm = btnConfirm;
    this.btnCreateNewDeck = btnCreateNewDeck;
    this.etSearchDeck = etSearchDeck;
    this.rvDecks = rvDecks;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogDeckSelectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogDeckSelectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_deck_selection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogDeckSelectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel;
      Button btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btn_confirm;
      Button btnConfirm = ViewBindings.findChildViewById(rootView, id);
      if (btnConfirm == null) {
        break missingId;
      }

      id = R.id.btn_create_new_deck;
      Button btnCreateNewDeck = ViewBindings.findChildViewById(rootView, id);
      if (btnCreateNewDeck == null) {
        break missingId;
      }

      id = R.id.et_search_deck;
      TextInputEditText etSearchDeck = ViewBindings.findChildViewById(rootView, id);
      if (etSearchDeck == null) {
        break missingId;
      }

      id = R.id.rv_decks;
      RecyclerView rvDecks = ViewBindings.findChildViewById(rootView, id);
      if (rvDecks == null) {
        break missingId;
      }

      return new DialogDeckSelectionBinding((LinearLayout) rootView, btnCancel, btnConfirm,
          btnCreateNewDeck, etSearchDeck, rvDecks);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
