package com.erroranalysis.app.ui.study

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.speech.tts.TextToSpeech
import android.text.SpannableString
import android.text.Spanned
import android.text.style.StyleSpan
import android.graphics.Typeface
import android.util.Log
import android.view.MenuItem
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.erroranalysis.app.R
import com.erroranalysis.app.databinding.ActivityCardViewerBinding
import com.erroranalysis.app.ui.base.ThemedActivity
import com.erroranalysis.app.ui.theme.AppTheme
import com.erroranalysis.app.data.DeckDataManager
import com.google.android.material.button.MaterialButton
import java.util.Locale

/**
 * 全新設計的卡片檢視Activity
 * 簡潔、可靠、易於維護
 */
class CardViewerActivity : ThemedActivity() {

    companion object {
        const val EXTRA_CARD = "extra_card"
        const val EXTRA_DECK_ID = "extra_deck_id"
        private const val TAG = "CardViewer"
        private const val REQUEST_EDIT_CARD = 1001
    }

    private lateinit var binding: ActivityCardViewerBinding
    private lateinit var dataManager: DeckDataManager
    private lateinit var card: StudyCard
    private lateinit var deckId: String

    // TTS語音播放
    private var textToSpeech: TextToSpeech? = null
    private var isTtsInitialized = false
    private var isTtsSpeaking = false

    // 當前顯示狀態：false=題目, true=答案+AI解答
    private var showingAnswer = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "CardViewerActivity 開始初始化")

        try {
            // 初始化視圖
            binding = ActivityCardViewerBinding.inflate(layoutInflater)
            setContentView(binding.root)
            Log.d(TAG, "視圖綁定完成")

            // 初始化數據管理器
            dataManager = DeckDataManager(this)

            // 獲取傳入的數據
            if (!extractIntentData()) {
                return // 數據獲取失敗，Activity已結束
            }

            // 設置界面
            setupToolbar()
            setupClickListeners()
            setupTTS()

            // 載入初始內容
            loadContent()

            // 應用主題
            applyTheme()

            Log.d(TAG, "CardViewerActivity 初始化完成")

        } catch (e: Exception) {
            Log.e(TAG, "初始化過程中發生錯誤", e)
            showError("初始化失敗：${e.message}")
            finish()
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume: 刷新內容")
        // 從編輯模式返回時刷新內容，確保顯示最新的卡片數據
        refreshContent()
    }



    /**
     * 從Intent中提取數據
     */
    private fun extractIntentData(): Boolean {
        return try {
            card = intent.getParcelableExtra(EXTRA_CARD) ?: run {
                Log.e(TAG, "無法獲取卡片數據")
                showError("無法載入卡片數據")
                finish()
                return false
            }

            deckId = intent.getStringExtra(EXTRA_DECK_ID) ?: run {
                Log.e(TAG, "無法獲取卡組ID")
                showError("無法載入卡組ID")
                finish()
                return false
            }

            Log.d(TAG, "數據提取成功 - 卡片ID: ${card.id}, 卡組ID: $deckId")
            true

        } catch (e: Exception) {
            Log.e(TAG, "提取Intent數據失敗", e)
            showError("數據載入失敗：${e.message}")
            finish()
            false
        }
    }

    /**
     * 設置工具列
     */
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "卡片檢視"
        }
    }

    /**
     * 設置點擊監聽器
     */
    private fun setupClickListeners() {
        // 內容區域點擊切換
        binding.contentFrame.setOnClickListener {
            toggleContent()
        }

        // 切換按鈕
        binding.btnToggle.setOnClickListener {
            toggleContent()
        }

        // 編輯按鈕
        binding.btnEdit.setOnClickListener {
            editCard()
        }

        // TTS語音播放按鈕
        binding.btnTts.setOnClickListener {
            toggleTTS()
        }
    }

    /**
     * 刷新內容（用於從編輯模式返回或配置變更後）
     */
    private fun refreshContent() {
        try {
            Log.d(TAG, "開始刷新內容")

            // 嘗試從Intent重新獲取最新的卡片數據
            val latestCard = intent.getParcelableExtra<StudyCard>(EXTRA_CARD)
            if (latestCard != null && latestCard.updatedTime > card.updatedTime) {
                card = latestCard
                Log.d(TAG, "使用Intent中的最新卡片數據")
            } else {
                Log.d(TAG, "使用現有卡片數據，清除圖片緩存")
            }

            // 清除可能的圖片緩存
            clearImageCache()

            // 重新載入內容
            loadContent()

        } catch (e: Exception) {
            Log.e(TAG, "刷新內容失敗", e)
            // 如果刷新失敗，至少重新載入現有內容
            clearImageCache()
            loadContent()
        }
    }

    /**
     * 清除圖片緩存
     */
    private fun clearImageCache() {
        try {
            // 清除TextView中的圖片緩存
            binding.tvContent.text = ""

            // 強制垃圾回收，清理圖片記憶體
            System.gc()

            Log.d(TAG, "圖片緩存已清除")
        } catch (e: Exception) {
            Log.e(TAG, "清除圖片緩存失敗", e)
        }
    }

    /**
     * 載入內容
     */
    private fun loadContent() {
        try {
            Log.d(TAG, "開始載入內容")
            
            // 顯示載入指示器
            binding.progressBar.visibility = android.view.View.VISIBLE
            
            // 載入題目內容
            showingAnswer = false
            displayCurrentContent()
            
            // 隱藏載入指示器
            binding.progressBar.visibility = android.view.View.GONE
            
            Log.d(TAG, "內容載入完成")

        } catch (e: Exception) {
            Log.e(TAG, "載入內容失敗", e)
            binding.progressBar.visibility = android.view.View.GONE
            showError("載入內容失敗：${e.message}")
        }
    }

    /**
     * 切換內容顯示
     */
    private fun toggleContent() {
        try {
            Log.d(TAG, "切換內容顯示")

            // 停止當前的TTS播放
            if (isTtsSpeaking) {
                stopTTS()
            }

            showingAnswer = !showingAnswer
            displayCurrentContent()

            // 滾動到頂部
            binding.scrollView.post {
                binding.scrollView.scrollTo(0, 0)
            }

        } catch (e: Exception) {
            Log.e(TAG, "切換內容失敗", e)
            showError("切換內容失敗：${e.message}")
        }
    }

    /**
     * 顯示當前內容
     */
    private fun displayCurrentContent() {
        if (showingAnswer) {
            displayAnswerContent()
        } else {
            displayQuestionContent()
        }
        updateUI()
    }

    /**
     * 顯示題目內容
     */
    private fun displayQuestionContent() {
        try {
            // 嘗試顯示包含圖片的內容
            if (card.question.trim().startsWith("[")) {
                // JSON格式，嘗試顯示圖片
                displayRichContent(card.question)
            } else {
                // 純文字格式
                val content = extractTextContent(card.question)
                binding.tvContent.text = content
            }
            Log.d(TAG, "題目內容顯示成功")
        } catch (e: Exception) {
            Log.e(TAG, "顯示題目失敗", e)
            // 備用方案：顯示純文字
            val content = extractTextContent(card.question)
            binding.tvContent.text = content
        }
    }

    /**
     * 安全地顯示富文本內容（包含圖片）
     */
    private fun displayRichContent(jsonContent: String) {
        try {
            val spannableBuilder = android.text.SpannableStringBuilder()
            val jsonArray = org.json.JSONArray(jsonContent)

            for (i in 0 until jsonArray.length()) {
                val item = jsonArray.getJSONObject(i)
                val type = item.getString("type")
                val content = item.getString("content")

                when (type) {
                    "text" -> {
                        spannableBuilder.append(content)
                    }
                    "image" -> {
                        // 嘗試載入圖片
                        val bitmap = loadImageSafely(content)
                        if (bitmap != null) {
                            // 創建自適應大小的圖片Span
                            val scaledDrawable = createScaledDrawable(bitmap)
                            val imageSpan = android.text.style.ImageSpan(scaledDrawable)
                            val start = spannableBuilder.length
                            spannableBuilder.append(" ") // 使用空格作為圖片佔位符，避免顯示問號
                            spannableBuilder.setSpan(imageSpan, start, start + 1, android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                            // 移除多餘的換行符，讓圖片後面直接跟文字
                        } else {
                            // 圖片載入失敗，顯示文字描述
                            spannableBuilder.append("[圖片: $content]\n")
                        }
                    }
                }
            }

            binding.tvContent.text = spannableBuilder

            // 延遲調整圖片大小，確保TextView已經測量完成
            binding.tvContent.post {
                adjustImageSizesAfterLayout(spannableBuilder)
            }

        } catch (e: Exception) {
            Log.e(TAG, "顯示富文本內容失敗", e)
            // 備用方案：顯示純文字
            val content = extractTextContent(jsonContent)
            binding.tvContent.text = content
        }
    }

    /**
     * 創建自適應大小的圖片Drawable
     */
    private fun createScaledDrawable(bitmap: android.graphics.Bitmap): android.graphics.drawable.BitmapDrawable {
        val drawable = android.graphics.drawable.BitmapDrawable(resources, bitmap)

        // 檢查是否為橫屏模式
        val isLandscape = resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE

        // 獲取屏幕尺寸
        val screenWidth = resources.displayMetrics.widthPixels
        val screenHeight = resources.displayMetrics.heightPixels
        val contentPadding = binding.tvContent.paddingStart + binding.tvContent.paddingEnd

        // 根據屏幕方向調整可用空間
        val availableWidth = if (isLandscape) {
            screenWidth - contentPadding - 16 // 橫屏時充分利用寬度
        } else {
            screenWidth - contentPadding - 16 // 豎屏時保持原有邏輯
        }

        // 計算最佳顯示尺寸
        val targetWidth = if (isLandscape) {
            minOf(availableWidth, (screenWidth * 0.98).toInt()) // 橫屏時使用98%寬度
        } else {
            minOf(availableWidth, (screenWidth * 0.95).toInt()) // 豎屏時使用95%寬度
        }

        if (targetWidth > 0 && bitmap.width > 0) {
            // 按比例縮放
            val scale = targetWidth.toFloat() / bitmap.width
            val targetHeight = (bitmap.height * scale).toInt()

            // 根據屏幕方向調整最大高度限制
            val maxHeight = if (isLandscape) {
                (screenHeight * 0.8).toInt() // 橫屏時使用屏幕高度的80%
            } else {
                (screenWidth * 1.0).toInt() // 豎屏時使用屏幕寬度的100%
            }
            val finalHeight = minOf(targetHeight, maxHeight)
            val finalWidth = if (finalHeight < targetHeight) {
                // 如果高度被限制，重新計算寬度
                (targetWidth * finalHeight.toFloat() / targetHeight).toInt()
            } else {
                targetWidth
            }

            drawable.setBounds(0, 0, finalWidth, finalHeight)
            Log.d(TAG, "圖片縮放: 原始(${bitmap.width}x${bitmap.height}) → 顯示(${finalWidth}x${finalHeight})")
        } else {
            // 備用方案：使用更大的默認尺寸
            val fallbackScale = 0.9f // 增加到90%
            val fallbackWidth = (bitmap.width * fallbackScale).toInt()
            val fallbackHeight = (bitmap.height * fallbackScale).toInt()
            drawable.setBounds(0, 0, fallbackWidth, fallbackHeight)
            Log.d(TAG, "圖片縮放(備用): 原始(${bitmap.width}x${bitmap.height}) → 顯示(${fallbackWidth}x${fallbackHeight})")
        }

        return drawable
    }

    /**
     * 在TextView布局完成後調整圖片大小
     */
    private fun adjustImageSizesAfterLayout(spannableBuilder: android.text.SpannableStringBuilder) {
        try {
            val imageSpans = spannableBuilder.getSpans(0, spannableBuilder.length, android.text.style.ImageSpan::class.java)
            val textViewWidth = binding.tvContent.width
            val textViewPadding = binding.tvContent.paddingStart + binding.tvContent.paddingEnd
            val availableWidth = textViewWidth - textViewPadding

            Log.d(TAG, "TextView實際寬度: $textViewWidth, 可用寬度: $availableWidth")

            if (availableWidth > 0) {
                var needsUpdate = false

                for (imageSpan in imageSpans) {
                    val drawable = imageSpan.drawable
                    if (drawable is android.graphics.drawable.BitmapDrawable) {
                        val bitmap = drawable.bitmap
                        if (bitmap != null) {
                            // 重新計算圖片大小，讓圖片充分利用可用空間
                            val scale = availableWidth.toFloat() / bitmap.width
                            val newHeight = (bitmap.height * scale).toInt()

                            // 增加最大高度限制，讓圖片可以更大
                            val maxHeight = (availableWidth * 1.5).toInt() // 增加到1.5倍
                            val finalHeight = minOf(newHeight, maxHeight)
                            val finalWidth = if (finalHeight < newHeight) {
                                (availableWidth * finalHeight.toFloat() / newHeight).toInt()
                            } else {
                                availableWidth
                            }

                            drawable.setBounds(0, 0, finalWidth, finalHeight)
                            needsUpdate = true

                            Log.d(TAG, "重新調整圖片大小: 原始(${bitmap.width}x${bitmap.height}) → 最終(${finalWidth}x${finalHeight})")
                        }
                    }
                }

                if (needsUpdate) {
                    // 觸發重繪
                    binding.tvContent.text = spannableBuilder
                    Log.d(TAG, "圖片大小調整完成，觸發重繪")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "調整圖片大小時發生錯誤", e)
        }
    }

    /**
     * 安全地載入圖片（帶緩存清除）
     */
    private fun loadImageSafely(fileName: String): android.graphics.Bitmap? {
        return try {
            Log.d(TAG, "嘗試載入圖片: $fileName")

            // 使用ImageStorageManager載入圖片
            val imageManager = com.erroranalysis.app.utils.ImageStorageManager(this)

            // 強制重新載入圖片，不使用任何緩存
            val bitmap = imageManager.loadImageWithoutCache(fileName)

            if (bitmap != null) {
                Log.d(TAG, "圖片載入成功: ${bitmap.width}x${bitmap.height}")
            } else {
                Log.w(TAG, "圖片載入失敗: $fileName")
                // 如果無緩存載入失敗，嘗試普通載入
                val fallbackBitmap = imageManager.loadImage(fileName)
                if (fallbackBitmap != null) {
                    Log.d(TAG, "備用載入成功: ${fallbackBitmap.width}x${fallbackBitmap.height}")
                    return fallbackBitmap
                }
            }

            bitmap
        } catch (e: Exception) {
            Log.e(TAG, "載入圖片時發生異常: $fileName", e)
            null
        }
    }

    /**
     * 顯示答案內容
     */
    private fun displayAnswerContent() {
        try {
            // 構建組合的答案內容
            val combinedJsonContent = buildCombinedAnswerJson()

            // 嘗試顯示富文本內容
            displayRichContent(combinedJsonContent)
            Log.d(TAG, "答案內容顯示成功")

        } catch (e: Exception) {
            Log.e(TAG, "顯示答案失敗", e)
            // 備用方案：使用純文字顯示
            try {
                val answerContent = extractTextContent(card.answer)
                val aiAnswerContent = if (card.aiAnswer.isNotEmpty()) {
                    extractTextContent(card.aiAnswer)
                } else ""

                val combinedContent = buildString {
                    append("📝 答案\n")
                    append(answerContent)

                    if (aiAnswerContent.isNotEmpty()) {
                        append("\n\n🤖 AI解答\n")
                        append(aiAnswerContent)
                    }
                }

                val formattedText = processMarkdownFormatting(combinedContent)
                binding.tvContent.text = formattedText
            } catch (backupException: Exception) {
                Log.e(TAG, "備用顯示方案也失敗", backupException)
                binding.tvContent.text = "答案載入失敗：${e.message}"
            }
        }
    }

    /**
     * 構建組合的答案JSON內容
     */
    private fun buildCombinedAnswerJson(): String {
        val contentList = mutableListOf<Map<String, String>>()

        // 添加答案標題
        contentList.add(mapOf("type" to "text", "content" to "📝 答案\n"))

        // 添加答案內容
        if (card.answer.isNotEmpty()) {
            if (card.answer.trim().startsWith("[")) {
                // 答案是JSON格式，解析並添加
                try {
                    val answerArray = org.json.JSONArray(card.answer)
                    for (i in 0 until answerArray.length()) {
                        val item = answerArray.getJSONObject(i)
                        contentList.add(mapOf(
                            "type" to item.getString("type"),
                            "content" to item.getString("content")
                        ))
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "解析答案JSON失敗，使用純文字", e)
                    contentList.add(mapOf("type" to "text", "content" to card.answer))
                }
            } else {
                // 答案是純文字
                contentList.add(mapOf("type" to "text", "content" to card.answer))
            }
        }

        // 添加AI解答
        if (card.aiAnswer.isNotEmpty()) {
            contentList.add(mapOf("type" to "text", "content" to "\n\n🤖 AI解答\n"))
            contentList.add(mapOf("type" to "text", "content" to card.aiAnswer))
        }

        // 轉換為JSON字符串
        val jsonArray = org.json.JSONArray()
        for (item in contentList) {
            val jsonObject = org.json.JSONObject()
            jsonObject.put("type", item["type"])
            jsonObject.put("content", item["content"])
            jsonArray.put(jsonObject)
        }

        return jsonArray.toString()
    }

    /**
     * 從JSON或純文字中提取文字內容
     */
    private fun extractTextContent(content: String): String {
        return try {
            val rawText = if (content.trim().startsWith("[")) {
                // JSON格式，提取文字內容
                val jsonArray = org.json.JSONArray(content)
                val textBuilder = StringBuilder()

                for (i in 0 until jsonArray.length()) {
                    val item = jsonArray.getJSONObject(i)
                    val type = item.getString("type")
                    val itemContent = item.getString("content")

                    when (type) {
                        "text" -> {
                            textBuilder.append(itemContent)
                            if (i < jsonArray.length() - 1) {
                                textBuilder.append("\n")
                            }
                        }
                        "image" -> {
                            textBuilder.append("[圖片: $itemContent]")
                            if (i < jsonArray.length() - 1) {
                                textBuilder.append("\n")
                            }
                        }
                    }
                }

                textBuilder.toString()
            } else {
                // 純文字格式
                content
            }

            // 處理Markdown格式
            rawText

        } catch (e: Exception) {
            Log.w(TAG, "JSON解析失敗，使用純文字", e)
            content
        }
    }

    /**
     * 處理Markdown格式，移除格式標記並應用文字格式
     */
    private fun processMarkdownFormatting(text: String): SpannableString {
        val spannableString = SpannableString(text)

        // 處理粗體標記 **text**
        val boldPattern = Regex("\\*\\*([^*]+)\\*\\*")
        var offset = 0

        boldPattern.findAll(text).forEach { matchResult ->
            val start = matchResult.range.first - offset
            val end = matchResult.range.last + 1 - offset
            val boldText = matchResult.groupValues[1]

            // 替換文字（移除**標記）
            val beforeText = spannableString.substring(0, start)
            val afterText = spannableString.substring(end)
            val newText = beforeText + boldText + afterText

            // 創建新的SpannableString
            val newSpannableString = SpannableString(newText)

            // 複製之前的格式
            val spans = spannableString.getSpans(0, spannableString.length, Any::class.java)
            spans.forEach { span ->
                val spanStart = spannableString.getSpanStart(span)
                val spanEnd = spannableString.getSpanEnd(span)
                val flags = spannableString.getSpanFlags(span)

                when {
                    spanEnd <= start -> {
                        // span在替換區域之前
                        newSpannableString.setSpan(span, spanStart, spanEnd, flags)
                    }
                    spanStart >= end -> {
                        // span在替換區域之後，需要調整位置
                        val adjustment = boldText.length - (end - start)
                        newSpannableString.setSpan(span, spanStart + adjustment, spanEnd + adjustment, flags)
                    }
                }
            }

            // 為粗體文字添加粗體格式
            newSpannableString.setSpan(
                StyleSpan(Typeface.BOLD),
                start,
                start + boldText.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // 更新offset和spannableString
            offset += 4 // **text** 比 text 多4個字符
            return@forEach
        }

        // 簡單處理：移除所有Markdown標記
        var cleanText = text
        cleanText = cleanText.replace(Regex("`([^`]+)`"), "$1") // 移除代碼塊標記
        cleanText = cleanText.replace(Regex("\\*\\*([^*]+)\\*\\*"), "$1") // 移除粗體標記
        cleanText = cleanText.replace(Regex("\\*([^*]+)\\*"), "$1") // 移除斜體標記
        cleanText = cleanText.replace(Regex("__([^_]+)__"), "$1") // 移除下劃線粗體
        cleanText = cleanText.replace(Regex("_([^_]+)_"), "$1") // 移除下劃線斜體

        val finalSpannable = SpannableString(cleanText)

        // 重新處理粗體（簡化版本）
        val simpleBoldPattern = Regex("\\*\\*([^*]+)\\*\\*")
        var currentOffset = 0

        simpleBoldPattern.findAll(text).forEach { matchResult ->
            val originalStart = matchResult.range.first
            val boldText = matchResult.groupValues[1]

            // 在清理後的文字中找到對應位置
            val cleanStart = originalStart - currentOffset
            val cleanEnd = cleanStart + boldText.length

            if (cleanStart >= 0 && cleanEnd <= finalSpannable.length) {
                finalSpannable.setSpan(
                    StyleSpan(Typeface.BOLD),
                    cleanStart,
                    cleanEnd,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }

            currentOffset += 4 // **text** 比 text 多4個字符
        }

        return finalSpannable
    }

    /**
     * 更新UI狀態
     */
    private fun updateUI() {
        if (showingAnswer) {
            binding.tvContentType.text = if (card.aiAnswer.isNotEmpty()) "答案 & AI解答" else "答案"
            binding.btnToggle.text = "查看題目"
            binding.btnToggle.icon = null
        } else {
            binding.tvContentType.text = "題目"

            // 設置按鈕文字
            if (card.aiAnswer.isNotEmpty()) {
                binding.btnToggle.text = "查看答案 AI"
                binding.btnToggle.icon = null
                binding.btnToggle.maxLines = 1 // 強制單行
            } else {
                binding.btnToggle.text = "查看答案"
                binding.btnToggle.icon = null
                binding.btnToggle.maxLines = 1
            }
        }
    }

    /**
     * 編輯卡片
     */
    private fun editCard() {
        try {
            val intent = Intent(this, CardEditActivity::class.java)
            intent.putExtra(CardEditActivity.EXTRA_CARD, card)
            intent.putExtra(CardEditActivity.EXTRA_DECK_ID, deckId)
            startActivityForResult(intent, REQUEST_EDIT_CARD)
        } catch (e: Exception) {
            Log.e(TAG, "啟動編輯失敗", e)
            showError("無法開啟編輯功能：${e.message}")
        }
    }

    /**
     * 處理編輯結果
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == REQUEST_EDIT_CARD && resultCode == RESULT_OK) {
            val cardSaved = data?.getBooleanExtra(CardEditActivity.RESULT_CARD_SAVED, false) ?: false
            if (cardSaved) {
                // 重新載入卡片數據
                reloadCardData()
            }
        }
    }

    /**
     * 重新載入卡片數據
     */
    private fun reloadCardData() {
        try {
            // 從數據管理器重新載入卡片
            val allCards = dataManager.loadCards()
            val updatedCard = allCards.find { it.id == card.id }

            if (updatedCard != null) {
                card = updatedCard
                loadContent() // 重新載入內容到UI
                Log.d(TAG, "卡片數據已重新載入")
            } else {
                Log.w(TAG, "找不到更新後的卡片")
            }
        } catch (e: Exception) {
            Log.e(TAG, "重新載入卡片數據失敗", e)
            showError("載入更新後的卡片失敗：${e.message}")
        }
    }

    /**
     * 顯示錯誤信息
     */
    private fun showError(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
        Log.e(TAG, "錯誤: $message")
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onApplyTheme(theme: AppTheme) {
        // 應用主題到根佈局背景
        findViewById<android.view.View>(android.R.id.content).setBackgroundColor(theme.getBackgroundColorInt())

        // 應用主題到工具欄
        binding.toolbar.setBackgroundColor(theme.getPrimaryColorInt())
    }

    /**
     * 設置TTS語音播放
     */
    private fun setupTTS() {
        textToSpeech = TextToSpeech(this) { status ->
            if (status == TextToSpeech.SUCCESS) {
                isTtsInitialized = true
                // 設置語言，優先使用英文，如果不支援則使用預設語言
                val result = textToSpeech?.setLanguage(Locale.ENGLISH)
                if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                    // 如果英文不支援，嘗試使用繁體中文
                    textToSpeech?.setLanguage(Locale.TRADITIONAL_CHINESE)
                }
                Log.d(TAG, "TTS初始化成功")
            } else {
                isTtsInitialized = false
                Log.e(TAG, "TTS初始化失敗")
            }
        }
    }

    /**
     * 切換TTS播放/停止
     */
    private fun toggleTTS() {
        if (!isTtsInitialized || textToSpeech == null) {
            Toast.makeText(this, "語音功能尚未準備就緒", Toast.LENGTH_SHORT).show()
            return
        }

        if (isTtsSpeaking) {
            // 停止播放
            stopTTS()
        } else {
            // 開始播放
            speakCurrentContent()
        }
    }

    /**
     * 停止TTS播放
     */
    private fun stopTTS() {
        textToSpeech?.stop()
        isTtsSpeaking = false
        updateTTSButton()
        Toast.makeText(this, "語音播放已停止", Toast.LENGTH_SHORT).show()
    }

    /**
     * 播放當前內容的語音
     */
    private fun speakCurrentContent() {
        if (!isTtsInitialized || textToSpeech == null) {
            Toast.makeText(this, "語音功能尚未準備就緒", Toast.LENGTH_SHORT).show()
            return
        }

        val textToSpeak = if (showingAnswer) {
            // 播放答案內容，包含AI解答
            val answerText = extractTextContent(card.answer)
            val aiAnswerText = if (card.aiAnswer.isNotEmpty()) {
                "\n\nAI解答：${card.aiAnswer}"
            } else {
                ""
            }
            answerText + aiAnswerText
        } else {
            // 播放題目內容
            extractTextContent(card.question)
        }

        if (textToSpeak.isNotEmpty()) {
            // 檢測語言並設置相應的TTS語言
            val isEnglish = textToSpeak.matches(Regex(".*[a-zA-Z].*"))
            if (isEnglish) {
                textToSpeech?.setLanguage(Locale.ENGLISH)
            } else {
                textToSpeech?.setLanguage(Locale.TRADITIONAL_CHINESE)
            }

            // 設置播放完成監聽器
            val utteranceId = "tts_${System.currentTimeMillis()}"
            textToSpeech?.setOnUtteranceProgressListener(object : android.speech.tts.UtteranceProgressListener() {
                override fun onStart(utteranceId: String?) {
                    runOnUiThread {
                        isTtsSpeaking = true
                        updateTTSButton()
                    }
                }

                override fun onDone(utteranceId: String?) {
                    runOnUiThread {
                        isTtsSpeaking = false
                        updateTTSButton()
                    }
                }

                override fun onError(utteranceId: String?) {
                    runOnUiThread {
                        isTtsSpeaking = false
                        updateTTSButton()
                        Toast.makeText(this@CardViewerActivity, "語音播放出錯", Toast.LENGTH_SHORT).show()
                    }
                }
            })

            val params = Bundle()
            params.putString(TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID, utteranceId)
            textToSpeech?.speak(textToSpeak, TextToSpeech.QUEUE_FLUSH, params, utteranceId)

            Toast.makeText(this, "正在播放語音", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "沒有可播放的文字內容", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 更新TTS按鈕圖標
     */
    private fun updateTTSButton() {
        if (isTtsSpeaking) {
            binding.btnTts.setImageResource(R.drawable.ic_volume_off)
            binding.btnTts.contentDescription = "停止語音播放"
        } else {
            binding.btnTts.setImageResource(R.drawable.ic_volume_up)
            binding.btnTts.contentDescription = "語音播放"
        }
    }



    override fun onDestroy() {
        super.onDestroy()
        // 釋放TTS資源
        textToSpeech?.stop()
        textToSpeech?.shutdown()
        isTtsSpeaking = false
    }
}
