// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityBackupRestoreBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialCardView layoutGoogleDriveBackup;

  @NonNull
  public final MaterialCardView layoutGoogleDriveRestore;

  @NonNull
  public final MaterialCardView layoutLocalBackup;

  @NonNull
  public final MaterialCardView layoutLocalRestore;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final Toolbar toolbar;

  private ActivityBackupRestoreBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialCardView layoutGoogleDriveBackup,
      @NonNull MaterialCardView layoutGoogleDriveRestore,
      @NonNull MaterialCardView layoutLocalBackup, @NonNull MaterialCardView layoutLocalRestore,
      @NonNull ProgressBar progressBar, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.layoutGoogleDriveBackup = layoutGoogleDriveBackup;
    this.layoutGoogleDriveRestore = layoutGoogleDriveRestore;
    this.layoutLocalBackup = layoutLocalBackup;
    this.layoutLocalRestore = layoutLocalRestore;
    this.progressBar = progressBar;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityBackupRestoreBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityBackupRestoreBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_backup_restore, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityBackupRestoreBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.layout_google_drive_backup;
      MaterialCardView layoutGoogleDriveBackup = ViewBindings.findChildViewById(rootView, id);
      if (layoutGoogleDriveBackup == null) {
        break missingId;
      }

      id = R.id.layout_google_drive_restore;
      MaterialCardView layoutGoogleDriveRestore = ViewBindings.findChildViewById(rootView, id);
      if (layoutGoogleDriveRestore == null) {
        break missingId;
      }

      id = R.id.layout_local_backup;
      MaterialCardView layoutLocalBackup = ViewBindings.findChildViewById(rootView, id);
      if (layoutLocalBackup == null) {
        break missingId;
      }

      id = R.id.layout_local_restore;
      MaterialCardView layoutLocalRestore = ViewBindings.findChildViewById(rootView, id);
      if (layoutLocalRestore == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityBackupRestoreBinding((LinearLayout) rootView, layoutGoogleDriveBackup,
          layoutGoogleDriveRestore, layoutLocalBackup, layoutLocalRestore, progressBar, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
