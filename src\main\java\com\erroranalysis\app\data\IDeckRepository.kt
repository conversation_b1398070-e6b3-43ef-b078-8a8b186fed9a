package com.erroranalysis.app.data

import com.erroranalysis.app.ui.study.SimpleDeck
import com.erroranalysis.app.ui.study.StudyCard
import com.erroranalysis.app.ui.study.StudyDeck

import java.io.File

interface IDeckRepository {
    fun saveDecks(decks: List<SimpleDeck>)
    fun loadDecks(): MutableList<SimpleDeck>
    fun addDeck(name: String, description: String): String
    fun saveDeck(deck: StudyDeck)
    fun updateDeck(updatedDeck: SimpleDeck)
    fun deleteDeck(deckId: String)
    fun generateNewDeckId(): String
    fun getQuickNotesDeck(): SimpleDeck?
    fun isQuickNotesDeck(deckId: String): Boolean
    fun generateNewCardId(): String
    fun saveCards(cards: List<StudyCard>)
    fun loadCards(): MutableList<StudyCard>
    fun loadCardsByDeckId(deckId: String): List<StudyCard>
    fun addCard(card: StudyCard)
    fun updateCard(updatedCard: StudyCard)
    fun moveCard(cardId: String, targetDeckId: String)
    fun toggleCardStar(cardId: String)
    fun deleteCard(cardId: String)
    fun validateDataIntegrity(): String
    fun fixDataIntegrity()
    fun clearAllDataForDevelopment()
    fun clearAllData()
    suspend fun exportDeck(deckId: String): Result<File>
    suspend fun exportDeckAsZip(deckId: String): Result<File>
    suspend fun shareDeck(deckId: String): Result<File>
}
