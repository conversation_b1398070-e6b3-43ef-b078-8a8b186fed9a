<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 工具列 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:theme="@style/ThemeOverlay.MaterialComponents.Dark.ActionBar"
        app:title="批次匯入題庫"
        app:titleTextColor="@android:color/white" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 標題 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="📚 批次匯入題庫"
                android:textColor="@color/primary"
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center" />

            <!-- 說明文字 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <TextView
                    android:id="@+id/text_instructions"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp"
                    android:text="載入中..."
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:lineSpacingExtra="4dp" />

            </com.google.android.material.card.MaterialCardView>

            <!-- 選擇檔案按鈕 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/button_select_file"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="📁 選擇題庫檔案"
                android:textSize="16sp"
                android:textStyle="bold"
                android:backgroundTint="@color/primary"
                android:textColor="@android:color/white"
                app:cornerRadius="12dp"
                app:icon="@null"
                style="@style/Widget.MaterialComponents.Button" />

            <!-- 範例格式按鈕 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/button_show_example"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:text="📄 查看CSV格式範例"
                android:textSize="14sp"
                android:backgroundTint="@android:color/transparent"
                android:textColor="@color/primary"
                app:cornerRadius="12dp"
                app:strokeColor="@color/primary"
                app:strokeWidth="1dp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

            <!-- 進度條 -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginBottom="16dp"
                android:visibility="gone" />

            <!-- 狀態文字 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <TextView
                    android:id="@+id/text_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp"
                    android:text="請選擇要匯入的題庫檔案"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:gravity="center" />

            </com.google.android.material.card.MaterialCardView>

            <!-- 錯誤詳情 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="#FFEBEE">

                <TextView
                    android:id="@+id/text_errors"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp"
                    android:text=""
                    android:textColor="@color/black"
                    android:textSize="12sp"
                    android:fontFamily="monospace" />

            </com.google.android.material.card.MaterialCardView>

            <!-- 查看卡組按鈕 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/button_view_deck"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="📚 查看新建立的卡組"
                android:textSize="16sp"
                android:textStyle="bold"
                android:backgroundTint="@color/primary"
                android:textColor="@android:color/white"
                android:visibility="gone"
                app:cornerRadius="12dp"
                style="@style/Widget.MaterialComponents.Button" />

            <!-- 完成按鈕 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/button_complete"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="✅ 完成匯入"
                android:textSize="16sp"
                android:textStyle="bold"
                android:backgroundTint="#4CAF50"
                android:textColor="@android:color/white"
                android:visibility="gone"
                app:cornerRadius="12dp"
                style="@style/Widget.MaterialComponents.Button" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>
