package com.erroranalysis.app.ui.camera

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.core.content.ContextCompat
import com.erroranalysis.app.R
import com.erroranalysis.app.domain.camera.model.DocumentDetectionState
import kotlin.math.abs
import kotlin.math.sqrt

/**
 * 文檔邊界覆蓋層
 * 在相機預覽上顯示檢測到的文檔邊界
 */
class DocumentBoundaryOverlay @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    
    // 繪製相關
    private val boundaryPaint = Paint().apply {
        color = Color.WHITE  // 改為白色
        style = Paint.Style.STROKE
        strokeWidth = 19.2f  // 原來的60% (32f * 0.6 = 19.2f)
        isAntiAlias = true
    }

    private val cornerPaint = Paint().apply {
        color = Color.WHITE  // 改為白色
        style = Paint.Style.STROKE
        strokeWidth = 28.8f  // 原來的60% (48f * 0.6 = 28.8f)
        strokeCap = Paint.Cap.ROUND
        isAntiAlias = true
    }
    
    // 移除填充繪製，只保留邊框
    
    // 邊界點
    private var boundaryPoints: MutableList<PointF>? = null
    private var isDocumentDetected = false
    private var detectionState = DocumentDetectionState.NO_DETECTION

    // 動畫相關
    private var animationAlpha = 255
    private var animationDirection = -1

    // 狀態提示文字繪製
    private val textPaint = Paint().apply {
        color = Color.WHITE
        textSize = 48f
        isAntiAlias = true
        setShadowLayer(4f, 2f, 2f, Color.BLACK)
    }

    // 手動調整相關
    private var isDragging = false
    private var dragPointIndex = -1
    private var lastTouchX = 0f
    private var lastTouchY = 0f
    private var isManualAdjustmentEnabled = true  // 默認啟用手動調整

    // 回調
    private var onBoundaryChangedListener: ((List<PointF>) -> Unit)? = null

    companion object {
        private const val CORNER_RADIUS = 40f      // 圓弧範圍再增加1倍 (20f * 2 = 40f)
        private const val ANIMATION_STEP = 8
        private const val TOUCH_TOLERANCE = 120f   // 觸摸容差也相應增大 (60f * 2 = 120f)
    }
    
    /**
     * 設置文檔邊界點
     */
    fun setBoundaryPoints(points: List<PointF>?) {
        if (points == null || points.isEmpty()) {
            // 如果沒有檢測到邊界，顯示默認的文檔檢測框
            showDefaultDocumentFrame()
            detectionState = DocumentDetectionState.NO_DETECTION
        } else {
            boundaryPoints = points.toMutableList()
            isDocumentDetected = true
            // 分析檢測狀態
            detectionState = analyzeDetectionState(points)
        }
        invalidate()
    }

    /**
     * 設置文檔邊界點並指定檢測狀態
     */
    fun setBoundaryPoints(points: List<PointF>?, state: DocumentDetectionState) {
        if (points == null || points.isEmpty()) {
            showDefaultDocumentFrame()
            detectionState = DocumentDetectionState.TOO_FAR // 默認框表示距離太遠
        } else {
            boundaryPoints = points.toMutableList()
            isDocumentDetected = true
            detectionState = state
        }
        invalidate()
    }

    /**
     * 分析檢測狀態
     */
    private fun analyzeDetectionState(points: List<PointF>): DocumentDetectionState {
        if (points.size != 4) return DocumentDetectionState.NO_DETECTION

        val viewWidth = width.toFloat()
        val viewHeight = height.toFloat()

        if (viewWidth <= 0 || viewHeight <= 0) return DocumentDetectionState.GOOD_DETECTION

        // 計算檢測框的面積
        val detectedArea = calculatePolygonArea(points)
        val viewArea = viewWidth * viewHeight
        val areaRatio = detectedArea / viewArea

        return when {
            areaRatio < 0.1 -> DocumentDetectionState.TOO_CLOSE  // 檢測框太小，距離太近
            areaRatio > 0.8 -> DocumentDetectionState.TOO_FAR   // 檢測框太大，距離太遠
            else -> DocumentDetectionState.GOOD_DETECTION       // 檢測良好
        }
    }

    /**
     * 計算多邊形面積
     */
    private fun calculatePolygonArea(points: List<PointF>): Float {
        if (points.size < 3) return 0f

        var area = 0f
        for (i in points.indices) {
            val j = (i + 1) % points.size
            area += points[i].x * points[j].y
            area -= points[j].x * points[i].y
        }
        return abs(area) / 2f
    }

    /**
     * 顯示默認的文檔檢測框（基於 ViewFinder 尺寸）
     */
    private fun showDefaultDocumentFrame() {
        post {
            val width = this.width.toFloat()
            val height = this.height.toFloat()

            if (width > 0 && height > 0) {
                val margin = minOf(width, height) * 0.15f // 增加邊距到15%，確保框在畫面內

                boundaryPoints = mutableListOf(
                    PointF(margin, margin),                    // 左上
                    PointF(width - margin, margin),            // 右上
                    PointF(width - margin, height - margin),   // 右下
                    PointF(margin, height - margin)            // 左下
                )
                isDocumentDetected = true
                invalidate()
            }
        }
    }

    /**
     * 設置邊界變化監聽器
     */
    fun setOnBoundaryChangedListener(listener: (List<PointF>) -> Unit) {
        onBoundaryChangedListener = listener
    }

    /**
     * 獲取當前邊界點
     */
    fun getBoundaryPoints(): List<PointF>? {
        return boundaryPoints?.toList()
    }

    /**
     * 啟用/禁用手動調整
     */
    fun setManualAdjustmentEnabled(enabled: Boolean) {
        isManualAdjustmentEnabled = enabled
    }
    
    /**
     * 清除邊界
     */
    fun clearBoundary() {
        boundaryPoints = null
        isDocumentDetected = false
        invalidate()
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        if (!isDocumentDetected || boundaryPoints == null) {
            return
        }
        
        val points = boundaryPoints!!
        if (points.size != 4) return
        
        // 更新動畫
        updateAnimation()
        
        // 根據檢測狀態設置顏色
        updatePaintColors()

        // 繪製填充區域
        drawFillArea(canvas, points)

        // 繪製邊界線
        drawBoundaryLines(canvas, points)

        // 繪製角點
        drawCornerPoints(canvas, points)

        // 繪製狀態提示
        drawStatusHint(canvas)
    }
    
    /**
     * 根據檢測狀態更新繪製顏色
     */
    private fun updatePaintColors() {
        // 統一使用白色
        boundaryPaint.color = Color.WHITE
        cornerPaint.color = Color.WHITE
    }

    /**
     * 繪製填充區域
     */
    private fun drawFillArea(canvas: Canvas, points: List<PointF>) {
        // 移除填充繪製，只保留邊框線條
    }

    /**
     * 繪製狀態提示
     */
    private fun drawStatusHint(canvas: Canvas) {
        val hintText = when (detectionState) {
            DocumentDetectionState.NO_DETECTION -> "未檢測到文檔"
            DocumentDetectionState.TOO_FAR -> "請靠近文檔"
            DocumentDetectionState.TOO_CLOSE -> "請遠離文檔"
            DocumentDetectionState.GOOD_DETECTION -> "檢測良好"
            DocumentDetectionState.DETECTING -> "正在檢測..."
            DocumentDetectionState.DETECTED -> "已檢測到文檔"
            DocumentDetectionState.NOT_DETECTED -> "未檢測到文檔"
            DocumentDetectionState.ERROR -> "檢測錯誤"
        }

        // 設置文字顏色
        textPaint.color = when (detectionState) {
            DocumentDetectionState.NO_DETECTION -> Color.GRAY
            DocumentDetectionState.TOO_FAR -> Color.YELLOW
            DocumentDetectionState.TOO_CLOSE -> Color.BLUE
            DocumentDetectionState.GOOD_DETECTION -> Color.GREEN
            DocumentDetectionState.DETECTING -> Color.WHITE
            DocumentDetectionState.DETECTED -> Color.GREEN
            DocumentDetectionState.NOT_DETECTED -> Color.GRAY
            DocumentDetectionState.ERROR -> Color.RED
        }

        // 計算文字位置（螢幕上方中央）
        val textWidth = textPaint.measureText(hintText)
        val x = (width - textWidth) / 2
        val y = 100f

        canvas.drawText(hintText, x, y, textPaint)
    }
    
    /**
     * 繪製邊界線 - 簡化為直線連接
     */
    private fun drawBoundaryLines(canvas: Canvas, points: List<PointF>) {
        boundaryPaint.alpha = animationAlpha

        // 繪製四條直線邊界
        for (i in points.indices) {
            val startPoint = points[i]
            val endPoint = points[(i + 1) % points.size]

            canvas.drawLine(startPoint.x, startPoint.y, endPoint.x, endPoint.y, boundaryPaint)
        }
    }
    
    /**
     * 繪製角點 - 簡化為圓形標記
     */
    private fun drawCornerPoints(canvas: Canvas, points: List<PointF>) {
        cornerPaint.alpha = animationAlpha

        // 簡化為圓形角點標記
        points.forEach { point ->
            canvas.drawCircle(point.x, point.y, CORNER_RADIUS * 0.3f, cornerPaint)
        }
    }

    /**
     * 繪製朝向內部的圓弧
     */
    private fun drawInwardCornerArc(canvas: Canvas, center: PointF, startAngle: Float, sweepAngle: Float) {
        // 使用較小的半徑來創建朝內效果
        val inwardRadius = CORNER_RADIUS * 0.7f

        val rect = RectF(
            center.x - inwardRadius,
            center.y - inwardRadius,
            center.x + inwardRadius,
            center.y + inwardRadius
        )

        // 調整起始角度，使圓弧朝向內部
        val adjustedStartAngle = startAngle + 180f
        val adjustedSweepAngle = -sweepAngle // 反向掃描

        canvas.drawArc(rect, adjustedStartAngle, adjustedSweepAngle, false, cornerPaint)
    }

    /**
     * 繪製單個角的圓弧（保留原方法作為備用）
     */
    private fun drawCornerArc(canvas: Canvas, center: PointF, startAngle: Float, sweepAngle: Float) {
        val rect = RectF(
            center.x - CORNER_RADIUS,
            center.y - CORNER_RADIUS,
            center.x + CORNER_RADIUS,
            center.y + CORNER_RADIUS
        )
        canvas.drawArc(rect, startAngle, sweepAngle, false, cornerPaint)
    }
    
    /**
     * 更新動畫
     */
    private fun updateAnimation() {
        animationAlpha += animationDirection * ANIMATION_STEP
        
        if (animationAlpha <= 150) {
            animationAlpha = 150
            animationDirection = 1
        } else if (animationAlpha >= 255) {
            animationAlpha = 255
            animationDirection = -1
        }
        
        // 繼續動畫
        postInvalidateDelayed(50)
    }
    
    /**
     * 將相機座標轉換為View座標
     */
    fun transformCameraToViewCoordinates(
        cameraPoints: List<PointF>,
        cameraWidth: Int,
        cameraHeight: Int
    ): List<PointF> {
        val scaleX = width.toFloat() / cameraWidth
        val scaleY = height.toFloat() / cameraHeight
        
        return cameraPoints.map { point ->
            PointF(point.x * scaleX, point.y * scaleY)
        }
    }
    
    /**
     * 檢查點是否在邊界內
     */
    fun isPointInBoundary(x: Float, y: Float): Boolean {
        if (!isDocumentDetected || boundaryPoints == null) return false
        
        val points = boundaryPoints!!
        if (points.size != 4) return false
        
        // 使用射線法判斷點是否在多邊形內
        var intersections = 0
        for (i in points.indices) {
            val p1 = points[i]
            val p2 = points[(i + 1) % points.size]
            
            if (((p1.y > y) != (p2.y > y)) &&
                (x < (p2.x - p1.x) * (y - p1.y) / (p2.y - p1.y) + p1.x)) {
                intersections++
            }
        }
        
        return intersections % 2 == 1
    }
    
    /**
     * 獲取邊界中心點
     */
    fun getBoundaryCenter(): PointF? {
        if (!isDocumentDetected || boundaryPoints == null) return null
        
        val points = boundaryPoints!!
        if (points.size != 4) return null
        
        val centerX = points.map { it.x }.average().toFloat()
        val centerY = points.map { it.y }.average().toFloat()
        
        return PointF(centerX, centerY)
    }
    
    /**
     * 計算邊界面積
     */
    fun getBoundaryArea(): Float {
        if (!isDocumentDetected || boundaryPoints == null) return 0f
        
        val points = boundaryPoints!!
        if (points.size != 4) return 0f
        
        // 使用鞋帶公式計算面積
        var area = 0f
        for (i in points.indices) {
            val j = (i + 1) % points.size
            area += points[i].x * points[j].y
            area -= points[j].x * points[i].y
        }
        
        return kotlin.math.abs(area) / 2f
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (!isManualAdjustmentEnabled || !isDocumentDetected || boundaryPoints == null) {
            return super.onTouchEvent(event)
        }

        val x = event.x
        val y = event.y

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                return handleTouchDown(x, y)
            }
            MotionEvent.ACTION_MOVE -> {
                return handleTouchMove(x, y)
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                return handleTouchUp()
            }
        }

        return super.onTouchEvent(event)
    }

    /**
     * 處理觸摸開始
     */
    private fun handleTouchDown(x: Float, y: Float): Boolean {
        val points = boundaryPoints ?: return false

        // 查找最近的角點
        for (i in points.indices) {
            val distance = getDistance(x, y, points[i].x, points[i].y)
            if (distance <= TOUCH_TOLERANCE) {
                isDragging = true
                dragPointIndex = i
                lastTouchX = x
                lastTouchY = y
                return true
            }
        }

        return false
    }

    /**
     * 處理觸摸移動
     */
    private fun handleTouchMove(x: Float, y: Float): Boolean {
        if (!isDragging || dragPointIndex == -1) return false

        val points = boundaryPoints ?: return false

        // 更新拖拽點的位置
        points[dragPointIndex].x = x
        points[dragPointIndex].y = y

        // 確保點在視圖範圍內
        constrainPointToView(points[dragPointIndex])

        lastTouchX = x
        lastTouchY = y

        invalidate()

        // 通知邊界變化
        onBoundaryChangedListener?.invoke(points.toList())

        return true
    }

    /**
     * 處理觸摸結束
     */
    private fun handleTouchUp(): Boolean {
        if (isDragging) {
            isDragging = false
            dragPointIndex = -1
            return true
        }
        return false
    }

    /**
     * 計算兩點間距離
     */
    private fun getDistance(x1: Float, y1: Float, x2: Float, y2: Float): Float {
        val dx = x1 - x2
        val dy = y1 - y2
        return sqrt(dx * dx + dy * dy)
    }

    /**
     * 將點限制在視圖範圍內
     */
    private fun constrainPointToView(point: PointF) {
        point.x = point.x.coerceIn(0f, width.toFloat())
        point.y = point.y.coerceIn(0f, height.toFloat())
    }
}
