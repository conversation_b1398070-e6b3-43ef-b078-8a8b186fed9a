package com.erroranalysis.app.ui.camera

import android.graphics.BitmapFactory
import android.os.Bundle
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import com.erroranalysis.app.R

/**
 * 測試裁剪選取框效果的Activity
 */
class CropOverlayTestActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_crop_overlay_test)

        // 設置測試圖片
        val imageView = findViewById<ImageView>(R.id.test_image)
        val cropOverlay = findViewById<CropSelectionOverlay>(R.id.crop_overlay)

        // 載入一個測試圖片（可以是任何圖片）
        try {
            val bitmap = BitmapFactory.decodeResource(resources, R.drawable.ic_launcher_background)
            imageView.setImageBitmap(bitmap)
        } catch (e: Exception) {
            // 如果沒有圖片，使用純色背景
            imageView.setBackgroundColor(0xFF888888.toInt())
        }
    }
}
