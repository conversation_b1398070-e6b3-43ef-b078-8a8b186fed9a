// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.erroranalysis.app.ui.camera.CropSelectionOverlay;
import com.erroranalysis.app.ui.camera.DocumentBoundaryOverlay;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPhotoEditBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final ImageButton btnCancel;

  @NonNull
  public final ImageButton btnConfirm;

  @NonNull
  public final ImageButton btnCrop;

  @NonNull
  public final Button btnCropAdjustment;

  @NonNull
  public final Button btnPerspectiveCorrection;

  @NonNull
  public final ImageButton btnReset;

  @NonNull
  public final CropSelectionOverlay cropSelectionOverlay;

  @NonNull
  public final DocumentBoundaryOverlay documentBoundaryOverlay;

  @NonNull
  public final ImageView ivPhoto;

  @NonNull
  public final LinearLayout layoutBottomControls;

  @NonNull
  public final LinearLayout layoutConfirmButtons;

  @NonNull
  public final FrameLayout layoutPhotoContainer;

  @NonNull
  public final LinearLayout layoutSelectionButtons;

  @NonNull
  public final LinearLayout layoutTopToolbar;

  @NonNull
  public final TextView tvInstruction;

  @NonNull
  public final TextView tvTitle;

  private ActivityPhotoEditBinding(@NonNull ConstraintLayout rootView, @NonNull ImageButton btnBack,
      @NonNull ImageButton btnCancel, @NonNull ImageButton btnConfirm, @NonNull ImageButton btnCrop,
      @NonNull Button btnCropAdjustment, @NonNull Button btnPerspectiveCorrection,
      @NonNull ImageButton btnReset, @NonNull CropSelectionOverlay cropSelectionOverlay,
      @NonNull DocumentBoundaryOverlay documentBoundaryOverlay, @NonNull ImageView ivPhoto,
      @NonNull LinearLayout layoutBottomControls, @NonNull LinearLayout layoutConfirmButtons,
      @NonNull FrameLayout layoutPhotoContainer, @NonNull LinearLayout layoutSelectionButtons,
      @NonNull LinearLayout layoutTopToolbar, @NonNull TextView tvInstruction,
      @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnCancel = btnCancel;
    this.btnConfirm = btnConfirm;
    this.btnCrop = btnCrop;
    this.btnCropAdjustment = btnCropAdjustment;
    this.btnPerspectiveCorrection = btnPerspectiveCorrection;
    this.btnReset = btnReset;
    this.cropSelectionOverlay = cropSelectionOverlay;
    this.documentBoundaryOverlay = documentBoundaryOverlay;
    this.ivPhoto = ivPhoto;
    this.layoutBottomControls = layoutBottomControls;
    this.layoutConfirmButtons = layoutConfirmButtons;
    this.layoutPhotoContainer = layoutPhotoContainer;
    this.layoutSelectionButtons = layoutSelectionButtons;
    this.layoutTopToolbar = layoutTopToolbar;
    this.tvInstruction = tvInstruction;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPhotoEditBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPhotoEditBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_photo_edit, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPhotoEditBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_cancel;
      ImageButton btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btn_confirm;
      ImageButton btnConfirm = ViewBindings.findChildViewById(rootView, id);
      if (btnConfirm == null) {
        break missingId;
      }

      id = R.id.btn_crop;
      ImageButton btnCrop = ViewBindings.findChildViewById(rootView, id);
      if (btnCrop == null) {
        break missingId;
      }

      id = R.id.btn_crop_adjustment;
      Button btnCropAdjustment = ViewBindings.findChildViewById(rootView, id);
      if (btnCropAdjustment == null) {
        break missingId;
      }

      id = R.id.btn_perspective_correction;
      Button btnPerspectiveCorrection = ViewBindings.findChildViewById(rootView, id);
      if (btnPerspectiveCorrection == null) {
        break missingId;
      }

      id = R.id.btn_reset;
      ImageButton btnReset = ViewBindings.findChildViewById(rootView, id);
      if (btnReset == null) {
        break missingId;
      }

      id = R.id.crop_selection_overlay;
      CropSelectionOverlay cropSelectionOverlay = ViewBindings.findChildViewById(rootView, id);
      if (cropSelectionOverlay == null) {
        break missingId;
      }

      id = R.id.document_boundary_overlay;
      DocumentBoundaryOverlay documentBoundaryOverlay = ViewBindings.findChildViewById(rootView, id);
      if (documentBoundaryOverlay == null) {
        break missingId;
      }

      id = R.id.iv_photo;
      ImageView ivPhoto = ViewBindings.findChildViewById(rootView, id);
      if (ivPhoto == null) {
        break missingId;
      }

      id = R.id.layout_bottom_controls;
      LinearLayout layoutBottomControls = ViewBindings.findChildViewById(rootView, id);
      if (layoutBottomControls == null) {
        break missingId;
      }

      id = R.id.layout_confirm_buttons;
      LinearLayout layoutConfirmButtons = ViewBindings.findChildViewById(rootView, id);
      if (layoutConfirmButtons == null) {
        break missingId;
      }

      id = R.id.layout_photo_container;
      FrameLayout layoutPhotoContainer = ViewBindings.findChildViewById(rootView, id);
      if (layoutPhotoContainer == null) {
        break missingId;
      }

      id = R.id.layout_selection_buttons;
      LinearLayout layoutSelectionButtons = ViewBindings.findChildViewById(rootView, id);
      if (layoutSelectionButtons == null) {
        break missingId;
      }

      id = R.id.layout_top_toolbar;
      LinearLayout layoutTopToolbar = ViewBindings.findChildViewById(rootView, id);
      if (layoutTopToolbar == null) {
        break missingId;
      }

      id = R.id.tv_instruction;
      TextView tvInstruction = ViewBindings.findChildViewById(rootView, id);
      if (tvInstruction == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new ActivityPhotoEditBinding((ConstraintLayout) rootView, btnBack, btnCancel,
          btnConfirm, btnCrop, btnCropAdjustment, btnPerspectiveCorrection, btnReset,
          cropSelectionOverlay, documentBoundaryOverlay, ivPhoto, layoutBottomControls,
          layoutConfirmButtons, layoutPhotoContainer, layoutSelectionButtons, layoutTopToolbar,
          tvInstruction, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
