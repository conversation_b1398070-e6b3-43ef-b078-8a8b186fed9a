// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.erroranalysis.app.ui.camera.CropSelectionOverlay;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCropOverlayTestBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final CropSelectionOverlay cropOverlay;

  @NonNull
  public final ImageView testImage;

  private ActivityCropOverlayTestBinding(@NonNull FrameLayout rootView,
      @NonNull CropSelectionOverlay cropOverlay, @NonNull ImageView testImage) {
    this.rootView = rootView;
    this.cropOverlay = cropOverlay;
    this.testImage = testImage;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCropOverlayTestBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCropOverlayTestBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_crop_overlay_test, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCropOverlayTestBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.crop_overlay;
      CropSelectionOverlay cropOverlay = ViewBindings.findChildViewById(rootView, id);
      if (cropOverlay == null) {
        break missingId;
      }

      id = R.id.test_image;
      ImageView testImage = ViewBindings.findChildViewById(rootView, id);
      if (testImage == null) {
        break missingId;
      }

      return new ActivityCropOverlayTestBinding((FrameLayout) rootView, cropOverlay, testImage);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
