package com.erroranalysis.app.ui.camera

import android.graphics.Bitmap
import android.graphics.ImageFormat
import android.graphics.PointF
import android.graphics.Rect
import android.graphics.YuvImage
import android.util.Log
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import com.erroranalysis.app.utils.DocumentBoundaryDetector
import com.erroranalysis.app.domain.camera.model.DocumentDetectionResult
import com.erroranalysis.app.domain.camera.model.DocumentDetectionState
import com.erroranalysis.app.domain.camera.model.FocusState
import java.io.ByteArrayOutputStream


/**
 * 合併的圖像分析器
 * 同時處理對焦檢測和文檔邊界檢測，避免使用多個 ImageAnalysis 用例
 */
class CombinedImageAnalyzer(
    private val onFocusStateChanged: (FocusState) -> Unit,
    private val onBoundariesDetected: (DocumentDetectionResult) -> Unit
) : ImageAnalysis.Analyzer {
    
    companion object {
        private const val TAG = "CombinedImageAnalyzer"
        private const val FOCUS_ANALYSIS_INTERVAL = 10 // 每10幀分析對焦
        private const val DOCUMENT_ANALYSIS_INTERVAL = 30 // 每30幀分析文檔（約每秒一次，假設30fps）
    }
    
    private var frameCount = 0
    private val documentDetector = DocumentBoundaryDetector()
    
    override fun analyze(image: ImageProxy) {
        try {
            frameCount++
            
            // 對焦檢測（較頻繁）
            if (frameCount % FOCUS_ANALYSIS_INTERVAL == 0) {
                val focusState = analyzeFocus(image)
                onFocusStateChanged(focusState)
            }
            
            // 文檔邊界檢測（較不頻繁）
            if (frameCount % DOCUMENT_ANALYSIS_INTERVAL == 0) {
                val detectionResult = analyzeDocumentBoundaries(image)
                onBoundariesDetected(detectionResult)
                Log.d(TAG, "Document detection result: ${detectionResult.state}, boundaries: ${detectionResult.boundaries?.size ?: 0}")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error in combined image analysis", e)
        } finally {
            // 確保 ImageProxy 被正確關閉
            image.close()
        }
    }
    
    /**
     * 分析對焦狀態（安全實現）
     */
    private fun analyzeFocus(image: ImageProxy): FocusState {
        return try {
            // 使用圖像的基本屬性來模擬對焦檢測
            val width = image.width
            val height = image.height
            
            // 基於圖像尺寸的簡單對焦狀態判斷
            val focusScore = (width * height) % 4
            
            when (focusScore) {
                0 -> FocusState.FOCUSED
                1 -> FocusState.TOO_FAR
                2 -> FocusState.TOO_CLOSE
                else -> FocusState.UNFOCUSED
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error analyzing focus", e)
            FocusState.UNFOCUSED
        }
    }
    
    /**
     * 分析文檔邊界（實時 OpenCV 檢測）
     */
    private fun analyzeDocumentBoundaries(image: ImageProxy): DocumentDetectionResult {
        return try {
            Log.d(TAG, "開始實時文檔邊界檢測")

            // 將 ImageProxy 轉換為 Bitmap
            val bitmap = imageProxyToBitmap(image)
            if (bitmap == null) {
                Log.w(TAG, "無法轉換 ImageProxy 為 Bitmap，使用默認邊界")
                return DocumentDetectionResult(
                    boundaries = generateDefaultBoundariesList(image),
                    state = DocumentDetectionState.TOO_FAR
                )
            }

            // 使用 DocumentBoundaryDetector 進行實時檢測
            val detectedBoundaries = documentDetector.detectDocumentBoundary(bitmap)

            // 清理資源
            bitmap.recycle()

            if (detectedBoundaries != null && detectedBoundaries.size == 4) {
                Log.d(TAG, "✅ 實時檢測成功，檢測到 ${detectedBoundaries.size} 個角點")
                Log.d(TAG, "📍 角點座標: ${detectedBoundaries.map { "(${it.x.toInt()}, ${it.y.toInt()})" }}")

                // 將檢測到的座標轉換為 ImageProxy 座標系
                val scaledBoundaries = scaleBoundariesToImageProxy(detectedBoundaries, bitmap.width, bitmap.height, image)

                // 分析檢測狀態
                val detectionState = analyzeDetectionQuality(scaledBoundaries, image)

                return DocumentDetectionResult(
                    boundaries = scaledBoundaries,
                    state = detectionState
                )
            } else {
                Log.d(TAG, "⚠️ 實時檢測失敗，使用默認邊界")
                return DocumentDetectionResult(
                    boundaries = generateDefaultBoundariesList(image),
                    state = DocumentDetectionState.TOO_FAR
                )
            }

        } catch (e: Exception) {
            Log.e(TAG, "實時文檔邊界檢測錯誤", e)
            return DocumentDetectionResult(
                boundaries = generateDefaultBoundariesList(image),
                state = DocumentDetectionState.NO_DETECTION
            )
        }
    }

    /**
     * 將 ImageProxy 轉換為 Bitmap
     */
    private fun imageProxyToBitmap(image: ImageProxy): Bitmap? {
        return try {
            val yBuffer = image.planes[0].buffer
            val uBuffer = image.planes[1].buffer
            val vBuffer = image.planes[2].buffer

            val ySize = yBuffer.remaining()
            val uSize = uBuffer.remaining()
            val vSize = vBuffer.remaining()

            val nv21 = ByteArray(ySize + uSize + vSize)

            yBuffer.get(nv21, 0, ySize)
            vBuffer.get(nv21, ySize, vSize)
            uBuffer.get(nv21, ySize + vSize, uSize)

            val yuvImage = YuvImage(nv21, ImageFormat.NV21, image.width, image.height, null)
            val out = ByteArrayOutputStream()
            yuvImage.compressToJpeg(Rect(0, 0, image.width, image.height), 50, out)
            val imageBytes = out.toByteArray()

            android.graphics.BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.size)
        } catch (e: Exception) {
            Log.e(TAG, "ImageProxy 轉 Bitmap 失敗", e)
            null
        }
    }

    /**
     * 縮放邊界座標到 ImageProxy 座標系
     */
    private fun scaleBoundariesToImageProxy(
        boundaries: List<PointF>,
        bitmapWidth: Int,
        bitmapHeight: Int,
        image: ImageProxy
    ): List<PointF> {
        val scaleX = image.width.toFloat() / bitmapWidth
        val scaleY = image.height.toFloat() / bitmapHeight

        return boundaries.map { point ->
            PointF(point.x * scaleX, point.y * scaleY)
        }
    }

    /**
     * 分析檢測質量
     */
    private fun analyzeDetectionQuality(boundaries: List<PointF>, image: ImageProxy): DocumentDetectionState {
        if (boundaries.size != 4) return DocumentDetectionState.NO_DETECTION

        val imageWidth = image.width.toFloat()
        val imageHeight = image.height.toFloat()
        val imageArea = imageWidth * imageHeight

        // 計算檢測框面積
        val detectedArea = calculatePolygonArea(boundaries)
        val areaRatio = detectedArea / imageArea

        return when {
            areaRatio < 0.15 -> DocumentDetectionState.TOO_CLOSE  // 檢測框太小，距離太近
            areaRatio > 0.75 -> DocumentDetectionState.TOO_FAR   // 檢測框太大，距離太遠
            else -> DocumentDetectionState.GOOD_DETECTION        // 檢測良好
        }
    }

    /**
     * 計算多邊形面積
     */
    private fun calculatePolygonArea(points: List<PointF>): Float {
        if (points.size < 3) return 0f

        var area = 0f
        for (i in points.indices) {
            val j = (i + 1) % points.size
            area += points[i].x * points[j].y
            area -= points[j].x * points[i].y
        }
        return kotlin.math.abs(area) / 2f
    }

    /**
     * 生成默認邊界（當檢測失敗時）
     */
    private fun generateDefaultBoundariesList(image: ImageProxy): List<PointF> {
        val width = image.width.toFloat()
        val height = image.height.toFloat()
        val margin = minOf(width, height) * 0.2f // 20% 邊距，讓用戶知道需要靠近

        return listOf(
            PointF(margin, margin),                           // 左上
            PointF(width - margin, margin),                   // 右上
            PointF(width - margin, height - margin),          // 右下
            PointF(margin, height - margin)                   // 左下
        )
    }
}
