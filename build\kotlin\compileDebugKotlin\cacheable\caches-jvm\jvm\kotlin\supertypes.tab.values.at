/ Header Record For PersistentHashMapValueStorage android.app.Application+ *com.erroranalysis.app.data.IDeckRepository4 3com.erroranalysis.app.data.camera.ICameraRepository kotlin.Enum kotlin.Enum kotlin.Enum8 7com.erroranalysis.app.domain.camera.model.CaptureResult8 7com.erroranalysis.app.domain.camera.model.CaptureResult@ ?com.erroranalysis.app.domain.camera.model.DocumentProcessResult@ ?com.erroranalysis.app.domain.camera.model.DocumentProcessResulti (androidx.appcompat.app.AppCompatActivity?com.erroranalysis.app.ui.theme.ThemeManager.ThemeChangeListener) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity androidx.lifecycle.ViewModel, +com.erroranalysis.app.ui.camera.CameraEvent, +com.erroranalysis.app.ui.camera.CameraEvent, +com.erroranalysis.app.ui.camera.CameraEvent, +com.erroranalysis.app.ui.camera.CameraEvent, +com.erroranalysis.app.ui.camera.CameraEvent, +com.erroranalysis.app.ui.camera.CameraEvent, +com.erroranalysis.app.ui.camera.CameraEvent, +com.erroranalysis.app.ui.camera.CameraEvent, +com.erroranalysis.app.ui.camera.CameraEvent, +com.erroranalysis.app.ui.camera.CameraEvent, +com.erroranalysis.app.ui.camera.CameraEvent, +com.erroranalysis.app.ui.camera.CameraEvent, +com.erroranalysis.app.ui.camera.CameraEvent, +com.erroranalysis.app.ui.camera.CameraEvent- ,androidx.lifecycle.ViewModelProvider.Factory, +androidx.camera.core.ImageAnalysis.Analyzer) (androidx.appcompat.app.AppCompatActivity android.view.View kotlin.Enum2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder android.view.View, +androidx.camera.core.ImageAnalysis.Analyzer, +androidx.camera.core.ImageAnalysis.Analyzer) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity- ,com.erroranalysis.app.ui.base.ThemedActivity) (androidx.appcompat.app.AppCompatActivity kotlin.Enum2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolderi (androidx.appcompat.app.AppCompatActivity?com.erroranalysis.app.ui.theme.ThemeManager.ThemeChangeListener2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity kotlin.Enum$ #androidx.lifecycle.AndroidViewModel- ,androidx.lifecycle.ViewModelProvider.Factory kotlin.Enum- ,com.erroranalysis.app.ui.base.ThemedActivity android.os.Parcelable android.os.Parcelable kotlin.Enum kotlin.Enum kotlin.Enum android.os.Parcelable) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.appcompat.app.AppCompatActivity kotlin.Enum2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder android.app.Dialog8 7com.google.android.material.textfield.TextInputEditText android.text.style.ImageSpan) (com.erroranalysis.app.utils.ImportResult) (com.erroranalysis.app.utils.ImportResult9 8androidx.recyclerview.widget.RecyclerView.ItemDecoration= <com.erroranalysis.app.utils.SimpleBackupManager.BackupResult= <com.erroranalysis.app.utils.SimpleBackupManager.BackupResult> =com.erroranalysis.app.utils.SimpleBackupManager.RestoreResult> =com.erroranalysis.app.utils.SimpleBackupManager.RestoreResult!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding- ,com.erroranalysis.app.ui.base.ThemedActivity kotlin.Enum$ #androidx.lifecycle.AndroidViewModel- ,com.erroranalysis.app.ui.base.ThemedActivity kotlin.Enum) (androidx.appcompat.app.AppCompatActivity- ,com.erroranalysis.app.ui.base.ThemedActivity- ,com.erroranalysis.app.ui.base.ThemedActivity kotlin.Enum- ,com.erroranalysis.app.ui.base.ThemedActivity android.os.Parcelable) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback!  androidx.viewbinding.ViewBinding- ,com.erroranalysis.app.ui.base.ThemedActivity kotlin.Enum$ #androidx.lifecycle.AndroidViewModel!  androidx.viewbinding.ViewBinding- ,com.erroranalysis.app.ui.base.ThemedActivity kotlin.Enum!  androidx.viewbinding.ViewBinding- ,com.erroranalysis.app.ui.base.ThemedActivity kotlin.Enum