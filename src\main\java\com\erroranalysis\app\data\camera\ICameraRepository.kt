package com.erroranalysis.app.data.camera

import android.graphics.Bitmap
import android.graphics.PointF
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageProxy
import androidx.lifecycle.LiveData
import com.erroranalysis.app.domain.camera.model.*
import kotlinx.coroutines.flow.Flow

/**
 * Camera Repository 介面
 * 定義相機相關的資料操作
 */
interface ICameraRepository {
    
    // 相機狀態管理
    fun getFocusState(): Flow<FocusState>
    fun getCaptureState(): Flow<CaptureState>
    fun getDocumentDetectionState(): Flow<DocumentDetectionResult>
    
    // 相機操作
    suspend fun captureImage(imageCapture: ImageCapture, outputFileOptions: ImageCapture.OutputFileOptions): CaptureResult
    suspend fun analyzeImage(imageProxy: ImageProxy): ImageAnalysisResult
    
    // 文檔處理
    suspend fun detectDocumentBoundaries(imagePath: String): List<PointF>?
    suspend fun correctPerspective(imagePath: String, boundaries: List<PointF>, outputWidth: Int? = null, outputHeight: Int? = null): String?
    suspend fun processDocument(imagePath: String, manualBoundaries: List<PointF>? = null): DocumentProcessResult
    
    // OCR 處理
    suspend fun performOCR(imagePath: String): OCRResult
    
    // AI 求解
    suspend fun solveWithAI(imagePath: String, questionText: String? = null): AIResult
    
    // 圖片管理
    suspend fun saveImageToGallery(imagePath: String): Boolean
    suspend fun deleteImage(imagePath: String): Boolean
    fun getImageInfo(imagePath: String): ImageInfo?
    
    // 設定管理
    fun getCameraSettings(): CameraSettings
    fun updateCameraSettings(settings: CameraSettings)
}