# AI Development Tool Quick Reference

## 🤖 For AI Code Analysis & Generation

### 📍 **Quick Architecture Map**
```
CameraActivity.kt → CameraViewModel.kt → CameraUseCases.kt → ICameraRepository.kt → CameraRepository.kt
     (UI)              (State)           (Business)          (Contract)         (Implementation)
```

### 🎯 **Key Files to Understand First**
1. **`ICameraRepository.kt`** - All available operations (API contract)
2. **`CameraUseCases.kt`** - All business features (14 use cases)
3. **`CameraViewModel.kt`** - UI state management
4. **`CameraModels.kt`** - All data structures

### 🔍 **Pattern Recognition for AI Tools**

#### **Use Case Pattern** (Most Important)
```kotlin
// Template for all business operations
class [Action]UseCase(private val repository: ICameraRepository) {
    suspend operator fun invoke([params]): [ReturnType] = 
        repository.[repositoryMethod]([params])
}
```

#### **ViewModel Pattern**
```kotlin
// Template for UI state management
class CameraViewModel(private val cameraUseCases: CameraUseCases) : ViewModel() {
    private val _uiState = MutableStateFlow(CameraUiState())
    val uiState: StateFlow<CameraUiState> = _uiState.asStateFlow()
    
    fun [userAction]() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            try {
                val result = cameraUseCases.[useCase]()
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    [resultField] = result
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = e.message
                )
            }
        }
    }
}
```

### 🚀 **AI Code Generation Templates**

#### **Adding New Feature Checklist**
```kotlin
// 1. Add to ICameraRepository.kt
suspend fun [newFeature](params): [ResultType]

// 2. Implement in CameraRepository.kt
override suspend fun [newFeature](params): [ResultType] {
    // Implementation
}

// 3. Create Use Case in CameraUseCases.kt
class [NewFeature]UseCase(private val repository: ICameraRepository) {
    suspend operator fun invoke(params): [ResultType] = 
        repository.[newFeature](params)
}

// 4. Add to CameraUseCases data class
data class CameraUseCases(
    // ... existing use cases
    val [newFeature]: [NewFeature]UseCase
)

// 5. Update CameraModule.kt
fun provideCameraUseCases(repository: ICameraRepository): CameraUseCases {
    return CameraUseCases(
        // ... existing
        [newFeature] = [NewFeature]UseCase(repository)
    )
}

// 6. Add to CameraViewModel.kt
fun [newFeature](params) {
    viewModelScope.launch {
        _uiState.value = _uiState.value.copy(isLoading = true)
        try {
            val result = cameraUseCases.[newFeature](params)
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                [resultField] = result
            )
        } catch (e: Exception) {
            handleError(e)
        }
    }
}
```

### 📊 **Current Feature Map**
```
Camera Operations:
├── captureImage          → CaptureImageUseCase
├── analyzeImage          → AnalyzeImageUseCase
├── getFocusState         → GetFocusStateUseCase
└── getCaptureState       → GetCaptureStateUseCase

Document Processing:
├── detectDocumentBoundaries    → DetectDocumentBoundariesUseCase
├── correctPerspective          → CorrectPerspectiveUseCase
└── processDocument             → ProcessDocumentUseCase

AI Features:
├── performOCR            → PerformOCRUseCase
└── solveWithAI           → SolveWithAIUseCase

File Management:
├── saveImageToGallery    → SaveImageToGalleryUseCase
├── deleteImage           → DeleteImageUseCase
└── getImageInfo          → GetImageInfoUseCase

Settings:
├── getCameraSettings     → GetCameraSettingsUseCase
└── updateCameraSettings  → UpdateCameraSettingsUseCase
```

### 🎨 **UI State Structure**
```kotlin
data class CameraUiState(
    val isLoading: Boolean = false,           // Loading indicator
    val errorMessage: String? = null,         // Error display
    val lastCapturedImagePath: String? = null, // Last photo
    val correctedImagePath: String? = null,   // Processed image
    val detectedBoundaries: List<PointF>? = null, // Document bounds
    val ocrResult: OCRResult? = null,         // OCR output
    val aiResult: AIResult? = null,           // AI solution
    val cameraSettings: CameraSettings = CameraSettings() // Settings
)
```

### 🔄 **Data Flow Patterns**

#### **Reactive State Flow**
```kotlin
// Repository provides state
fun getFocusState(): Flow<FocusState>

// ViewModel exposes to UI
val focusState: Flow<FocusState> = cameraUseCases.getFocusState()

// Activity observes
lifecycleScope.launch {
    viewModel.focusState.collect { state ->
        updateFocusUI(state)
    }
}
```

#### **Action-Result Pattern**
```kotlin
// User action
viewModel.captureImage()

// ViewModel processes
fun captureImage() {
    viewModelScope.launch {
        val result = cameraUseCases.captureImage(...)
        when (result) {
            is CaptureResult.Success -> handleSuccess(result.imagePath)
            is CaptureResult.Error -> handleError(result.message)
        }
    }
}
```

### 🧪 **Testing Patterns**

#### **Use Case Testing**
```kotlin
@Test
fun `captureImage should return success when repository succeeds`() = runTest {
    // Given
    val expectedResult = CaptureResult.Success("path/to/image")
    whenever(mockRepository.captureImage(any(), any())).thenReturn(expectedResult)
    
    // When
    val result = captureImageUseCase(mockImageCapture, mockOutputOptions)
    
    // Then
    assertEquals(expectedResult, result)
}
```

#### **ViewModel Testing**
```kotlin
@Test
fun `captureImage should update UI state correctly`() = runTest {
    // Given
    val expectedPath = "path/to/image"
    whenever(mockUseCases.captureImage(any(), any()))
        .thenReturn(CaptureResult.Success(expectedPath))
    
    // When
    viewModel.captureImage()
    
    // Then
    assertEquals(expectedPath, viewModel.uiState.value.lastCapturedImagePath)
    assertFalse(viewModel.uiState.value.isLoading)
}
```

### 🔧 **Common AI Tool Operations**

#### **Finding All Features**
```bash
# Search for all use cases
grep -r "class.*UseCase" app_camera_refactor/src/
```

#### **Understanding Data Flow**
```bash
# Find all repository methods
grep -r "suspend fun\|fun.*Flow" app_camera_refactor/src/main/java/*/data/camera/ICameraRepository.kt
```

#### **Locating UI States**
```bash
# Find all UI state properties
grep -r "CameraUiState\|_uiState" app_camera_refactor/src/
```

### 🎯 **AI Tool Recommendations**

1. **For Code Analysis**: Start with `ICameraRepository.kt` to understand capabilities
2. **For Feature Addition**: Follow the Use Case pattern in `CameraUseCases.kt`
3. **For UI Changes**: Check `CameraUiState` and `CameraViewModel.kt`
4. **For Testing**: Use the established patterns in test files
5. **For Debugging**: Check error handling in `CameraViewModel.kt`

### 📝 **Naming Conventions**
- **Use Cases**: `[Verb][Noun]UseCase` (e.g., `CaptureImageUseCase`)
- **Repository Methods**: `[verb][Noun]` (e.g., `captureImage`)
- **ViewModel Methods**: `[verb][Noun]` (e.g., `captureImage`)
- **UI State Fields**: `[adjective][Noun]` (e.g., `lastCapturedImagePath`)
- **Domain Models**: `[Noun][Type]` (e.g., `CaptureResult`, `FocusState`)

### 📚 **Study Module Quick Reference**

#### **Core Models**
```kotlin
// Deck Management
data class SimpleDeck(id, name, description, icon, color, cardCount, ...)
data class StudyDeck(id, name, description, cards: List<StudyCard>, ...)

// Card Management  
data class StudyCard(id, deckId, question, answer, aiAnswer, imagePath, mastery, ...)
enum class CardMastery { LEVEL_1, LEVEL_2, LEVEL_3, LEVEL_4, LEVEL_5 }
```

#### **Repository Pattern (Study)**
```kotlin
interface IDeckRepository {
    // Deck Operations
    fun addDeck(name: String, description: String): String
    fun updateDeck(updatedDeck: SimpleDeck)
    fun deleteDeck(deckId: String)
    
    // Card Operations
    fun addCard(card: StudyCard)
    fun updateCard(updatedCard: StudyCard)
    fun moveCard(cardId: String, targetDeckId: String)
    fun loadCardsByDeckId(deckId: String): List<StudyCard>
}
```

#### **Cross-Module Integration**
```kotlin
// Camera → Study Integration
fun savePhotoAsCard(aiResult: AIResult, imagePath: String, deckId: String) {
    val card = StudyCard(
        id = generateNewCardId(),
        deckId = deckId,
        question = "OCR識別的問題",
        answer = "", // 用戶後續填寫
        aiAnswer = aiResult.solution,
        questionImagePath = imagePath,
        mastery = CardMastery.LEVEL_3
    )
    deckRepository.addCard(card)
}
```

#### **Common Operations**
```kotlin
// Adding new card features
1. Update StudyCard data class
2. Modify DeckDataManager serialization
3. Update CardFilter if needed
4. Add UI handling in DeckDetailViewModel

// Adding new deck features  
1. Update SimpleDeck/StudyDeck data class
2. Modify DeckDataManager methods
3. Update UI in DeckDetailActivity
4. Add ViewModel methods if needed
```

This guide should enable any AI development tool to quickly understand and work with this comprehensive codebase effectively.