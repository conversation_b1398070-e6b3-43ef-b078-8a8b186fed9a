package com.erroranalysis.app.utils

import android.content.Context
import android.util.Log
import org.opencv.android.OpenCVLoader

/**
 * OpenCV 管理器
 * 負責初始化和管理 OpenCV 庫
 */
object OpenCVManager {
    private const val TAG = "OpenCVManager"
    
    private var isInitialized = false
    private var initializationCallbacks = mutableListOf<(Boolean) -> Unit>()
    
    /**
     * 初始化 OpenCV
     */
    fun initialize(context: Context, callback: (Boolean) -> Unit) {
        if (isInitialized) {
            callback(true)
            return
        }

        initializationCallbacks.add(callback)

        // 使用本地 OpenCV 庫初始化（推薦方法）
        try {
            if (OpenCVLoader.initLocal()) {
                Log.d(TAG, "OpenCV library loaded successfully from local module")
                isInitialized = true
                notifyCallbacks(true)
            } else {
                Log.e(TAG, "OpenCV local initialization failed")
                notifyCallbacks(false)
            }
        } catch (e: Exception) {
            Log.e(TAG, "OpenCV initialization error: ${e.message}")
            notifyCallbacks(false)
        }
    }
    
    private fun notifyCallbacks(success: Boolean) {
        initializationCallbacks.forEach { it(success) }
        initializationCallbacks.clear()
    }
    
    /**
     * 檢查 OpenCV 是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized
}
