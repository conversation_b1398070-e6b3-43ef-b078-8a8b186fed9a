<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_deck_detail" modulePackage="com.erroranalysis.app" filePath="app_camera_refactor\src\main\res\layout\activity_deck_detail.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_deck_detail_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="133" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="8" endLine="69" endOffset="43"/></Target><Target id="@+id/text_deck_title" view="TextView"><Expressions/><location startLine="23" startOffset="16" endLine="32" endOffset="55"/></Target><Target id="@+id/btn_sort" view="ImageButton"><Expressions/><location startLine="35" startOffset="16" endLine="43" endOffset="57"/></Target><Target id="@+id/btn_filter" view="ImageButton"><Expressions/><location startLine="46" startOffset="16" endLine="54" endOffset="57"/></Target><Target id="@+id/btn_more_options" view="ImageButton"><Expressions/><location startLine="57" startOffset="16" endLine="65" endOffset="57"/></Target><Target id="@+id/recycler_cards" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="84" startOffset="12" endLine="87" endOffset="54"/></Target><Target id="@+id/layout_empty" view="LinearLayout"><Expressions/><location startLine="89" startOffset="12" endLine="119" endOffset="26"/></Target><Target id="@+id/fab_create_card" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="125" startOffset="4" endLine="131" endOffset="40"/></Target></Targets></Layout>