package com.erroranalysis.app

import android.app.Application
import android.util.Log
import com.erroranalysis.app.di.ServiceLocator
import com.erroranalysis.app.utils.OpenCVInitializer

/**
 * Application 類別
 * 初始化全局依賴
 */
class ErrorAnalysisApplication : Application() {
    
    companion object {
        private const val TAG = "ErrorAnalysisApp"
    }
    
    override fun onCreate() {
        super.onCreate()
        
        // 初始化 OpenCV (必須在其他初始化之前)
        initializeOpenCV()
        
        // 初始化 Service Locator
        ServiceLocator.initialize(this)
    }
    
    private fun initializeOpenCV() {
        OpenCVInitializer.initialize(this) { success ->
            if (success) {
                Log.i(TAG, "✅ OpenCV initialization successful")
            } else {
                Log.e(TAG, "❌ OpenCV initialization failed")
            }
        }
    }
    
    override fun onTerminate() {
        super.onTerminate()
        ServiceLocator.reset()
    }
}