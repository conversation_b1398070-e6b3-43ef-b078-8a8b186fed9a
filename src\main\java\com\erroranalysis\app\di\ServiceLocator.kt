package com.erroranalysis.app.di

import android.content.Context
import com.erroranalysis.app.data.DeckDataManager
import com.erroranalysis.app.data.IDeckRepository
import com.erroranalysis.app.data.camera.CameraRepository
import com.erroranalysis.app.data.camera.ICameraRepository

/**
 * 簡單的 Service Locator，用於提供依賴。
 * 在實際應用中，可以替換為 Dagger Hilt 等更完善的依賴注入框架。
 */
object ServiceLocator {

    @Volatile
    private var applicationContext: Context? = null
    
    @Volatile
    var deckRepository: IDeckRepository? = null
    
    @Volatile
    var cameraRepository: ICameraRepository? = null

    fun initialize(context: Context) {
        applicationContext = context.applicationContext
    }

    fun provideDeckRepository(context: Context): IDeckRepository {
        synchronized(this) {
            return deckRepository ?: createDeckRepository(context)
        }
    }
    
    fun provideCameraRepository(context: Context): ICameraRepository {
        synchronized(this) {
            return cameraRepository ?: createCameraRepository(context)
        }
    }

    private fun createDeckRepository(context: Context): IDeckRepository {
        val newRepo = DeckDataManager(context.applicationContext)
        deckRepository = newRepo
        return newRepo
    }
    
    private fun createCameraRepository(context: Context): ICameraRepository {
        val newRepo = CameraRepository(context.applicationContext)
        cameraRepository = newRepo
        return newRepo
    }

    /**
     * 僅用於測試，重置 Service Locator 的實例。
     */
    fun reset() {
        deckRepository = null
        cameraRepository = null
        applicationContext = null
    }
}