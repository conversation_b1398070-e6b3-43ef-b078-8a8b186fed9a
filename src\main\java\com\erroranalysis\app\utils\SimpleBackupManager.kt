package com.erroranalysis.app.utils

import android.content.Context
import android.net.Uri
import android.util.Log
import com.erroranalysis.app.data.DeckDataManager
import com.erroranalysis.app.ui.study.CardDifficulty
import com.erroranalysis.app.ui.study.CardMastery
import com.erroranalysis.app.ui.study.StudyCard
import com.erroranalysis.app.ui.study.StudyDeck
import com.erroranalysis.app.ui.theme.ThemeManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.io.*
import java.text.SimpleDateFormat
import java.util.*
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream
import java.util.zip.ZipOutputStream

/**
 * 簡化版備份與還原管理器
 */
class SimpleBackupManager(private val context: Context) {
    
    companion object {
        private const val TAG = "SimpleBackupManager"
        private const val BACKUP_FILE_EXTENSION = ".eabackup"
        private const val BACKUP_VERSION = 1
    }
    
    private val deckDataManager = DeckDataManager(context)
    private val themeManager = ThemeManager.getInstance(context)
    private val imageStorageManager = ImageStorageManager(context)
    
    /**
     * 創建備份
     */
    suspend fun createBackup(): BackupResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "開始創建備份")
            
            val backupData = JSONObject()
            backupData.put("version", BACKUP_VERSION)
            backupData.put("timestamp", System.currentTimeMillis())
            backupData.put("app_version", getAppVersion())
            
            // 備份題庫數據
            val decksData = backupDecks()
            backupData.put("decks", decksData)
            
            // 備份設定
            val settingsData = backupSettings()
            backupData.put("settings", settingsData)
            
            // 備份圖片列表
            val imagesList = getImagesList()
            backupData.put("images", imagesList)
            
            Log.d(TAG, "備份數據準備完成")
            BackupResult.Success(backupData)
            
        } catch (e: Exception) {
            Log.e(TAG, "創建備份失敗", e)
            BackupResult.Error("創建備份失敗: ${e.message}")
        }
    }
    
    /**
     * 保存備份到指定位置
     */
    suspend fun saveBackupToUri(
        backupResult: BackupResult.Success,
        targetUri: Uri
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "保存備份到: $targetUri")
            
            context.contentResolver.openOutputStream(targetUri)?.use { outputStream ->
                saveBackupToOutputStream(backupResult, outputStream)
            }
            
            Log.d(TAG, "備份保存成功")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "保存備份失敗", e)
            false
        }
    }

    /**
     * 將備份數據寫入指定的 OutputStream
     */
    suspend fun saveBackupToOutputStream(
        backupResult: BackupResult.Success,
        outputStream: OutputStream
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            ZipOutputStream(outputStream).use { zipOut ->

                // 保存主要備份數據
                val backupEntry = ZipEntry("backup.json")
                zipOut.putNextEntry(backupEntry)
                zipOut.write(backupResult.data.toString(2).toByteArray())
                zipOut.closeEntry()

                // 添加圖片文件
                val imagesArray = backupResult.data.optJSONArray("images")
                if (imagesArray != null) {
                    Log.d(TAG, "開始備份 ${imagesArray.length()} 個圖片文件")
                    for (i in 0 until imagesArray.length()) {
                        val imageName = imagesArray.getString(i)
                        val imageFile = imageStorageManager.getImageFile(imageName)

                        if (imageFile?.exists() == true) {
                            Log.d(TAG, "備份圖片: $imageName (${imageFile.length()} bytes)")
                            val imageEntry = ZipEntry("images/$imageName")
                            zipOut.putNextEntry(imageEntry)

                            FileInputStream(imageFile).use { imageInput ->
                                imageInput.copyTo(zipOut)
                            }
                            zipOut.closeEntry()
                        } else {
                            Log.w(TAG, "圖片文件不存在: $imageName")
                        }
                    }
                } else {
                    Log.w(TAG, "備份中沒有圖片數據")
                }
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "寫入備份到 OutputStream 失敗", e)
            false
        }
    }
    
    /**
     * 從指定位置還原備份
     */
    suspend fun restoreFromUri(sourceUri: Uri): RestoreResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "從 $sourceUri 還原備份")
            
            context.contentResolver.openInputStream(sourceUri)?.use { inputStream ->
                ZipInputStream(inputStream).use { zipIn ->
                    
                    var backupData: JSONObject? = null
                    val imageFiles = mutableMapOf<String, ByteArray>()
                    
                    // 讀取ZIP文件內容
                    var entry = zipIn.nextEntry
                    while (entry != null) {
                        when {
                            entry.name == "backup.json" -> {
                                val content = zipIn.readBytes().toString(Charsets.UTF_8)
                                backupData = JSONObject(content)
                            }
                            entry.name.startsWith("images/") -> {
                                val imageName = entry.name.substring(7)
                                imageFiles[imageName] = zipIn.readBytes()
                            }
                        }
                        entry = zipIn.nextEntry
                    }
                    
                    if (backupData == null) {
                        return@withContext RestoreResult.Error("無效的備份文件")
                    }
                    
                    val results = mutableListOf<String>()
                    
                    // 還原題庫
                    if (backupData.has("decks")) {
                        val success = restoreDecks(backupData.getJSONArray("decks"))
                        if (success) {
                            results.add("題庫數據還原成功")
                        } else {
                            results.add("題庫數據還原失敗")
                        }
                    }
                    
                    // 還原設定
                    if (backupData.has("settings")) {
                        val success = restoreSettings(backupData.getJSONObject("settings"))
                        if (success) {
                            results.add("應用設定還原成功")
                        } else {
                            results.add("應用設定還原失敗")
                        }
                    }
                    
                    // 還原圖片
                    if (imageFiles.isNotEmpty()) {
                        Log.d(TAG, "開始還原 ${imageFiles.size} 個圖片文件")
                        val success = restoreImages(imageFiles)
                        if (success) {
                            results.add("圖片文件還原成功 (${imageFiles.size}張)")
                            Log.d(TAG, "圖片文件還原成功")
                        } else {
                            results.add("圖片文件還原失敗")
                            Log.e(TAG, "圖片文件還原失敗")
                        }
                    } else {
                        Log.w(TAG, "沒有找到圖片文件需要還原")
                    }
                    
                    Log.d(TAG, "還原完成: ${results.joinToString(", ")}")
                    RestoreResult.Success(results)
                }
            } ?: RestoreResult.Error("無法讀取備份文件")
            
        } catch (e: Exception) {
            Log.e(TAG, "還原備份失敗", e)
            RestoreResult.Error("還原失敗: ${e.message}")
        }
    }
    
    /**
     * 創建單一卡組的備份
     */
    suspend fun createDeckBackup(deckId: String): BackupResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "開始創建卡組備份，卡組ID: $deckId")

            val deckToBackup = deckDataManager.loadDecks().find { it.id == deckId }
                ?: return@withContext BackupResult.Error("找不到指定的卡組")

            val cardsToBackup = deckDataManager.loadCardsByDeckId(deckId)

            val backupData = JSONObject()
            backupData.put("version", BACKUP_VERSION)
            backupData.put("timestamp", System.currentTimeMillis())
            backupData.put("app_version", getAppVersion())
            backupData.put("type", "deck_backup") // 新增一個類型標記

            // 備份單一題庫數據
            val deckJson = backupSingleDeck(deckToBackup, cardsToBackup)
            backupData.put("deck", deckJson) // 使用 "deck" 而不是 "decks"

            // 備份相關圖片列表
            val imagesList = getImagesListForCards(cardsToBackup)
            backupData.put("images", imagesList)

            Log.d(TAG, "卡組備份數據準備完成")
            BackupResult.Success(backupData)

        } catch (e: Exception) {
            Log.e(TAG, "創建卡組備份失敗", e)
            BackupResult.Error("創建卡組備份失敗: ${e.message}")
        }
    }

    /**
     * 備份單一題庫及其卡片
     */
    private fun backupSingleDeck(deck: com.erroranalysis.app.ui.study.SimpleDeck, cards: List<StudyCard>): JSONObject {
        val deckJson = JSONObject()
        deckJson.put("id", deck.id)
        deckJson.put("name", deck.name)
        deckJson.put("description", deck.description)
        deckJson.put("icon", deck.icon)
        deckJson.put("color", deck.color)
        deckJson.put("cardCount", cards.size) // 使用實際卡片數量
        deckJson.put("createdAt", deck.createdAt)
        deckJson.put("isStarred", deck.isStarred)

        // 備份卡片
        val cardsArray = JSONArray()
        for (card in cards) {
            val cardJson = JSONObject().apply {
                put("id", card.id)
                put("deckId", card.deckId)
                put("question", card.question)
                put("answer", card.answer)
                put("aiAnswer", card.aiAnswer)
                put("questionImagePath", card.questionImagePath)
                put("answerImagePath", card.answerImagePath)
                put("tags", JSONArray(card.tags))
                put("difficulty", card.difficulty.name)
                put("mastery", card.mastery.name)
                put("masteryLevel", card.masteryLevel)
                put("reviewCount", card.reviewCount)
                put("correctCount", card.correctCount)
                put("lastReviewTime", card.lastReviewTime)
                put("nextReviewTime", card.nextReviewTime)
                put("createdTime", card.createdTime)
                put("updatedTime", card.updatedTime)
                put("isStarred", card.isStarred)
            }
            cardsArray.put(cardJson)
        }
        deckJson.put("cards", cardsArray)
        return deckJson
    }

    /**
     * 獲取指定卡片列表的圖片文件列表
     */
    private fun getImagesListForCards(cards: List<StudyCard>): JSONArray {
        val imagesList = mutableSetOf<String>()
        cards.forEach { card ->
            card.questionImagePath?.let { imagesList.add(it) }
            card.answerImagePath?.let { imagesList.add(it) }
        }
        return JSONArray(imagesList)
    }

    /**
     * 備份題庫數據
     */
    private fun backupDecks(): JSONArray {
        val decksArray = JSONArray()
        val decks = deckDataManager.loadDecks()
        
        for (deck in decks) {
            val deckJson = JSONObject()
            deckJson.put("id", deck.id)
            deckJson.put("name", deck.name)
            deckJson.put("description", deck.description)
            deckJson.put("icon", deck.icon)
            deckJson.put("color", deck.color)
            deckJson.put("cardCount", deck.cardCount)
            deckJson.put("createdAt", deck.createdAt)
            deckJson.put("isStarred", deck.isStarred)
            
            // 備份卡片
            val cards = deckDataManager.loadCardsByDeckId(deck.id)
            val cardsArray = JSONArray()
            
            for (card in cards) {
                val cardJson = JSONObject()
                cardJson.put("id", card.id)
                cardJson.put("deckId", card.deckId)
                cardJson.put("question", card.question)
                cardJson.put("answer", card.answer)
                cardJson.put("aiAnswer", card.aiAnswer)
                cardJson.put("questionImagePath", card.questionImagePath)
                cardJson.put("answerImagePath", card.answerImagePath)
                cardJson.put("tags", JSONArray(card.tags))
                cardJson.put("difficulty", card.difficulty.name)
                cardJson.put("mastery", card.mastery.name)
                cardJson.put("masteryLevel", card.masteryLevel)
                cardJson.put("reviewCount", card.reviewCount)
                cardJson.put("correctCount", card.correctCount)
                cardJson.put("lastReviewTime", card.lastReviewTime)
                cardJson.put("nextReviewTime", card.nextReviewTime)
                cardJson.put("createdTime", card.createdTime)
                cardJson.put("updatedTime", card.updatedTime)
                cardJson.put("isStarred", card.isStarred)
                
                cardsArray.put(cardJson)
            }
            
            deckJson.put("cards", cardsArray)
            decksArray.put(deckJson)
        }
        
        return decksArray
    }
    
    /**
     * 備份應用設定
     */
    private fun backupSettings(): JSONObject {
        val settingsJson = JSONObject()
        
        // 主題設定
        val currentTheme = themeManager.getCurrentTheme()
        settingsJson.put("theme", currentTheme.id)
        
        return settingsJson
    }
    
    /**
     * 獲取所有圖片文件列表
     */
    private fun getImagesList(): JSONArray {
        val imagesList = JSONArray()
        val imageFiles = imageStorageManager.getAllImageFiles()

        Log.d(TAG, "找到 ${imageFiles.size} 個圖片文件")
        for (imageFileName in imageFiles) {
            Log.d(TAG, "添加圖片到備份: $imageFileName")
            imagesList.put(imageFileName)
        }

        return imagesList
    }
    
    /**
     * 還原題庫數據
     */
    private fun restoreDecks(decksArray: JSONArray): Boolean {
        return try {
            for (i in 0 until decksArray.length()) {
                val deckJson = decksArray.getJSONObject(i)

                // 創建新卡組，保留原有的圖標和顏色
                val deckId = deckDataManager.generateNewDeckId()
                val deck = com.erroranalysis.app.ui.study.StudyDeck(
                    id = deckId,
                    name = deckJson.getString("name"),
                    description = deckJson.optString("description", ""),
                    icon = deckJson.optString("icon", "📚"),
                    color = deckJson.optString("color", "#6B7280"),
                    cardCount = 0,
                    createdAt = deckJson.optLong("createdAt", System.currentTimeMillis()),
                    isStarred = deckJson.optBoolean("isStarred", false)
                )

                // 保存卡組
                deckDataManager.saveDeck(deck)
                
                // 還原卡片
                val cardsArray = deckJson.getJSONArray("cards")
                for (j in 0 until cardsArray.length()) {
                    val cardJson = cardsArray.getJSONObject(j)
                    
                    val card = StudyCard(
                        id = deckDataManager.generateNewCardId(),
                        deckId = deckId, // 使用新的卡組ID
                        question = cardJson.getString("question"),
                        answer = cardJson.getString("answer"),
                        aiAnswer = cardJson.optString("aiAnswer", ""),
                        questionImagePath = cardJson.optString("questionImagePath"),
                        answerImagePath = cardJson.optString("answerImagePath"),
                        tags = jsonArrayToList(cardJson.optJSONArray("tags")),
                        difficulty = com.erroranalysis.app.ui.study.CardDifficulty.valueOf(
                            cardJson.optString("difficulty", "NORMAL")
                        ),
                        mastery = com.erroranalysis.app.ui.study.CardMastery.valueOf(
                            cardJson.optString("mastery", "LEVEL_3")
                        ),
                        masteryLevel = cardJson.optInt("masteryLevel", 3),
                        reviewCount = cardJson.optInt("reviewCount", 0),
                        correctCount = cardJson.optInt("correctCount", 0),
                        lastReviewTime = cardJson.optLong("lastReviewTime", 0),
                        nextReviewTime = cardJson.optLong("nextReviewTime", 0),
                        createdTime = cardJson.optLong("createdTime", System.currentTimeMillis()),
                        updatedTime = cardJson.optLong("updatedTime", System.currentTimeMillis()),
                        isStarred = cardJson.optBoolean("isStarred", false)
                    )
                    
                    deckDataManager.addCard(card)
                }
            }
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "還原題庫失敗", e)
            false
        }
    }
    
    /**
     * 還原應用設定
     */
    private fun restoreSettings(settingsJson: JSONObject): Boolean {
        return try {
            // 還原主題
            val themeId = settingsJson.optString("theme")
            if (themeId.isNotEmpty()) {
                val theme = themeManager.getAllThemes().find { it.id == themeId }
                if (theme != null) {
                    themeManager.setCurrentTheme(theme)
                }
            }
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "還原設定失敗", e)
            false
        }
    }
    
    /**
     * 還原圖片文件
     */
    private fun restoreImages(imageFiles: Map<String, ByteArray>): Boolean {
        return try {
            for ((fileName, data) in imageFiles) {
                imageStorageManager.saveImageFromBytes(fileName, data)
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "還原圖片失敗", e)
            false
        }
    }
    
    /**
     * 獲取應用版本
     */
    private fun getAppVersion(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName ?: "1.0"
        } catch (e: Exception) {
            "1.0"
        }
    }
    
    /**
     * 生成備份文件名
     */
    fun generateBackupFileName(): String {
        val dateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
        val timestamp = dateFormat.format(Date())
        return "ErrorAnalysis_Backup_$timestamp$BACKUP_FILE_EXTENSION"
    }
    
    /**
     * JSONArray轉List
     */
    private fun jsonArrayToList(jsonArray: JSONArray?): List<String> {
        if (jsonArray == null) return emptyList()
        val list = mutableListOf<String>()
        for (i in 0 until jsonArray.length()) {
            list.add(jsonArray.getString(i))
        }
        return list
    }
    
    /**
     * 備份結果
     */
    sealed class BackupResult {
        data class Success(val data: JSONObject) : BackupResult()
        data class Error(val message: String) : BackupResult()
    }
    
    /**
     * 還原結果
     */
    sealed class RestoreResult {
        data class Success(val messages: List<String>) : RestoreResult()
        data class Error(val message: String) : RestoreResult()
    }
}
