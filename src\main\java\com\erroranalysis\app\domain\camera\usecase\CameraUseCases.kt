package com.erroranalysis.app.domain.camera.usecase

import android.graphics.PointF
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageProxy
import com.erroranalysis.app.data.camera.ICameraRepository
import com.erroranalysis.app.domain.camera.model.*
import kotlinx.coroutines.flow.Flow

/**
 * 相機相關的 Use Cases
 * 封裝業務邏輯，供 ViewModel 使用
 */

class GetFocusStateUseCase(private val repository: ICameraRepository) {
    operator fun invoke(): Flow<FocusState> = repository.getFocusState()
}

class GetCaptureStateUseCase(private val repository: ICameraRepository) {
    operator fun invoke(): Flow<CaptureState> = repository.getCaptureState()
}

class GetDocumentDetectionStateUseCase(private val repository: ICameraRepository) {
    operator fun invoke(): Flow<DocumentDetectionResult> = repository.getDocumentDetectionState()
}

class CaptureImageUseCase(private val repository: ICameraRepository) {
    suspend operator fun invoke(
        imageCapture: ImageCapture,
        outputFileOptions: ImageCapture.OutputFileOptions
    ): CaptureResult = repository.captureImage(imageCapture, outputFileOptions)
}

class AnalyzeImageUseCase(private val repository: ICameraRepository) {
    suspend operator fun invoke(imageProxy: ImageProxy): ImageAnalysisResult = 
        repository.analyzeImage(imageProxy)
}

class DetectDocumentBoundariesUseCase(private val repository: ICameraRepository) {
    suspend operator fun invoke(imagePath: String): List<PointF>? = 
        repository.detectDocumentBoundaries(imagePath)
}

class CorrectPerspectiveUseCase(private val repository: ICameraRepository) {
    suspend operator fun invoke(
        imagePath: String,
        boundaries: List<PointF>,
        outputWidth: Int? = null,
        outputHeight: Int? = null
    ): String? = repository.correctPerspective(imagePath, boundaries, outputWidth, outputHeight)
}

class ProcessDocumentUseCase(private val repository: ICameraRepository) {
    suspend operator fun invoke(
        imagePath: String,
        manualBoundaries: List<PointF>? = null
    ): DocumentProcessResult = repository.processDocument(imagePath, manualBoundaries)
}

class PerformOCRUseCase(private val repository: ICameraRepository) {
    suspend operator fun invoke(imagePath: String): OCRResult = 
        repository.performOCR(imagePath)
}

class SolveWithAIUseCase(private val repository: ICameraRepository) {
    suspend operator fun invoke(imagePath: String, questionText: String? = null): AIResult = 
        repository.solveWithAI(imagePath, questionText)
}

class SaveImageToGalleryUseCase(private val repository: ICameraRepository) {
    suspend operator fun invoke(imagePath: String): Boolean = 
        repository.saveImageToGallery(imagePath)
}

class DeleteImageUseCase(private val repository: ICameraRepository) {
    suspend operator fun invoke(imagePath: String): Boolean = 
        repository.deleteImage(imagePath)
}

class GetImageInfoUseCase(private val repository: ICameraRepository) {
    operator fun invoke(imagePath: String): ImageInfo? = 
        repository.getImageInfo(imagePath)
}

class GetCameraSettingsUseCase(private val repository: ICameraRepository) {
    operator fun invoke(): CameraSettings = repository.getCameraSettings()
}

class UpdateCameraSettingsUseCase(private val repository: ICameraRepository) {
    operator fun invoke(settings: CameraSettings) = repository.updateCameraSettings(settings)
}

/**
 * 相機 Use Cases 的容器類別
 * 方便依賴注入和管理
 */
data class CameraUseCases(
    val getFocusState: GetFocusStateUseCase,
    val getCaptureState: GetCaptureStateUseCase,
    val getDocumentDetectionState: GetDocumentDetectionStateUseCase,
    val captureImage: CaptureImageUseCase,
    val analyzeImage: AnalyzeImageUseCase,
    val detectDocumentBoundaries: DetectDocumentBoundariesUseCase,
    val correctPerspective: CorrectPerspectiveUseCase,
    val processDocument: ProcessDocumentUseCase,
    val performOCR: PerformOCRUseCase,
    val solveWithAI: SolveWithAIUseCase,
    val saveImageToGallery: SaveImageToGalleryUseCase,
    val deleteImage: DeleteImageUseCase,
    val getImageInfo: GetImageInfoUseCase,
    val getCameraSettings: GetCameraSettingsUseCase,
    val updateCameraSettings: UpdateCameraSettingsUseCase
)