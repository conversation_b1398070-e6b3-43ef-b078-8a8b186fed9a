1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.erroranalysis.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- 相機權限 -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:5:5-65
12-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:5:22-62
13
14    <uses-feature
14-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:6:5-84
15        android:name="android.hardware.camera"
15-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:6:19-57
16        android:required="true" />
16-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:6:58-81
17    <uses-feature
17-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:7:5-95
18        android:name="android.hardware.camera.autofocus"
18-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:7:19-67
19        android:required="false" />
19-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:7:68-92
20
21    <!-- 存儲權限 -->
22    <uses-permission
22-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:10:5-11:38
23        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
23-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:10:22-78
24        android:maxSdkVersion="28" />
24-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:11:9-35
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:12:5-80
25-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:12:22-77
26
27    <!-- 網絡權限 -->
28    <uses-permission android:name="android.permission.INTERNET" />
28-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:15:5-67
28-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:15:22-64
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:16:5-79
29-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:16:22-76
30
31    <permission
31-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
32        android:name="com.erroranalysis.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
32-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
33        android:protectionLevel="signature" />
33-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
34
35    <uses-permission android:name="com.erroranalysis.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
35-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
35-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
36
37    <application
37-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:18:5-133:23
38        android:name="com.erroranalysis.app.ErrorAnalysisApplication"
38-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:19:9-49
39        android:allowBackup="false"
39-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:20:9-36
40        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
40-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
41        android:debuggable="true"
42        android:extractNativeLibs="false"
43        android:icon="@drawable/ic_app_icon"
43-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:21:9-45
44        android:label="@string/app_name"
44-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:22:9-41
45        android:roundIcon="@drawable/ic_app_icon"
45-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:23:9-50
46        android:supportsRtl="true"
46-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:24:9-35
47        android:theme="@style/Theme.ErrorAnalysisApp" >
47-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:25:9-54
48
49        <!-- 主界面 -->
50        <activity
50-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:28:9-36:20
51            android:name="com.erroranalysis.app.ui.main.SimpleMainActivity"
51-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:29:13-55
52            android:exported="true"
52-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:30:13-36
53            android:theme="@style/Theme.ErrorAnalysisApp" >
53-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:31:13-58
54            <intent-filter>
54-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:32:13-35:29
55                <action android:name="android.intent.action.MAIN" />
55-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:33:17-69
55-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:33:25-66
56
57                <category android:name="android.intent.category.LAUNCHER" />
57-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:34:17-77
57-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:34:27-74
58            </intent-filter>
59        </activity>
60
61        <!-- 相機界面 (MVVM重構版) -->
62        <activity
62-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:39:9-42:52
63            android:name="com.erroranalysis.app.ui.camera.CameraActivity"
63-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:40:13-53
64            android:exported="false"
64-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:41:13-37
65            android:screenOrientation="portrait" />
65-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:42:13-49
66
67        <!-- 照片編輯界面 (MVVM重構版) -->
68        <activity
68-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:45:9-48:52
69            android:name="com.erroranalysis.app.ui.camera.PhotoEditActivity"
69-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:46:13-56
70            android:exported="false"
70-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:47:13-37
71            android:screenOrientation="portrait" />
71-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:48:13-49
72
73        <!-- AI求解界面 (MVVM重構版) -->
74        <activity
74-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:51:9-54:52
75            android:name="com.erroranalysis.app.ui.camera.AiSolveActivity"
75-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:52:13-54
76            android:exported="false"
76-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:53:13-37
77            android:screenOrientation="portrait" />
77-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:54:13-49
78
79        <!-- 裁剪選取框測試界面 -->
80        <activity
80-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:57:9-60:52
81            android:name="com.erroranalysis.app.ui.camera.CropOverlayTestActivity"
81-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:58:13-62
82            android:exported="false"
82-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:59:13-37
83            android:screenOrientation="portrait" />
83-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:60:13-49
84
85        <!-- 題目選擇界面 -->
86        <activity
86-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:63:9-66:52
87            android:name="com.erroranalysis.app.ui.selection.QuestionSelectionActivity"
87-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:64:13-67
88            android:exported="false"
88-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:65:13-37
89            android:screenOrientation="portrait" />
89-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:66:13-49
90
91        <!-- 分析結果界面 -->
92        <activity
92-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:69:9-71:40
93            android:name="com.erroranalysis.app.ui.analysis.AnalysisActivity"
93-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:70:13-57
94            android:exported="false" />
94-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:71:13-37
95
96        <!-- 錯題庫主界面（簡化版） -->
97        <activity
97-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:75:9-79:61
98            android:name="com.erroranalysis.app.ui.study.SimpleStudyActivity"
98-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:76:13-57
99            android:configChanges="orientation|screenSize|screenLayout"
99-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:78:13-72
100            android:exported="false"
100-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:77:13-37
101            android:theme="@style/Theme.ErrorAnalysisApp" />
101-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:79:13-58
102
103        <!-- 卡組詳細界面 -->
104        <activity
104-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:82:9-86:61
105            android:name="com.erroranalysis.app.ui.study.DeckDetailActivity"
105-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:83:13-56
106            android:exported="false"
106-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:84:13-37
107            android:parentActivityName="com.erroranalysis.app.ui.study.SimpleStudyActivity"
107-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:85:13-71
108            android:theme="@style/Theme.ErrorAnalysisApp" />
108-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:86:13-58
109
110        <!-- 備份與還原界面 -->
111        <activity
111-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:89:9-93:61
112            android:name="com.erroranalysis.app.ui.settings.BackupRestoreActivity"
112-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:90:13-62
113            android:exported="false"
113-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:91:13-37
114            android:parentActivityName="com.erroranalysis.app.ui.settings.SettingsActivity"
114-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:92:13-71
115            android:theme="@style/Theme.ErrorAnalysisApp" />
115-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:93:13-58
116
117        <!-- 卡片編輯界面 -->
118        <activity
118-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:96:9-101:58
119            android:name="com.erroranalysis.app.ui.study.CardEditActivity"
119-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:97:13-54
120            android:exported="false"
120-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:98:13-37
121            android:parentActivityName="com.erroranalysis.app.ui.study.DeckDetailActivity"
121-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:99:13-70
122            android:theme="@style/Theme.ErrorAnalysisApp"
122-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:100:13-58
123            android:windowSoftInputMode="adjustResize" />
123-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:101:13-55
124
125        <!-- 卡片檢視界面 -->
126        <activity
126-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:104:9-108:61
127            android:name="com.erroranalysis.app.ui.study.CardViewerActivity"
127-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:105:13-56
128            android:exported="false"
128-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:106:13-37
129            android:parentActivityName="com.erroranalysis.app.ui.study.DeckDetailActivity"
129-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:107:13-70
130            android:theme="@style/Theme.ErrorAnalysisApp" />
130-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:108:13-58
131
132        <!-- 批次匯入界面 -->
133        <activity
133-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:111:9-115:61
134            android:name="com.erroranalysis.app.ui.study.BatchImportActivity"
134-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:112:13-57
135            android:exported="false"
135-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:113:13-37
136            android:parentActivityName="com.erroranalysis.app.ui.study.SimpleStudyActivity"
136-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:114:13-71
137            android:theme="@style/Theme.ErrorAnalysisApp" />
137-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:115:13-58
138
139        <!-- 設置頁面 -->
140        <activity
140-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:118:9-122:61
141            android:name="com.erroranalysis.app.ui.settings.SettingsActivity"
141-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:119:13-57
142            android:exported="false"
142-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:120:13-37
143            android:parentActivityName="com.erroranalysis.app.ui.study.SimpleStudyActivity"
143-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:121:13-71
144            android:theme="@style/Theme.ErrorAnalysisApp" />
144-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:122:13-58
145
146        <provider
147            android:name="androidx.core.content.FileProvider"
147-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:125:17-66
148            android:authorities="com.erroranalysis.app.fileprovider"
148-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:126:17-68
149            android:exported="false"
149-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:127:17-41
150            android:grantUriPermissions="true" >
150-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:128:17-51
151            <meta-data
151-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:129:17-131:58
152                android:name="android.support.FILE_PROVIDER_PATHS"
152-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:130:21-71
153                android:resource="@xml/file_paths" />
153-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app_camera_refactor\src\main\AndroidManifest.xml:131:21-55
154        </provider>
155        <!--
156        Service for holding metadata. Cannot be instantiated.
157        Metadata will be merged from other manifests.
158        -->
159        <service
159-->[androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
160            android:name="androidx.camera.core.impl.MetadataHolderService"
160-->[androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:30:13-75
161            android:enabled="false"
161-->[androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:31:13-36
162            android:exported="false" >
162-->[androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:32:13-37
163            <meta-data
163-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7efb7f64467f693255020c5f2fa6ec22\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
164                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
164-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7efb7f64467f693255020c5f2fa6ec22\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
165                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
165-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7efb7f64467f693255020c5f2fa6ec22\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
166        </service>
167
168        <activity
168-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9c5f1994729eba115d1c6ddf8188e9b\transformed\play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
169            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
169-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9c5f1994729eba115d1c6ddf8188e9b\transformed\play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
170            android:excludeFromRecents="true"
170-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9c5f1994729eba115d1c6ddf8188e9b\transformed\play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
171            android:exported="false"
171-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9c5f1994729eba115d1c6ddf8188e9b\transformed\play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
172            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
172-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9c5f1994729eba115d1c6ddf8188e9b\transformed\play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
173        <!--
174            Service handling Google Sign-In user revocation. For apps that do not integrate with
175            Google Sign-In, this service will never be started.
176        -->
177        <service
177-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9c5f1994729eba115d1c6ddf8188e9b\transformed\play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
178            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
178-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9c5f1994729eba115d1c6ddf8188e9b\transformed\play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
179            android:exported="true"
179-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9c5f1994729eba115d1c6ddf8188e9b\transformed\play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
180            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
180-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9c5f1994729eba115d1c6ddf8188e9b\transformed\play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
181            android:visibleToInstantApps="true" />
181-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9c5f1994729eba115d1c6ddf8188e9b\transformed\play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
182        <service
182-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:9:9-15:19
183            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
183-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:10:13-91
184            android:directBootAware="true"
184-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:17:13-43
185            android:exported="false" >
185-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:11:13-37
186            <meta-data
186-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:12:13-14:85
187                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
187-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:13:17-114
188                android:value="com.google.firebase.components.ComponentRegistrar" />
188-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:14:17-82
189            <meta-data
189-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaca9f41a13f2d75f10de673be987a53\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
190                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
190-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaca9f41a13f2d75f10de673be987a53\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
191                android:value="com.google.firebase.components.ComponentRegistrar" />
191-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaca9f41a13f2d75f10de673be987a53\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
192            <meta-data
192-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:20:13-22:85
193                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
193-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:21:17-120
194                android:value="com.google.firebase.components.ComponentRegistrar" />
194-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:22:17-82
195        </service>
196
197        <provider
197-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:9:9-13:38
198            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
198-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:10:13-78
199            android:authorities="com.erroranalysis.app.mlkitinitprovider"
199-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:11:13-69
200            android:exported="false"
200-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:12:13-37
201            android:initOrder="99" />
201-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:13:13-35
202
203        <activity
203-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a170d33460fdd9db3f9af4fb8b90c6b\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:9-22:45
204            android:name="com.google.android.gms.common.api.GoogleApiActivity"
204-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a170d33460fdd9db3f9af4fb8b90c6b\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:19-85
205            android:exported="false"
205-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a170d33460fdd9db3f9af4fb8b90c6b\transformed\play-services-base-18.2.0\AndroidManifest.xml:22:19-43
206            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
206-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a170d33460fdd9db3f9af4fb8b90c6b\transformed\play-services-base-18.2.0\AndroidManifest.xml:21:19-78
207
208        <meta-data
208-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b749962aa03682a0d5ec949dd6944f8\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
209            android:name="com.google.android.gms.version"
209-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b749962aa03682a0d5ec949dd6944f8\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
210            android:value="@integer/google_play_services_version" />
210-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b749962aa03682a0d5ec949dd6944f8\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
211
212        <provider
212-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
213            android:name="androidx.startup.InitializationProvider"
213-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
214            android:authorities="com.erroranalysis.app.androidx-startup"
214-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
215            android:exported="false" >
215-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
216            <meta-data
216-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
217                android:name="androidx.emoji2.text.EmojiCompatInitializer"
217-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
218                android:value="androidx.startup" />
218-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
219            <meta-data
219-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bb1e110bac3f893ab69e80b3d9d714\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
220                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
220-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bb1e110bac3f893ab69e80b3d9d714\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
221                android:value="androidx.startup" />
221-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bb1e110bac3f893ab69e80b3d9d714\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
222            <meta-data
222-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
223                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
223-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
224                android:value="androidx.startup" />
224-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
225        </provider>
226
227        <receiver
227-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
228            android:name="androidx.profileinstaller.ProfileInstallReceiver"
228-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
229            android:directBootAware="false"
229-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
230            android:enabled="true"
230-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
231            android:exported="true"
231-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
232            android:permission="android.permission.DUMP" >
232-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
233            <intent-filter>
233-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
234                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
234-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
234-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
235            </intent-filter>
236            <intent-filter>
236-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
237                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
237-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
237-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
238            </intent-filter>
239            <intent-filter>
239-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
240                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
240-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
240-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
241            </intent-filter>
242            <intent-filter>
242-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
243                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
243-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
243-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
244            </intent-filter>
245        </receiver>
246
247        <service
247-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
248            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
248-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
249            android:exported="false" >
249-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
250            <meta-data
250-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
251                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
251-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
252                android:value="cct" />
252-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
253        </service>
254        <service
254-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
255            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
255-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
256            android:exported="false"
256-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
257            android:permission="android.permission.BIND_JOB_SERVICE" >
257-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
258        </service>
259
260        <receiver
260-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
261            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
261-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
262            android:exported="false" />
262-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
263    </application>
264
265</manifest>
