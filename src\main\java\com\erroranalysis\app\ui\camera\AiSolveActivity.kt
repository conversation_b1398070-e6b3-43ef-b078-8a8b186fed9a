package com.erroranalysis.app.ui.camera

import android.app.Dialog
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.RectF
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Base64
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.GestureDetectorCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.erroranalysis.app.data.DeckDataManager
import com.erroranalysis.app.databinding.ActivityAiSolveBinding
import com.erroranalysis.app.databinding.DialogDeckSelectionBinding
import com.erroranalysis.app.ui.study.SimpleDeck
import com.erroranalysis.app.ui.study.StudyCard
import com.erroranalysis.app.ui.study.CardDifficulty
import com.erroranalysis.app.utils.GeminiAIService
import com.erroranalysis.app.utils.ImageStorageManager
import com.erroranalysis.app.utils.MarkdownRenderer
import kotlinx.coroutines.launch
import java.io.ByteArrayOutputStream
import java.io.File

/**
 * AI求解Activity
 * 用於選擇問題區域並進行AI求解
 */
class AiSolveActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAiSolveBinding
    private lateinit var gestureDetector: GestureDetectorCompat
    private lateinit var geminiAIService: GeminiAIService
    private lateinit var deckDataManager: DeckDataManager
    private lateinit var imageStorageManager: ImageStorageManager
    private lateinit var markdownRenderer: MarkdownRenderer
    private var imagePath: String? = null
    private var isProcessing = false
    private var originalBitmap: Bitmap? = null
    private var currentAiAnswer: String = ""
    private var currentQuestionImage: Bitmap? = null

    companion object {
        private const val TAG = "AiSolveActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        Log.d(TAG, "AiSolveActivity onCreate() 開始")
        super.onCreate(savedInstanceState)

        try {
            binding = ActivityAiSolveBinding.inflate(layoutInflater)
            setContentView(binding.root)
            Log.d(TAG, "布局設置完成")

            // 獲取傳入的圖片路徑
            imagePath = intent.getStringExtra("image_path")
            Log.d(TAG, "收到圖片路徑: $imagePath")

            if (imagePath == null) {
                Log.e(TAG, "未收到圖片路徑")
                Toast.makeText(this, "圖片載入失敗", Toast.LENGTH_SHORT).show()
                finish()
                return
            }

            // 初始化服務和數據管理器
            geminiAIService = GeminiAIService(this)
            deckDataManager = DeckDataManager(this)
            imageStorageManager = ImageStorageManager(this)
            markdownRenderer = MarkdownRenderer(this)

            setupUI()
            loadImage()
            setupGestureDetector()
            Log.d(TAG, "AiSolveActivity 初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "AiSolveActivity onCreate() 發生錯誤", e)
            Toast.makeText(this, "頁面載入失敗: ${e.message}", Toast.LENGTH_LONG).show()
            finish()
        }
    }

    private fun setupUI() {
        // 返回按鈕 - 回到AI拍照頁面
        binding.btnBack.setOnClickListener {
            backToCameraPage()
        }

        // 存到卡片按鈕
        binding.btnSaveToCard.setOnClickListener {
            showDeckSelectionDialog()
        }

        // 設置全螢幕模式
        setupFullScreenMode()
    }

    /**
     * 返回到AI拍照頁面
     */
    private fun backToCameraPage() {
        Log.d(TAG, "返回到AI拍照頁面")

        // 創建返回相機頁面的Intent
        val cameraIntent = android.content.Intent(this, CameraActivity::class.java)

        // 設置標誌，清除當前任務棧並創建新的任務
        cameraIntent.flags = android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP or
                            android.content.Intent.FLAG_ACTIVITY_NEW_TASK

        // 傳遞AI模式狀態，確保返回時仍處於AI模式
        cameraIntent.putExtra("ai_solve_mode", true)

        startActivity(cameraIntent)
        finish()
    }

    private fun setupFullScreenMode() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
            // Android 11 (API 30) 及以上版本
            window.setDecorFitsSystemWindows(false)
            window.insetsController?.let { controller ->
                controller.hide(android.view.WindowInsets.Type.statusBars() or android.view.WindowInsets.Type.navigationBars())
                controller.systemBarsBehavior = android.view.WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            }
        } else {
            // Android 10 (API 29) 及以下版本
            @Suppress("DEPRECATION")
            window.decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_FULLSCREEN
                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            )
        }
        
        // 設置狀態欄和導航欄為透明
        window.statusBarColor = android.graphics.Color.TRANSPARENT
        window.navigationBarColor = android.graphics.Color.TRANSPARENT
    }

    private fun loadImage() {
        try {
            val imageFile = File(imagePath!!)
            if (imageFile.exists()) {
                val bitmap = BitmapFactory.decodeFile(imagePath)
                if (bitmap != null) {
                    originalBitmap = bitmap
                    binding.ivPhoto.setImageBitmap(bitmap)
                    Log.d(TAG, "圖片載入成功: $imagePath")
                } else {
                    Log.e(TAG, "圖片解碼失敗")
                    Toast.makeText(this, "圖片載入失敗", Toast.LENGTH_SHORT).show()
                    finish()
                }
            } else {
                Log.e(TAG, "圖片文件不存在: $imagePath")
                Toast.makeText(this, "圖片文件不存在", Toast.LENGTH_SHORT).show()
                finish()
            }
        } catch (e: Exception) {
            Log.e(TAG, "載入圖片時發生錯誤", e)
            Toast.makeText(this, "圖片載入錯誤", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun setupGestureDetector() {
        gestureDetector = GestureDetectorCompat(this, object : GestureDetector.SimpleOnGestureListener() {
            override fun onDoubleTap(e: MotionEvent): Boolean {
                // 檢查雙擊是否在選擇框內
                val selectionRect = binding.cropOverlay.getSelectionRect()
                if (selectionRect.contains(e.x, e.y)) {
                    Log.d(TAG, "雙擊在選擇框內，開始AI求解")
                    startAiSolve(selectionRect)
                    return true
                } else {
                    Log.d(TAG, "雙擊在選擇框外")
                    Toast.makeText(this@AiSolveActivity, "請在框內雙擊", Toast.LENGTH_SHORT).show()
                }
                return false
            }

            override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
                return false
            }
        })

        // 設置觸摸事件監聽
        binding.cropOverlay.setOnTouchListener { view, event ->
            // 先讓手勢檢測器處理
            val gestureHandled = gestureDetector.onTouchEvent(event)
            
            // 如果手勢檢測器沒有處理，則讓CropSelectionOverlay處理
            if (!gestureHandled) {
                view.onTouchEvent(event)
            } else {
                true
            }
        }
    }

    private fun startAiSolve(selectionRect: RectF) {
        if (isProcessing) {
            Log.d(TAG, "AI正在處理中，忽略重複請求")
            return
        }

        val bitmap = originalBitmap
        if (bitmap == null) {
            Log.e(TAG, "原始圖片為空，無法進行AI求解")
            Toast.makeText(this, "圖片載入失敗，無法進行AI求解", Toast.LENGTH_SHORT).show()
            return
        }

        isProcessing = true
        showLoading(true)

        Log.d(TAG, "開始AI求解 - 選擇區域: $selectionRect")

        // 使用協程進行AI求解
        lifecycleScope.launch {
            try {
                // 裁切選擇區域的圖片
                val croppedBitmap = cropImageFromSelection(bitmap, selectionRect)
                if (croppedBitmap == null) {
                    showAiAnswer("圖片裁切失敗，請重新選擇區域")
                    showLoading(false)
                    isProcessing = false
                    return@launch
                }

                Log.d(TAG, "圖片裁切完成，開始調用Gemini API")

                // 調用Gemini AI進行求解
                val aiAnswer = geminiAIService.solveQuestion("", listOf(croppedBitmap))

                Log.d(TAG, "AI求解完成")

                // 保存當前數據
                currentAiAnswer = aiAnswer
                currentQuestionImage = croppedBitmap

                showAiAnswer(aiAnswer)

            } catch (e: Exception) {
                Log.e(TAG, "AI求解過程發生錯誤", e)
                showAiAnswer("AI求解失敗：${e.message}")
            } finally {
                showLoading(false)
                isProcessing = false
            }
        }
    }

    private fun showLoading(show: Boolean) {
        binding.loadingContainer.visibility = if (show) View.VISIBLE else View.GONE
        if (show) {
            binding.tvAiAnswer.setText("")
        }
    }

    private fun showAiAnswer(answer: String) {
        try {
            // 檢查是否包含Markdown或LaTeX
            if (MarkdownRenderer.containsMarkdown(answer) || MarkdownRenderer.containsLatex(answer)) {
                // 使用Markdown渲染器
                markdownRenderer.renderToTextView(binding.tvAiAnswer, answer)
                Log.d(TAG, "使用Markdown渲染器渲染AI解答")
            } else {
                // 普通文本顯示
                binding.tvAiAnswer.text = answer
                Log.d(TAG, "使用普通文本顯示AI解答")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Markdown渲染失敗，使用簡化渲染", e)
            // 備用方案：使用簡化渲染
            markdownRenderer.renderSimple(binding.tvAiAnswer, answer)
        }

        // 顯示存到卡片按鈕
        binding.btnSaveToCard.visibility = View.VISIBLE

        // 滾動到AI解答區域
        binding.aiAnswerContainer.post {
            binding.aiAnswerContainer.smoothScrollTo(0, 0)
        }
    }

    /**
     * 根據選擇區域裁切圖片
     */
    private fun cropImageFromSelection(originalBitmap: Bitmap, selectionRect: RectF): Bitmap? {
        return try {
            // 獲取ImageView的實際顯示尺寸和位置
            val imageView = binding.ivPhoto
            val imageMatrix = imageView.imageMatrix
            val drawable = imageView.drawable

            if (drawable == null) {
                Log.e(TAG, "ImageView drawable為空")
                return null
            }

            // 計算圖片在ImageView中的實際顯示區域
            val drawableWidth = drawable.intrinsicWidth.toFloat()
            val drawableHeight = drawable.intrinsicHeight.toFloat()
            val viewWidth = imageView.width.toFloat()
            val viewHeight = imageView.height.toFloat()

            // 計算縮放比例（centerInside模式）
            val scaleX = viewWidth / drawableWidth
            val scaleY = viewHeight / drawableHeight
            val scale = minOf(scaleX, scaleY)

            // 計算圖片在ImageView中的實際位置
            val scaledWidth = drawableWidth * scale
            val scaledHeight = drawableHeight * scale
            val offsetX = (viewWidth - scaledWidth) / 2
            val offsetY = (viewHeight - scaledHeight) / 2

            // 將選擇區域座標轉換為原始圖片座標
            val cropX = ((selectionRect.left - offsetX) / scale).toInt().coerceAtLeast(0)
            val cropY = ((selectionRect.top - offsetY) / scale).toInt().coerceAtLeast(0)
            val cropWidth = ((selectionRect.width()) / scale).toInt()
                .coerceAtMost(originalBitmap.width - cropX)
            val cropHeight = ((selectionRect.height()) / scale).toInt()
                .coerceAtMost(originalBitmap.height - cropY)

            if (cropWidth <= 0 || cropHeight <= 0) {
                Log.e(TAG, "裁切區域無效: width=$cropWidth, height=$cropHeight")
                return null
            }

            Log.d(TAG, "裁切參數: x=$cropX, y=$cropY, width=$cropWidth, height=$cropHeight")
            Log.d(TAG, "原始圖片尺寸: ${originalBitmap.width}x${originalBitmap.height}")

            // 裁切圖片
            Bitmap.createBitmap(originalBitmap, cropX, cropY, cropWidth, cropHeight)

        } catch (e: Exception) {
            Log.e(TAG, "圖片裁切失敗", e)
            null
        }
    }



    override fun onResume() {
        super.onResume()
        // 重新設置全螢幕模式
        setupFullScreenMode()
    }

    /**
     * 顯示卡組選擇對話框
     */
    private fun showDeckSelectionDialog() {
        if (currentAiAnswer.isEmpty()) {
            Toast.makeText(this, "請先進行AI求解", Toast.LENGTH_SHORT).show()
            return
        }

        val dialogBinding = DialogDeckSelectionBinding.inflate(layoutInflater)
        val dialog = Dialog(this)
        dialog.setContentView(dialogBinding.root)
        dialog.window?.setLayout(
            (resources.displayMetrics.widthPixels * 0.9).toInt(),
            android.view.ViewGroup.LayoutParams.WRAP_CONTENT
        )

        // 載入卡組列表
        val decks = deckDataManager.loadDecks()
        val adapter = DeckSelectionAdapter(decks) { selectedDeck ->
            dialogBinding.btnConfirm.isEnabled = true
        }

        dialogBinding.rvDecks.layoutManager = LinearLayoutManager(this)
        dialogBinding.rvDecks.adapter = adapter

        // 設置搜尋功能
        dialogBinding.etSearchDeck.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val query = s?.toString() ?: ""
                adapter.filter(query)

                // 搜尋後重置確認按鈕狀態
                dialogBinding.btnConfirm.isEnabled = false
            }

            override fun afterTextChanged(s: Editable?) {}
        })

        // 建立新卡組按鈕
        dialogBinding.btnCreateNewDeck.setOnClickListener {
            dialog.dismiss()
            showCreateDeckDialog()
        }

        // 取消按鈕
        dialogBinding.btnCancel.setOnClickListener {
            dialog.dismiss()
        }

        // 確認按鈕
        dialogBinding.btnConfirm.setOnClickListener {
            val selectedDeck = adapter.getSelectedDeck()
            if (selectedDeck != null) {
                dialog.dismiss()
                saveToCard(selectedDeck)
            }
        }

        dialog.show()
    }

    /**
     * 顯示建立新卡組對話框
     */
    private fun showCreateDeckDialog() {
        // 這裡可以重用現有的建立卡組對話框
        // 或者創建一個簡化版本
        Toast.makeText(this, "建立新卡組功能開發中...", Toast.LENGTH_SHORT).show()
    }

    /**
     * 保存到卡片
     */
    private fun saveToCard(deck: SimpleDeck) {
        lifecycleScope.launch {
            try {
                // 保存題目圖片
                val questionImagePath = currentQuestionImage?.let { bitmap ->
                    imageStorageManager.saveImage(bitmap)
                }

                // 創建卡片
                val card = StudyCard(
                    id = deckDataManager.generateNewCardId(),
                    deckId = deck.id,
                    question = questionImagePath?.let { "[{\"type\":\"image\",\"content\":\"$it\"}]" } ?: "",
                    answer = "",
                    aiAnswer = currentAiAnswer,
                    questionImagePath = questionImagePath,
                    answerImagePath = null,
                    tags = mutableListOf(),
                    difficulty = CardDifficulty.NORMAL,
                    masteryLevel = 0,
                    reviewCount = 0,
                    correctCount = 0,
                    lastReviewTime = 0L,
                    nextReviewTime = System.currentTimeMillis() + 24 * 60 * 60 * 1000L, // 明天
                    createdTime = System.currentTimeMillis(),
                    updatedTime = System.currentTimeMillis(),
                    isStarred = false
                )

                // 保存卡片
                deckDataManager.addCard(card)

                // 顯示成功消息
                Toast.makeText(this@AiSolveActivity, "已保存到「${deck.name}」卡組", Toast.LENGTH_SHORT).show()

            } catch (e: Exception) {
                Log.e(TAG, "保存卡片失敗", e)
                Toast.makeText(this@AiSolveActivity, "保存失敗: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }
}
