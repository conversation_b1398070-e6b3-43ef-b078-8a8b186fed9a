package com.erroranalysis.app.utils

import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.RelativeSizeSpan
import android.text.style.SuperscriptSpan
import android.text.style.SubscriptSpan
import java.util.regex.Pattern

/**
 * 數學式格式化工具
 * 將LaTeX風格的數學表達式轉換為更易讀的格式
 */
object MathFormatHelper {
    
    /**
     * 格式化數學表達式
     */
    fun formatMathExpression(text: String): String {
        var result = text

        // 首先清理誤轉換的符號
        result = cleanupMisconvertedSymbols(result)

        // LaTeX符號轉換
        result = convertLatexSymbols(result)

        // 函數名稱轉換
        result = convertFunctionNames(result)

        // 運算符轉換
        result = convertOperators(result)

        // 改善段落格式
        result = improveFormatting(result)

        // 清理多餘的空格和符號
        result = cleanupExpression(result)

        return result
    }
    
    /**
     * 創建帶有上標下標的SpannableString
     */
    fun createFormattedSpannable(text: String): SpannableStringBuilder {
        val spannable = SpannableStringBuilder(formatMathExpression(text))
        
        // 處理上標 (x^2 -> x²)
        applySuperscript(spannable)
        
        // 處理下標 (x_1 -> x₁)
        applySubscript(spannable)
        
        return spannable
    }
    
    /**
     * 轉換LaTeX符號 - 保守版本，只處理常見符號
     */
    private fun convertLatexSymbols(text: String): String {
        var result = text

        // 只處理最常見的LaTeX符號，避免過度替換
        val replacements = mapOf(
            // 積分符號
            "\\\\int\\b" to "∫",
            "\\\\sum\\b" to "∑",
            "\\\\prod\\b" to "∏",

            // 希臘字母
            "\\\\pi\\b" to "π",
            "\\\\alpha\\b" to "α",
            "\\\\beta\\b" to "β",
            "\\\\gamma\\b" to "γ",
            "\\\\delta\\b" to "δ",
            "\\\\theta\\b" to "θ",
            "\\\\lambda\\b" to "λ",
            "\\\\mu\\b" to "μ",
            "\\\\sigma\\b" to "σ",
            "\\\\phi\\b" to "φ",
            "\\\\omega\\b" to "ω",

            // 運算符號
            "\\\\cdot\\b" to "·",
            "\\\\times\\b" to "×",
            "\\\\div\\b" to "÷",
            "\\\\pm\\b" to "±",

            // 比較符號
            "\\\\leq\\b" to "≤",
            "\\\\geq\\b" to "≥",
            "\\\\neq\\b" to "≠",
            "\\\\approx\\b" to "≈",

            // 其他常用符號
            "\\\\infty\\b" to "∞",
            "\\\\rightarrow\\b" to "→",
            "\\\\leftarrow\\b" to "←"
        )

        // 使用正則表達式進行精確替換
        for ((pattern, replacement) in replacements) {
            result = result.replace(Regex(pattern), replacement)
        }

        // 處理分數 - 只處理簡單的分數格式
        result = result.replace(Regex("\\\\frac\\{([^{}]+)\\}\\{([^{}]+)\\}"), "$1/$2")

        // 處理根號 - 只處理簡單的根號格式
        result = result.replace(Regex("\\\\sqrt\\{([^{}]+)\\}"), "√($1)")

        return result
    }
    
    /**
     * 轉換函數名稱
     */
    private fun convertFunctionNames(text: String): String {
        return text
            .replace("\\sin", "sin")
            .replace("\\cos", "cos")
            .replace("\\tan", "tan")
            .replace("\\log", "log")
            .replace("\\ln", "ln")
            .replace("\\exp", "exp")
            .replace("\\arcsin", "arcsin")
            .replace("\\arccos", "arccos")
            .replace("\\arctan", "arctan")
    }
    
    /**
     * 轉換運算符 - 更精確的轉換，避免誤轉換
     */
    private fun convertOperators(text: String): String {
        var result = text

        // 只轉換數學上下文中的乘法符號，避免破壞Markdown格式
        // 匹配模式：數字*數字、變數*變數、)*(等
        result = result.replace(Regex("(\\d)\\s*\\*\\s*(\\d)"), "$1×$2")
        result = result.replace(Regex("([a-zA-Z])\\s*\\*\\s*([a-zA-Z])"), "$1×$2")
        result = result.replace(Regex("(\\))\\s*\\*\\s*(\\()"), "$1×$2")
        result = result.replace(Regex("(\\d)\\s*\\*\\s*([a-zA-Z])"), "$1×$2")
        result = result.replace(Regex("([a-zA-Z])\\s*\\*\\s*(\\d)"), "$1×$2")

        // 除法符號（只在有空格的情況下轉換，避免影響分數）
        result = result.replace(Regex("\\s/\\s"), " ÷ ")

        // 指數符號轉換（只在數學上下文中）
        result = result.replace(Regex("([a-zA-Z0-9)])\\^2\\b"), "$1²")
        result = result.replace(Regex("([a-zA-Z0-9)])\\^3\\b"), "$1³")
        result = result.replace(Regex("([a-zA-Z0-9)])\\^4\\b"), "$1⁴")
        result = result.replace(Regex("([a-zA-Z0-9)])\\^5\\b"), "$1⁵")
        result = result.replace(Regex("([a-zA-Z0-9)])\\^6\\b"), "$1⁶")
        result = result.replace(Regex("([a-zA-Z0-9)])\\^7\\b"), "$1⁷")
        result = result.replace(Regex("([a-zA-Z0-9)])\\^8\\b"), "$1⁸")
        result = result.replace(Regex("([a-zA-Z0-9)])\\^9\\b"), "$1⁹")
        result = result.replace(Regex("([a-zA-Z0-9)])\\^0\\b"), "$1⁰")
        result = result.replace(Regex("([a-zA-Z0-9)])\\^1\\b"), "$1¹")
        result = result.replace(Regex("([a-zA-Z0-9)])\\^-1\\b"), "$1⁻¹")
        result = result.replace(Regex("([a-zA-Z0-9)])\\^-2\\b"), "$1⁻²")
        result = result.replace(Regex("([a-zA-Z0-9)])\\^-3\\b"), "$1⁻³")

        // 下標符號轉換（只在數學上下文中）
        result = result.replace(Regex("([a-zA-Z])_0\\b"), "$1₀")
        result = result.replace(Regex("([a-zA-Z])_1\\b"), "$1₁")
        result = result.replace(Regex("([a-zA-Z])_2\\b"), "$1₂")
        result = result.replace(Regex("([a-zA-Z])_3\\b"), "$1₃")
        result = result.replace(Regex("([a-zA-Z])_4\\b"), "$1₄")
        result = result.replace(Regex("([a-zA-Z])_5\\b"), "$1₅")
        result = result.replace(Regex("([a-zA-Z])_6\\b"), "$1₆")
        result = result.replace(Regex("([a-zA-Z])_7\\b"), "$1₇")
        result = result.replace(Regex("([a-zA-Z])_8\\b"), "$1₈")
        result = result.replace(Regex("([a-zA-Z])_9\\b"), "$1₉")

        return result
    }
    
    /**
     * 清理誤轉換的符號
     */
    private fun cleanupMisconvertedSymbols(text: String): String {
        return text
            // 修復被誤轉換的Markdown粗體格式
            .replace("×××", "***")
            .replace("××", "**")

            // 修復其他常見的誤轉換
            .replace("××", "**")  // 雙星號粗體
            .replace("×××", "***") // 三星號粗體加斜體

            // 修復列表符號
            .replace("× ", "* ")

            // 修復其他可能的誤轉換情況
            .replace(Regex("×(?![0-9a-zA-Z(])"), "*")  // 不在數學上下文中的×轉回*
    }

    /**
     * 改善格式化 - 添加適當的段落分隔
     */
    private fun improveFormatting(text: String): String {
        return text
            // 在標題後添加換行（使用正確的**格式）
            .replace(Regex("(\\*\\*[^*]+\\*\\*)"), "$1\n")

            // 在數學式前後添加換行
            .replace(Regex("([。！？])([∫∑∏√π])"), "$1\n\n$2")
            .replace(Regex("([∫∑∏√π][^\\n]*[=≠≤≥])"), "$1\n")

            // 在步驟說明前添加換行
            .replace(Regex("([。！？])(步驟|解法|方法|因此|所以|答案)"), "$1\n\n$2")

            // 在列表項目前添加換行
            .replace(Regex("([。！？])([1-9]\\.|[一二三四五六七八九十]\\.|•)"), "$1\n\n$2")

            // 標準化多個換行為雙換行
            .replace(Regex("\\n{3,}"), "\n\n")
    }

    /**
     * 清理表達式 - 保守版本，避免過度處理
     */
    private fun cleanupExpression(text: String): String {
        return text
            // 只移除明顯多餘的空格
            .replace(Regex("[ \\t]+"), " ")
            .replace(Regex("\\n[ \\t]+"), "\n")
            .replace(Regex("[ \\t]+\\n"), "\n")

            // 標準化換行
            .replace(Regex("\\n{4,}"), "\n\n\n")
            .trim()
    }
    
    /**
     * 應用上標格式
     */
    private fun applySuperscript(spannable: SpannableStringBuilder) {
        val superscriptPattern = Pattern.compile("\\^\\{([^}]+)\\}")
        val matcher = superscriptPattern.matcher(spannable)
        
        val matches = mutableListOf<Pair<Int, Int>>()
        while (matcher.find()) {
            matches.add(Pair(matcher.start(), matcher.end()))
        }
        
        // 從後往前處理，避免位置偏移
        for (match in matches.reversed()) {
            val start = match.first
            val end = match.second
            val superscriptText = spannable.substring(start + 2, end - 1)
            
            spannable.replace(start, end, superscriptText)
            spannable.setSpan(
                SuperscriptSpan(),
                start,
                start + superscriptText.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            spannable.setSpan(
                RelativeSizeSpan(0.7f),
                start,
                start + superscriptText.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
    }
    
    /**
     * 應用下標格式
     */
    private fun applySubscript(spannable: SpannableStringBuilder) {
        val subscriptPattern = Pattern.compile("_\\{([^}]+)\\}")
        val matcher = subscriptPattern.matcher(spannable)
        
        val matches = mutableListOf<Pair<Int, Int>>()
        while (matcher.find()) {
            matches.add(Pair(matcher.start(), matcher.end()))
        }
        
        // 從後往前處理，避免位置偏移
        for (match in matches.reversed()) {
            val start = match.first
            val end = match.second
            val subscriptText = spannable.substring(start + 2, end - 1)
            
            spannable.replace(start, end, subscriptText)
            spannable.setSpan(
                SubscriptSpan(),
                start,
                start + subscriptText.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            spannable.setSpan(
                RelativeSizeSpan(0.7f),
                start,
                start + subscriptText.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
    }
    
    /**
     * 檢查文本是否包含數學表達式
     */
    fun containsMathExpression(text: String): Boolean {
        val mathPatterns = listOf(
            "\\\\[a-zA-Z]+", // LaTeX命令
            "\\^[0-9]+", // 指數
            "_[0-9]+", // 下標
            "∫|∑|∏|√|π|α|β|γ|δ|θ|λ|μ|σ|φ|ω", // 數學符號
            "÷|≤|≥|≠|≈|∈|⊂|⊃|∪|∩|∅", // 運算符號（移除×避免誤判）
            "\\d+\\s*[×÷]\\s*\\d+", // 明確的數學運算
            "[a-zA-Z]\\s*[×÷]\\s*[a-zA-Z]" // 變數運算
        )

        return mathPatterns.any { pattern ->
            Pattern.compile(pattern).matcher(text).find()
        }
    }
}
