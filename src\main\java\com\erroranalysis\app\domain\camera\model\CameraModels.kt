package com.erroranalysis.app.domain.camera.model

import android.graphics.PointF

/**
 * 相機相關的領域模型
 */

// 對焦狀態
enum class FocusState {
    FOCUSED,
    TOO_CLOSE,
    TOO_FAR,
    UNFOCUSED,
    CAPTURING
}

// 拍攝狀態
enum class CaptureState {
    IDLE,
    CAPTURING,
    SUCCESS,
    ERROR
}

// 文檔檢測狀態
enum class DocumentDetectionState {
    DETECTING,
    DETECTED,
    NOT_DETECTED,
    ERROR,
    TOO_CLOSE,
    TOO_FAR,
    GOOD_DETECTION,
    NO_DETECTION
}

// 文檔檢測結果
data class DocumentDetectionResult(
    val boundaries: List<PointF>?,
    val state: DocumentDetectionState,
    val confidence: Float = 0f
)

// 拍攝結果
sealed class CaptureResult {
    data class Success(val imagePath: String) : CaptureResult()
    data class Error(val message: String, val exception: Throwable? = null) : CaptureResult()
}

// 圖像分析結果
data class ImageAnalysisResult(
    val focusState: FocusState,
    val documentDetection: DocumentDetectionResult
)

// 文檔處理結果
sealed class DocumentProcessResult {
    data class Success(
        val originalImagePath: String,
        val correctedImagePath: String,
        val detectedBoundaries: List<PointF>
    ) : DocumentProcessResult()
    
    data class Error(val message: String) : DocumentProcessResult()
}

// OCR 結果
data class OCRResult(
    val text: String,
    val confidence: Float,
    val isSuccess: Boolean,
    val errorMessage: String? = null
)

// AI 求解結果
data class AIResult(
    val solution: String,
    val isSuccess: Boolean,
    val errorMessage: String? = null
)

// 圖像信息
data class ImageInfo(
    val width: Int,
    val height: Int,
    val mimeType: String,
    val fileSize: Long
)

// 相機設定
data class CameraSettings(
    val enableDocumentDetection: Boolean = true,
    val enableFocusAnalysis: Boolean = true,
    val captureQuality: Int = 90,
    val enableFlash: Boolean = false,
    val enableGridLines: Boolean = true
)