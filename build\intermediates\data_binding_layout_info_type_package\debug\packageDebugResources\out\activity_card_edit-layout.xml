<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_card_edit" modulePackage="com.erroranalysis.app" filePath="app_camera_refactor\src\main\res\layout\activity_card_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_card_edit_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="323" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="8" endLine="45" endOffset="43"/></Target><Target id="@+id/button_cancel" view="ImageButton"><Expressions/><location startLine="23" startOffset="16" endLine="31" endOffset="57"/></Target><Target id="@+id/button_save" view="ImageButton"><Expressions/><location startLine="33" startOffset="16" endLine="41" endOffset="57"/></Target><Target id="@+id/button_rotate_question_image" view="ImageButton"><Expressions/><location startLine="87" startOffset="20" endLine="95" endOffset="55"/></Target><Target id="@+id/button_ocr_question" view="ImageButton"><Expressions/><location startLine="97" startOffset="20" endLine="105" endOffset="55"/></Target><Target id="@+id/button_add_question_image" view="ImageButton"><Expressions/><location startLine="107" startOffset="20" endLine="114" endOffset="55"/></Target><Target id="@+id/edit_question" view="com.erroranalysis.app.ui.widgets.RichTextEditText"><Expressions/><location startLine="124" startOffset="20" endLine="141" endOffset="49"/></Target><Target id="@+id/button_ocr_answer" view="ImageButton"><Expressions/><location startLine="173" startOffset="20" endLine="181" endOffset="55"/></Target><Target id="@+id/button_add_answer_image" view="ImageButton"><Expressions/><location startLine="183" startOffset="20" endLine="190" endOffset="55"/></Target><Target id="@+id/edit_answer" view="com.erroranalysis.app.ui.widgets.RichTextEditText"><Expressions/><location startLine="200" startOffset="20" endLine="217" endOffset="49"/></Target><Target id="@+id/button_ai_answer" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="229" startOffset="20" endLine="241" endOffset="73"/></Target><Target id="@+id/layout_ai_answer" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="244" startOffset="20" endLine="272" endOffset="71"/></Target><Target id="@+id/edit_ai_answer" view="com.erroranalysis.app.ui.widgets.RichTextEditText"><Expressions/><location startLine="251" startOffset="20" endLine="270" endOffset="51"/></Target><Target id="@+id/edit_tags" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="299" startOffset="20" endLine="308" endOffset="49"/></Target></Targets></Layout>