<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/camera_background"
    tools:context=".ui.camera.AiSolveActivity">

    <!-- 圖片顯示區域 -->
    <FrameLayout
        android:id="@+id/image_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/ai_answer_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="0.6">

        <!-- 背景圖片 -->
        <ImageView
            android:id="@+id/iv_photo"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerInside"
            android:background="@color/camera_background" />

        <!-- 裁剪選擇框覆蓋層 -->
        <com.erroranalysis.app.ui.camera.CropSelectionOverlay
            android:id="@+id/crop_overlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible" />

        <!-- 返回按鈕 -->
        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="top|start"
            android:layout_margin="16dp"
            android:background="@drawable/circle_button_background"
            android:contentDescription="返回"
            android:src="@drawable/ic_arrow_back"
            android:tint="@color/text_white" />



    </FrameLayout>

    <!-- AI解答區域 -->
    <ScrollView
        android:id="@+id/ai_answer_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/background_white"
        app:layout_constraintTop_toBottomOf="@id/image_container"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="spread"
        app:layout_constraintHeight_min="200dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- AI解答標題 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_ai"
                    android:tint="@color/primary_blue"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="AI 解答"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

            </LinearLayout>

            <!-- AI解答內容 -->
            <TextView
                android:id="@+id/tv_ai_answer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="點擊框內兩下開始AI求解..."
                android:textSize="16sp"
                android:textColor="@color/text_primary"
                android:lineSpacingExtra="4dp"
                android:minHeight="120dp"
                android:gravity="top" />

            <!-- 載入指示器 -->
            <LinearLayout
                android:id="@+id/loading_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:padding="20dp"
                android:visibility="gone">

                <ProgressBar
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="12dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="AI 正在分析中..."
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>

            <!-- 存到卡片按鈕 -->
            <Button
                android:id="@+id/btn_save_to_card"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/button_primary"
                android:text="存到卡片"
                android:textColor="@color/text_white"
                android:textSize="16sp"
                android:textStyle="bold"
                android:drawableStart="@drawable/ic_bookmark_add"
                android:drawablePadding="8dp"
                android:drawableTint="@color/text_white"
                android:gravity="center"
                android:visibility="gone" />

        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
