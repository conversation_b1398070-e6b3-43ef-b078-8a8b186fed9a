package com.erroranalysis.app.ui.theme

import android.content.Context
import android.widget.TextView
import android.view.ViewGroup

/**
 * 字體應用擴展函數
 */

/**
 * 為TextView應用當前字體
 */
fun TextView.applyCurrentFont(context: Context) {
    val typeface = FontManager.getTypeface(context)
    if (typeface != null) {
        this.typeface = typeface
    }
}

/**
 * 為ViewGroup中的所有TextView應用當前字體
 */
fun ViewGroup.applyCurrentFontToAllTextViews(context: Context) {
    val typeface = FontManager.getTypeface(context)
    if (typeface != null) {
        applyTypefaceToAllTextViews(this, typeface)
    }
}

/**
 * 遞歸應用字體到所有TextView
 */
private fun applyTypefaceToAllTextViews(viewGroup: ViewGroup, typeface: android.graphics.Typeface) {
    for (i in 0 until viewGroup.childCount) {
        val child = viewGroup.getChildAt(i)
        when (child) {
            is TextView -> {
                child.typeface = typeface
            }
            is ViewGroup -> {
                applyTypefaceToAllTextViews(child, typeface)
            }
        }
    }
}
