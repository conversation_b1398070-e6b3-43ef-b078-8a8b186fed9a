Tapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/ImageStorageManager.ktVapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/CardViewerActivity.kt\app_camera_refactor/src/main/java/com/erroranalysis/app/ui/settings/adapters/ThemeAdapter.kt[app_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CameraViewModelFactory.ktLapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/PdfExporter.ktNapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/CardFilter.ktOapp_camera_refactor/src/main/java/com/erroranalysis/app/data/IDeckRepository.ktYapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CropSelectionOverlay.ktWapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/SimpleStudyActivity.ktXapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/settings/FontOptionAdapter.ktLapp_camera_refactor/src/main/java/com/erroranalysis/app/di/ServiceLocator.ktRapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/OpenCVInitializer.ktOapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/FontManager.ktMapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/StudyCard.ktNapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/OpenCVManager.kt[app_camera_refactor/src/main/java/com/erroranalysis/app/domain/camera/model/CameraModels.ktXapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/adapters/IconAdapter.ktYapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/adapters/ColorAdapter.ktPapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/GeminiAIService.ktUapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/PerspectiveCorrector.kt\app_camera_refactor/src/main/java/com/erroranalysis/app/ui/settings/BackupRestoreActivity.ktWapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailViewModel.ktRapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/DocumentProcessor.ktYapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/SimpleCameraActivity.ktJapp_camera_refactor/src/main/java/com/erroranalysis/app/di/CameraModule.kt\app_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/DocumentBoundaryOverlay.ktWapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/BatchImportActivity.ktTapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/CardEditActivity.ktRapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/FocusAnalyzer.ktQapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/MarkdownRenderer.ktOapp_camera_refactor/src/main/java/com/erroranalysis/app/data/DeckDataManager.ktQapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/MathFormatHelper.ktVapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/FontSelectorDialog.ktYapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/DocumentBoundaryDetector.ktTapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CameraViewModel.ktVapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/PhotoEditActivity.ktTapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/SimpleBackupManager.kt]app_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/adapters/StudyCardAdapter.ktRapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/FontExtensions.ktVapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailActivity.ktVapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/widgets/RichTextEditText.kt_app_camera_refactor/src/main/java/com/erroranalysis/app/domain/camera/usecase/CameraUseCases.ktWapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/settings/SettingsActivity.kt^app_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckDetailViewModelFactory.ktSapp_camera_refactor/src/main/java/com/erroranalysis/app/ErrorAnalysisApplication.ktJapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/OCRHelper.ktZapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CombinedImageAnalyzer.ktPapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/ThemeManager.ktNapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/DeckFilter.kt^app_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/adapters/SimpleDeckAdapter.kt\app_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CropOverlayTestActivity.ktWapp_camera_refactor/src/main/java/com/erroranalysis/app/data/camera/CameraRepository.ktTapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/AiSolveActivity.ktXapp_camera_refactor/src/main/java/com/erroranalysis/app/data/camera/ICameraRepository.kt^app_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/DocumentDetectionAnalyzer.ktQapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/base/ThemedActivity.ktLapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/AppTheme.ktMapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/study/StudyDeck.ktUapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/main/SimpleMainActivity.ktWapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/theme/FontSelectorAdapter.ktYapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/DeckSelectionAdapter.ktZapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/GridSpacingItemDecoration.ktSapp_camera_refactor/src/main/java/com/erroranalysis/app/utils/BatchImportManager.ktSapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/camera/CameraActivity.ktaapp_camera_refactor/src/main/java/com/erroranalysis/app/ui/test/DocumentCorrectionTestActivity.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      