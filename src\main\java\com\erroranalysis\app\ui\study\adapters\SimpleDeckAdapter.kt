package com.erroranalysis.app.ui.study.adapters

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.erroranalysis.app.R
import com.erroranalysis.app.databinding.ItemSimpleDeckBinding
import com.erroranalysis.app.ui.study.SimpleDeck

/**
 * 簡化版卡組適配器
 */
class SimpleDeckAdapter(
    private val onDeckClick: (SimpleDeck) -> Unit,
    private val onDeckLongClick: (SimpleDeck) -> Unit,
    private val onStarClick: (SimpleDeck) -> Unit = {}
) : ListAdapter<SimpleDeck, SimpleDeckAdapter.DeckViewHolder>(DeckDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeckViewHolder {
        val binding = ItemSimpleDeckBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return DeckViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: DeckViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class DeckViewHolder(
        private val binding: ItemSimpleDeckBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(deck: SimpleDeck) {
            binding.textDeckName.text = deck.name
            binding.textCoverIcon.text = deck.icon

            // 使用findViewById來訪問新的卡片數量TextView
            val textCardCountNumber = binding.root.findViewById<TextView>(R.id.text_card_count_number)
            textCardCountNumber?.text = deck.cardCount.toString()

            // 如果卡片數量為0，隱藏圓形背景
            val countFrame = textCardCountNumber?.parent as? FrameLayout
            if (deck.cardCount == 0) {
                countFrame?.visibility = android.view.View.GONE
            } else {
                countFrame?.visibility = android.view.View.VISIBLE
            }

            // 設置星星標記 - 始終顯示，根據狀態改變樣式
            val textStarIcon = binding.root.findViewById<TextView>(R.id.text_star_icon)
            textStarIcon?.let { starIcon ->
                if (deck.isStarred) {
                    // 重要狀態：藍色背景，白色星星
                    starIcon.setBackgroundResource(R.drawable.circle_background_blue)
                    starIcon.setTextColor(android.graphics.Color.WHITE)
                } else {
                    // 普通狀態：灰色背景，灰色星星
                    starIcon.setBackgroundResource(R.drawable.circle_background_gray)
                    starIcon.setTextColor(binding.root.context.getColor(R.color.text_secondary))
                }

                // 設置星星點擊事件
                starIcon.setOnClickListener {
                    onStarClick(deck)
                }
            }

            // 設置點擊事件
            binding.root.setOnClickListener {
                onDeckClick(deck)
            }

            binding.root.setOnLongClickListener {
                onDeckLongClick(deck)
                true
            }
        }
        
        private fun darkenColor(color: Int, factor: Float): Int {
            val a = Color.alpha(color)
            val r = (Color.red(color) * (1 - factor)).toInt()
            val g = (Color.green(color) * (1 - factor)).toInt()
            val b = (Color.blue(color) * (1 - factor)).toInt()
            return Color.argb(a, r, g, b)
        }
    }
    
    private class DeckDiffCallback : DiffUtil.ItemCallback<SimpleDeck>() {
        override fun areItemsTheSame(oldItem: SimpleDeck, newItem: SimpleDeck): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: SimpleDeck, newItem: SimpleDeck): Boolean {
            return oldItem == newItem
        }
    }
}
