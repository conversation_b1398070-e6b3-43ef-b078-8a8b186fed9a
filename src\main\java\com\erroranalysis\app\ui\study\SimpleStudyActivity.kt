package com.erroranalysis.app.ui.study

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle

import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AlertDialog
import com.erroranalysis.app.ui.base.ThemedActivity
import com.erroranalysis.app.ui.theme.AppTheme
import androidx.recyclerview.widget.GridLayoutManager
import com.erroranalysis.app.R
import com.erroranalysis.app.databinding.ActivitySimpleStudyBinding
import com.erroranalysis.app.ui.study.adapters.SimpleDeckAdapter
import com.erroranalysis.app.ui.study.adapters.IconAdapter
import com.erroranalysis.app.ui.study.adapters.IconCollection
import com.erroranalysis.app.ui.settings.SettingsActivity
import com.erroranalysis.app.data.DeckDataManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.textfield.TextInputEditText

/**
 * 簡化版記憶庫主畫面
 * 最小可行版本，專注於核心功能
 */
class SimpleStudyActivity : ThemedActivity() {

    companion object {
        private const val REQUEST_BATCH_IMPORT = 1001
    }

    private lateinit var binding: ActivitySimpleStudyBinding
    private lateinit var deckAdapter: SimpleDeckAdapter
    private lateinit var dataManager: DeckDataManager
    private val deckList = mutableListOf<SimpleDeck>()
    private val filteredDeckList = mutableListOf<SimpleDeck>()
    private var currentSortType = DeckSortType.CREATED_TIME_DESC
    private var currentFilter = DeckFilter()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySimpleStudyBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 初始化資料管理器
        dataManager = DeckDataManager(this)

        setupToolbar()
        setupRecyclerView()
        setupFab()
        setupSearchAndSort()
        loadData()

        // 應用主題
        applyTheme()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.title = "記憶庫"
        // 啟用返回按鈕
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
    }
    
    private fun setupRecyclerView() {
        deckAdapter = SimpleDeckAdapter(
            onDeckClick = { deck -> openDeck(deck) },
            onDeckLongClick = { deck -> showDeckOptions(deck) },
            onStarClick = { deck -> toggleDeckStar(deck) }
        )

        binding.recyclerDecks.apply {
            layoutManager = GridLayoutManager(this@SimpleStudyActivity, getSpanCount())
            adapter = deckAdapter
        }
    }

    /**
     * 根據螢幕方向和寬度計算網格列數
     */
    private fun getSpanCount(): Int {
        val orientation = resources.configuration.orientation
        val screenWidthDp = resources.configuration.screenWidthDp

        return when {
            // 橫屏模式
            orientation == Configuration.ORIENTATION_LANDSCAPE -> {
                when {
                    screenWidthDp >= 900 -> 5  // 大平板橫屏
                    screenWidthDp >= 700 -> 4  // 小平板或大手機橫屏
                    else -> 3                  // 一般手機橫屏
                }
            }
            // 直屏模式
            else -> {
                when {
                    screenWidthDp >= 600 -> 3  // 平板直屏
                    else -> 2                  // 手機直屏
                }
            }
        }
    }
    
    private fun setupFab() {
        binding.fabCreateDeck.setOnClickListener {
            showCreateDeckDialog()
        }

        // 長按FAB顯示更多選項
        binding.fabCreateDeck.setOnLongClickListener {
            showMoreOptions()
            true
        }
    }

    private fun setupSearchAndSort() {
        // 搜尋功能
        binding.editSearch.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                currentFilter = currentFilter.copy(keyword = s.toString())
                applyFilterAndSort()
            }
        })

        // 篩選按鈕
        binding.btnFilter.setOnClickListener {
            showFilterDialog()
        }

        // 排序按鈕
        binding.btnSort.setOnClickListener {
            showSortDialog()
        }
    }


    
    private fun loadData() {
        // 檢查並修復資料完整性
        dataManager.validateDataIntegrity()
        dataManager.fixDataIntegrity()

        // 從資料管理器載入資料
        deckList.clear()
        deckList.addAll(dataManager.loadDecks())

        // 應用排序和篩選
        applyFilterAndSort()
    }

    private fun applyFilterAndSort() {
        filteredDeckList.clear()

        // 應用篩選條件
        if (currentFilter.hasFilter()) {
            filteredDeckList.addAll(deckList.filter { currentFilter.matches(it) })
        } else {
            filteredDeckList.addAll(deckList)
        }

        // 應用排序
        sortDecks()
        deckAdapter.submitList(filteredDeckList.toList())
        updateEmptyState()
    }

    private fun sortDecks() {
        when (currentSortType) {
            DeckSortType.CREATED_TIME_DESC -> filteredDeckList.sortByDescending { it.createdAt }
            DeckSortType.CREATED_TIME_ASC -> filteredDeckList.sortBy { it.createdAt }
            DeckSortType.STARRED_FIRST -> filteredDeckList.sortWith(compareByDescending<SimpleDeck> { it.isStarred }.thenByDescending { it.createdAt })
            DeckSortType.NAME_ASC -> filteredDeckList.sortBy { it.name }
            DeckSortType.NAME_DESC -> filteredDeckList.sortByDescending { it.name }
            DeckSortType.CARD_COUNT_DESC -> filteredDeckList.sortByDescending { it.cardCount }
            DeckSortType.CARD_COUNT_ASC -> filteredDeckList.sortBy { it.cardCount }
        }
    }

    private fun showFilterDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_deck_filter, null)

        // 設置當前篩選條件
        val editKeyword = dialogView.findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.edit_keyword)
        val chipStarred = dialogView.findViewById<com.google.android.material.chip.Chip>(R.id.chip_starred)
        val chipNotStarred = dialogView.findViewById<com.google.android.material.chip.Chip>(R.id.chip_not_starred)
        val chipEmpty = dialogView.findViewById<com.google.android.material.chip.Chip>(R.id.chip_empty)
        val chipFewCards = dialogView.findViewById<com.google.android.material.chip.Chip>(R.id.chip_few_cards)
        val chipManyCards = dialogView.findViewById<com.google.android.material.chip.Chip>(R.id.chip_many_cards)

        editKeyword.setText(currentFilter.keyword)
        chipStarred.isChecked = currentFilter.showStarred
        chipNotStarred.isChecked = currentFilter.showNotStarred
        chipEmpty.isChecked = currentFilter.showEmpty
        chipFewCards.isChecked = currentFilter.showFewCards
        chipManyCards.isChecked = currentFilter.showManyCards

        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .create()

        // 清除按鈕
        dialogView.findViewById<com.google.android.material.button.MaterialButton>(R.id.btn_clear).setOnClickListener {
            currentFilter = DeckFilter()
            binding.editSearch.setText("")
            applyFilterAndSort()
            dialog.dismiss()
        }

        // 套用按鈕
        dialogView.findViewById<com.google.android.material.button.MaterialButton>(R.id.btn_apply).setOnClickListener {
            currentFilter = DeckFilter(
                keyword = editKeyword.text.toString().trim(),
                showStarred = chipStarred.isChecked,
                showNotStarred = chipNotStarred.isChecked,
                showEmpty = chipEmpty.isChecked,
                showFewCards = chipFewCards.isChecked,
                showManyCards = chipManyCards.isChecked
            )
            binding.editSearch.setText(currentFilter.keyword)
            applyFilterAndSort()
            dialog.dismiss()
        }

        dialog.show()
    }

    private fun showSortDialog() {
        val sortOptions = DeckSortType.values().map { it.displayName }.toTypedArray()
        val currentIndex = DeckSortType.values().indexOf(currentSortType)

        AlertDialog.Builder(this)
            .setTitle("排序方式")
            .setSingleChoiceItems(sortOptions, currentIndex) { dialog, which ->
                currentSortType = DeckSortType.values()[which]
                applyFilterAndSort()
                dialog.dismiss()
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun showCreateDeckDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_simple_create_deck, null)
        val editName = dialogView.findViewById<TextInputEditText>(R.id.edit_deck_name)
        val editSubject = dialogView.findViewById<TextInputEditText>(R.id.edit_subject)
        val recyclerIcons = dialogView.findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.recycler_icons)

        // 設置圖標選擇器
        var selectedIcon = "🎯" // 預設圖標
        val allIcons = IconCollection.getAllIcons()
        android.util.Log.d("SimpleStudyActivity", "載入圖標數量: ${allIcons.size}")

        val iconAdapter = IconAdapter(allIcons) { icon ->
            selectedIcon = icon
            android.util.Log.d("SimpleStudyActivity", "選擇圖標: $icon")
        }
        iconAdapter.setSelectedIcon(selectedIcon) // 設置初始選中狀態

        recyclerIcons.apply {
            layoutManager = LinearLayoutManager(this@SimpleStudyActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = iconAdapter
            isNestedScrollingEnabled = false
            android.util.Log.d("SimpleStudyActivity", "設置圖標RecyclerView")
        }

        AlertDialog.Builder(this)
            .setTitle("建立新卡組")
            .setView(dialogView)
            .setPositiveButton("建立") { _, _ ->
                val name = editName.text.toString().trim()
                val subject = editSubject.text.toString().trim().ifEmpty { "其他" }

                if (name.isNotEmpty()) {
                    createDeck(name, subject, selectedIcon)
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun createDeck(name: String, subject: String, customIcon: String? = null) {
        val newDeck = SimpleDeck(
            id = dataManager.generateNewDeckId(),
            name = name,
            description = "新建立的$subject 卡組",
            icon = customIcon ?: getSubjectIcon(subject),
            color = getSubjectColor(subject),
            cardCount = 0,
            createdAt = System.currentTimeMillis(),
            isStarred = false
        )

        // 保存到資料管理器
        dataManager.addDeck(newDeck.name, newDeck.description)

        // 更新UI
        deckList.add(newDeck)
        deckAdapter.submitList(deckList.toList())
        updateEmptyState()
    }
    
    private fun getSubjectIcon(subject: String): String {
        return when (subject.lowercase()) {
            "數學" -> "🧮"  // 算盤，更直觀
            "物理" -> "⚡"  // 閃電，代表物理現象
            "化學" -> "🧪"  // 試管，化學實驗
            "生物" -> "🌱"  // 幼苗，生命科學
            "英文" -> "🌍"  // 地球，國際語言
            "國文" -> "🖋️"  // 鋼筆，文學書寫
            "歷史" -> "🏛️"  // 古建築，歷史文明
            "地理" -> "🗺️"  // 地圖，地理探索
            "程式" -> "💻"  // 電腦，程式設計
            else -> "🎯"    // 靶心，學習目標
        }
    }
    
    private fun getSubjectColor(subject: String): String {
        return when (subject.lowercase()) {
            "數學" -> "#4A90E2"
            "物理" -> "#F5A623"
            "化學" -> "#7ED321"
            "生物" -> "#50E3C2"
            "英文" -> "#D0021B"
            else -> "#6B7280"
        }
    }
    
    private fun openDeck(deck: SimpleDeck) {
        // 開啟卡組詳細頁面
        val intent = Intent(this, DeckDetailActivity::class.java)
        intent.putExtra(DeckDetailActivity.EXTRA_DECK, deck)
        startActivity(intent)
    }
    
    private fun showDeckOptions(deck: SimpleDeck) {
        // 隨手記卡組只能編輯，不能刪除
        val options = if (dataManager.isQuickNotesDeck(deck.id)) {
            arrayOf("編輯")
        } else {
            arrayOf("編輯", "刪除")
        }

        AlertDialog.Builder(this)
            .setTitle(deck.name)
            .setItems(options) { _, which ->
                when (which) {
                    0 -> editDeck(deck)
                    1 -> if (!dataManager.isQuickNotesDeck(deck.id)) deleteDeck(deck)
                }
            }
            .show()
    }

    private fun toggleDeckStar(deck: SimpleDeck) {
        val updatedDeck = deck.copy(isStarred = !deck.isStarred)
        dataManager.updateDeck(updatedDeck)

        // 更新本地列表
        val index = deckList.indexOfFirst { it.id == deck.id }
        if (index != -1) {
            deckList[index] = updatedDeck
            applyFilterAndSort()
        }
    }
    
    private fun editDeck(deck: SimpleDeck) {
        val dialogView = layoutInflater.inflate(R.layout.dialog_simple_create_deck, null)
        val editName = dialogView.findViewById<TextInputEditText>(R.id.edit_deck_name)
        val editSubject = dialogView.findViewById<TextInputEditText>(R.id.edit_subject)
        val recyclerIcons = dialogView.findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.recycler_icons)

        editName.setText(deck.name)
        editSubject.setText(deck.description.replace("新建立的", "").replace("更新的", "").replace(" 卡組", ""))

        // 設置圖標選擇器
        var selectedIcon = deck.icon // 使用當前卡組的圖標
        val iconAdapter = IconAdapter(IconCollection.getAllIcons()) { icon ->
            selectedIcon = icon
            android.util.Log.d("SimpleStudyActivity", "編輯時選擇圖標: $icon")
        }
        iconAdapter.setSelectedIcon(selectedIcon) // 設置當前圖標為選中狀態

        recyclerIcons.apply {
            layoutManager = LinearLayoutManager(this@SimpleStudyActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = iconAdapter
            isNestedScrollingEnabled = false
            android.util.Log.d("SimpleStudyActivity", "設置編輯圖標RecyclerView")
        }

        AlertDialog.Builder(this)
            .setTitle("編輯卡組")
            .setView(dialogView)
            .setPositiveButton("更新") { _, _ ->
                val name = editName.text.toString().trim()
                val subject = editSubject.text.toString().trim()

                if (name.isNotEmpty()) {
                    val updatedDeck = deck.copy(
                        name = name,
                        description = "更新的$subject 卡組",
                        icon = selectedIcon, // 使用選擇的圖標
                        color = getSubjectColor(subject)
                    )

                    // 保存到資料管理器
                    dataManager.updateDeck(updatedDeck)

                    // 更新UI
                    val index = deckList.indexOfFirst { it.id == deck.id }
                    if (index != -1) {
                        deckList[index] = updatedDeck
                        deckAdapter.submitList(deckList.toList())
                    }
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun deleteDeck(deck: SimpleDeck) {
        // 雙重檢查，防止刪除隨手記卡組
        if (dataManager.isQuickNotesDeck(deck.id)) {
            Toast.makeText(this, "隨手記卡組無法刪除", Toast.LENGTH_SHORT).show()
            return
        }

        AlertDialog.Builder(this)
            .setTitle("刪除卡組")
            .setMessage("確定要刪除「${deck.name}」嗎？")
            .setPositiveButton("刪除") { _, _ ->
                // 從資料管理器刪除
                dataManager.deleteDeck(deck.id)

                // 更新UI
                deckList.removeAll { it.id == deck.id }
                deckAdapter.submitList(deckList.toList())
                updateEmptyState()
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun updateEmptyState() {
        if (deckList.isEmpty()) {
            binding.layoutEmpty.visibility = android.view.View.VISIBLE
            binding.recyclerDecks.visibility = android.view.View.GONE
        } else {
            binding.layoutEmpty.visibility = android.view.View.GONE
            binding.recyclerDecks.visibility = android.view.View.VISIBLE
        }
    }
    
    override fun onResume() {
        super.onResume()
        // 重新載入資料，確保資料同步
        loadData()
    }

    /**
     * 顯示更多選項
     */
    private fun showMoreOptions() {
        val options = arrayOf("📚 批次匯入題庫", "🔧 調試選項")

        AlertDialog.Builder(this)
            .setTitle("更多功能")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> startBatchImport()
                    1 -> showDebugOptions()
                }
            }
            .show()
    }

    /**
     * 啟動批次匯入
     */
    private fun startBatchImport() {
        val intent = Intent(this, BatchImportActivity::class.java)
        startActivityForResult(intent, REQUEST_BATCH_IMPORT)
    }

    private fun showDebugOptions() {
        val options = arrayOf("檢查資料完整性", "修復資料問題", "強制重置資料", "清除所有資料")

        AlertDialog.Builder(this)
            .setTitle("調試選項")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> {
                        val report = dataManager.validateDataIntegrity()
                        AlertDialog.Builder(this)
                            .setTitle("資料完整性報告")
                            .setMessage(report)
                            .setPositiveButton("確定", null)
                            .show()
                    }
                    1 -> {
                        dataManager.fixDataIntegrity()
                        loadData()
                        Toast.makeText(this, "資料問題已修復", Toast.LENGTH_SHORT).show()
                    }
                    2 -> {
                        AlertDialog.Builder(this)
                            .setTitle("強制重置資料")
                            .setMessage("這將清除所有資料並重新建立預設卡組，用於修復ID衝突問題。確定要繼續嗎？")
                            .setPositiveButton("確定") { _, _ ->
                                // 清除所有資料
                                dataManager.clearAllData()
                                // 重新初始化
                                dataManager = DeckDataManager(this)
                                loadData()
                                Toast.makeText(this, "資料已重置，ID衝突問題已修復", Toast.LENGTH_LONG).show()
                            }
                            .setNegativeButton("取消", null)
                            .show()
                    }
                    3 -> {
                        AlertDialog.Builder(this)
                            .setTitle("清除所有資料")
                            .setMessage("這將刪除所有卡組和卡片，確定要繼續嗎？")
                            .setPositiveButton("確定") { _, _ ->
                                dataManager.clearAllData()
                                loadData()
                                Toast.makeText(this, "所有資料已清除", Toast.LENGTH_SHORT).show()
                            }
                            .setNegativeButton("取消", null)
                            .show()
                    }
                }
            }
            .show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == REQUEST_BATCH_IMPORT && resultCode == RESULT_OK) {
            // 批次匯入完成，重新載入資料
            loadData()
            Toast.makeText(this, "題庫已更新", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        // 返回主頁
        finish()
        return true
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)

        // 螢幕方向改變時，重新設置網格列數
        val newSpanCount = getSpanCount()
        val layoutManager = binding.recyclerDecks.layoutManager as? GridLayoutManager
        layoutManager?.spanCount = newSpanCount

        android.util.Log.d("SimpleStudyActivity", "螢幕方向改變，新列數: $newSpanCount")
    }

    override fun onApplyTheme(theme: AppTheme) {
        // 應用主題到根佈局背景
        findViewById<android.view.View>(android.R.id.content).setBackgroundColor(theme.getBackgroundColorInt())

        // 應用主題到工具欄
        binding.toolbar.setBackgroundColor(theme.getPrimaryColorInt())

        // 應用主題到FAB
        binding.fabCreateDeck.backgroundTintList =
            android.content.res.ColorStateList.valueOf(theme.getPrimaryColorInt())
    }
}

/**
 * 簡化的卡組資料類別
 */
@kotlinx.parcelize.Parcelize
data class SimpleDeck(
    val id: String,
    val name: String,
    val description: String,
    val icon: String,
    val color: String,
    val cardCount: Int,
    val createdAt: Long = System.currentTimeMillis(),
    val isStarred: Boolean = false
) : android.os.Parcelable