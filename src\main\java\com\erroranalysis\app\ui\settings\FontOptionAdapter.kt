package com.erroranalysis.app.ui.settings

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.erroranalysis.app.databinding.ItemFontOptionBinding
import com.erroranalysis.app.ui.theme.FontManager

/**
 * 字體選項適配器
 */
class FontOptionAdapter(
    private val fonts: List<FontManager.FontType>,
    private var selectedFont: FontManager.FontType,
    private val onFontSelected: (FontManager.FontType) -> Unit
) : RecyclerView.Adapter<FontOptionAdapter.FontOptionViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FontOptionViewHolder {
        val binding = ItemFontOptionBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return FontOptionViewHolder(binding)
    }

    override fun onBindViewHolder(holder: FontOptionViewHolder, position: Int) {
        holder.bind(fonts[position])
    }

    override fun getItemCount(): Int = fonts.size

    /**
     * 更新選中的字體
     */
    fun updateSelectedFont(newSelectedFont: FontManager.FontType) {
        val oldPosition = fonts.indexOf(selectedFont)
        val newPosition = fonts.indexOf(newSelectedFont)
        
        selectedFont = newSelectedFont
        
        // 只更新變化的項目
        if (oldPosition != -1) notifyItemChanged(oldPosition)
        if (newPosition != -1) notifyItemChanged(newPosition)
    }

    inner class FontOptionViewHolder(
        private val binding: ItemFontOptionBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(fontType: FontManager.FontType) {
            // 設置字體名稱
            binding.textFontName.text = fontType.displayName
            
            // 設置字體預覽文字
            binding.textFontPreview.text = "題目答案 常在我心"
            
            // 應用字體到預覽文字
            val typeface = FontManager.getTypeface(binding.root.context, fontType)
            if (typeface != null) {
                binding.textFontPreview.typeface = typeface
            } else {
                // 系統字體，使用默認
                binding.textFontPreview.typeface = null
            }
            
            // 設置選中狀態
            binding.radioFont.isChecked = fontType == selectedFont
            
            // 設置點擊事件
            binding.root.setOnClickListener {
                if (fontType != selectedFont) {
                    onFontSelected(fontType)
                }
            }
        }
    }
}
