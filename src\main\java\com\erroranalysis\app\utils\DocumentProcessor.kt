package com.erroranalysis.app.utils

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.PointF
import android.util.Log
import com.erroranalysis.app.domain.camera.model.DocumentProcessResult
import com.erroranalysis.app.domain.camera.model.ImageInfo
import java.io.File
import java.io.FileOutputStream

/**
 * 文檔處理工具類
 * 整合文檔邊界檢測和透視校正功能
 */
class DocumentProcessor {
    
    companion object {
        private const val TAG = "DocumentProcessor"
        private const val CORRECTED_IMAGE_QUALITY = 90
    }
    
    private val boundaryDetector = DocumentBoundaryDetector()
    private val perspectiveCorrector = PerspectiveCorrector()
    
    /**
     * 處理拍攝的圖像
     * @param imagePath 原始圖像路徑
     * @param manualBoundaries 手動調整的邊界點（可選）
     * @param outputWidth 輸出圖像寬度（可選）
     * @param outputHeight 輸出圖像高度（可選）
     * @return 處理結果
     */
    fun processDocument(
        imagePath: String,
        manualBoundaries: List<PointF>? = null,
        outputWidth: Int? = null,
        outputHeight: Int? = null
    ): DocumentProcessResult {
        
        try {
            // 載入原始圖像
            val originalBitmap = BitmapFactory.decodeFile(imagePath)
                ?: return DocumentProcessResult.Error("無法載入圖像")
            
            // 使用手動邊界或自動檢測邊界
            val boundaries = manualBoundaries ?: run {
                val detected = boundaryDetector.detectDocumentBoundary(originalBitmap)
                detected ?: return DocumentProcessResult.Error("無法檢測到文檔邊界")
            }
            
            // 驗證邊界點
            if (!PerspectiveCorrector().validateCorners(boundaries, originalBitmap.width, originalBitmap.height)) {
                return DocumentProcessResult.Error("邊界點無效")
            }
            
            // 執行透視校正
            val correctedBitmap = perspectiveCorrector.correctPerspective(
                originalBitmap, boundaries, outputWidth, outputHeight
            ) ?: return DocumentProcessResult.Error("透視校正失敗")
            
            // 保存校正後的圖像
            val correctedImagePath = saveCorrectedImage(correctedBitmap, imagePath)
            
            // 清理資源
            originalBitmap.recycle()
            correctedBitmap.recycle()
            
            return DocumentProcessResult.Success(
                originalImagePath = imagePath,
                correctedImagePath = correctedImagePath,
                detectedBoundaries = boundaries
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error processing document", e)
            return DocumentProcessResult.Error("處理文檔時發生錯誤: ${e.message}")
        }
    }
    
    /**
     * 僅檢測文檔邊界
     */
    fun detectBoundariesOnly(imagePath: String): List<PointF>? {
        return try {
            val bitmap = BitmapFactory.decodeFile(imagePath) ?: return null
            val boundaries = boundaryDetector.detectDocumentBoundary(bitmap)
            bitmap.recycle()
            boundaries
        } catch (e: Exception) {
            Log.e(TAG, "Error detecting boundaries", e)
            null
        }
    }
    
    /**
     * 僅執行透視校正
     */
    fun correctPerspectiveOnly(
        imagePath: String,
        boundaries: List<PointF>,
        outputWidth: Int? = null,
        outputHeight: Int? = null
    ): String? {
        return try {
            val originalBitmap = BitmapFactory.decodeFile(imagePath) ?: return null
            
            val correctedBitmap = perspectiveCorrector.correctPerspective(
                originalBitmap, boundaries, outputWidth, outputHeight
            ) ?: return null
            
            val correctedImagePath = saveCorrectedImage(correctedBitmap, imagePath)
            
            originalBitmap.recycle()
            correctedBitmap.recycle()
            
            correctedImagePath
        } catch (e: Exception) {
            Log.e(TAG, "Error correcting perspective", e)
            null
        }
    }
    
    /**
     * 保存校正後的圖像
     */
    private fun saveCorrectedImage(bitmap: Bitmap, originalPath: String): String {
        val originalFile = File(originalPath)
        val correctedFile = File(
            originalFile.parent,
            "${originalFile.nameWithoutExtension}_corrected.jpg"
        )
        
        FileOutputStream(correctedFile).use { out ->
            bitmap.compress(Bitmap.CompressFormat.JPEG, CORRECTED_IMAGE_QUALITY, out)
        }
        
        return correctedFile.absolutePath
    }
    
    /**
     * 獲取圖像信息
     */
    fun getImageInfo(imagePath: String): ImageInfo? {
        return try {
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(imagePath, options)
            
            ImageInfo(
                width = options.outWidth,
                height = options.outHeight,
                mimeType = options.outMimeType ?: "image/jpeg",
                fileSize = 0L // 這裡需要實際的檔案大小
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error getting image info", e)
            null
        }
    }
}

// 注意：DocumentProcessResult 和 ImageInfo 已在 domain/camera/model/CameraModels.kt 中定義
