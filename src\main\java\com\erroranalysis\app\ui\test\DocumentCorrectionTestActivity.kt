package com.erroranalysis.app.ui.test

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.PointF
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.erroranalysis.app.R
import com.erroranalysis.app.domain.camera.model.DocumentProcessResult
import com.erroranalysis.app.utils.DocumentBoundaryDetector
import com.erroranalysis.app.utils.DocumentProcessor
import com.erroranalysis.app.utils.OpenCVInitializer
import com.erroranalysis.app.utils.PerspectiveCorrector

/**
 * 文檔校正測試活動
 * 用於測試文檔邊界檢測和透視校正功能
 */
class DocumentCorrectionTestActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "DocumentCorrectionTest"
    }
    
    private val documentProcessor = DocumentProcessor()
    private val boundaryDetector = DocumentBoundaryDetector()
    private val perspectiveCorrector = PerspectiveCorrector()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main) // 使用簡單布局
        
        // 確保OpenCV已初始化
        if (OpenCVInitializer.isInitialized()) {
            runTests()
        } else {
            OpenCVInitializer.initialize(this) { success ->
                if (success) {
                    runTests()
                } else {
                    Toast.makeText(this, "OpenCV初始化失敗", Toast.LENGTH_SHORT).show()
                    finish()
                }
            }
        }
    }
    
    private fun runTests() {
        Log.d(TAG, "開始運行文檔校正測試...")
        
        // 測試1: 創建測試圖像
        testCreateTestImage()
        
        // 測試2: 邊界檢測
        testBoundaryDetection()
        
        // 測試3: 透視校正
        testPerspectiveCorrection()
        
        // 測試4: 完整流程
        testCompleteWorkflow()
        
        Log.d(TAG, "所有測試完成")
        Toast.makeText(this, "文檔校正測試完成，請查看日誌", Toast.LENGTH_LONG).show()
    }
    
    private fun testCreateTestImage() {
        Log.d(TAG, "測試1: 創建測試圖像")
        
        try {
            // 創建一個簡單的測試圖像
            val testBitmap = Bitmap.createBitmap(400, 300, Bitmap.Config.ARGB_8888)
            testBitmap.eraseColor(android.graphics.Color.WHITE)
            
            Log.d(TAG, "✓ 測試圖像創建成功: ${testBitmap.width}x${testBitmap.height}")
            testBitmap.recycle()
        } catch (e: Exception) {
            Log.e(TAG, "✗ 測試圖像創建失敗", e)
        }
    }
    
    private fun testBoundaryDetection() {
        Log.d(TAG, "測試2: 邊界檢測")
        
        try {
            // 創建一個帶有明顯邊界的測試圖像
            val testBitmap = createDocumentTestImage()
            
            val boundaries = boundaryDetector.detectDocumentBoundary(testBitmap)
            
            if (boundaries != null && boundaries.size == 4) {
                Log.d(TAG, "✓ 邊界檢測成功，檢測到 ${boundaries.size} 個角點")
                boundaries.forEachIndexed { index, point ->
                    Log.d(TAG, "  角點 $index: (${point.x}, ${point.y})")
                }
            } else {
                Log.w(TAG, "✗ 邊界檢測失敗或檢測到的角點數量不正確")
            }
            
            testBitmap.recycle()
        } catch (e: Exception) {
            Log.e(TAG, "✗ 邊界檢測測試失敗", e)
        }
    }
    
    private fun testPerspectiveCorrection() {
        Log.d(TAG, "測試3: 透視校正")
        
        try {
            val testBitmap = createDocumentTestImage()
            
            // 定義測試角點（模擬梯形文檔）
            val testCorners = listOf(
                PointF(50f, 50f),   // 左上
                PointF(350f, 80f),  // 右上
                PointF(320f, 250f), // 右下
                PointF(80f, 220f)   // 左下
            )
            
            val correctedBitmap = perspectiveCorrector.correctPerspective(
                testBitmap, testCorners, 300, 200
            )
            
            if (correctedBitmap != null) {
                Log.d(TAG, "✓ 透視校正成功: ${correctedBitmap.width}x${correctedBitmap.height}")
                correctedBitmap.recycle()
            } else {
                Log.w(TAG, "✗ 透視校正失敗")
            }
            
            testBitmap.recycle()
        } catch (e: Exception) {
            Log.e(TAG, "✗ 透視校正測試失敗", e)
        }
    }
    
    private fun testCompleteWorkflow() {
        Log.d(TAG, "測試4: 完整工作流程")
        
        try {
            // 這裡應該使用真實的圖像文件路徑進行測試
            // 由於我們沒有真實圖像，這個測試會跳過
            Log.d(TAG, "⚠ 完整工作流程測試需要真實圖像文件，跳過此測試")
            
            // 如果有測試圖像文件，可以這樣測試：
            // val result = documentProcessor.processDocument("/path/to/test/image.jpg")
            // when (result) {
            //     is DocumentProcessResult.Success -> Log.d(TAG, "✓ 完整流程測試成功")
            //     is DocumentProcessResult.Error -> Log.w(TAG, "✗ 完整流程測試失敗: ${result.message}")
            // }
            
        } catch (e: Exception) {
            Log.e(TAG, "✗ 完整工作流程測試失敗", e)
        }
    }
    
    /**
     * 創建一個模擬文檔的測試圖像
     */
    private fun createDocumentTestImage(): Bitmap {
        val bitmap = Bitmap.createBitmap(400, 300, Bitmap.Config.ARGB_8888)
        val canvas = android.graphics.Canvas(bitmap)
        
        // 填充背景
        canvas.drawColor(android.graphics.Color.LTGRAY)
        
        // 繪製一個白色矩形模擬文檔
        val paint = android.graphics.Paint().apply {
            color = android.graphics.Color.WHITE
            style = android.graphics.Paint.Style.FILL
        }
        
        canvas.drawRect(80f, 60f, 320f, 240f, paint)
        
        // 繪製邊框
        paint.apply {
            color = android.graphics.Color.BLACK
            style = android.graphics.Paint.Style.STROKE
            strokeWidth = 3f
        }
        canvas.drawRect(80f, 60f, 320f, 240f, paint)
        
        return bitmap
    }
}
