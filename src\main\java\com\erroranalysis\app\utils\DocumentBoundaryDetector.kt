package com.erroranalysis.app.utils

import android.graphics.Bitmap
import android.graphics.PointF
import android.util.Log
import org.opencv.android.Utils
import org.opencv.core.*
import org.opencv.imgproc.Imgproc
import kotlin.math.*

/**
 * 基於 OpenCV 的文檔邊界檢測器
 * 使用先進的圖像處理技術檢測文檔的四個角點
 */
class DocumentBoundaryDetector {

    companion object {
        private const val TAG = "DocumentBoundaryDetector"

        // 檢測參數 - 針對考卷檢測優化
        private const val GAUSSIAN_BLUR_SIZE = 5
        private const val CANNY_THRESHOLD_1 = 50.0
        private const val CANNY_THRESHOLD_2 = 150.0
        private const val CONTOUR_APPROXIMATION_EPSILON = 0.02
        private const val MIN_CONTOUR_AREA_RATIO = 0.1  // 提高最小面積要求，確保檢測到主要物件

        // 形態學操作參數
        private const val MORPH_KERNEL_SIZE = 3
        private const val DILATE_ITERATIONS = 2
        private const val ERODE_ITERATIONS = 1

        // 圖像縮放參數（用於提高檢測速度）
        private const val DETECTION_IMAGE_WIDTH = 800.0

        // 中央區域優先檢測參數
        private const val CENTER_REGION_RATIO = 0.8  // 中央區域佔圖像的比例（增加到80%）
        private const val CENTER_WEIGHT_BONUS = 5.0  // 中央區域的權重加成（增加權重）
        private const val CENTER_STRICT_RATIO = 0.4  // 嚴格中央區域比例

        // 矩形度檢查參數（更嚴格）
        private const val MIN_RECTANGULARITY = 0.8  // 最小矩形度（提高到0.8）
        private const val MAX_ASPECT_RATIO = 1.8    // 最大長寬比（更接近正方形）
        private const val MIN_ASPECT_RATIO = 0.6    // 最小長寬比（更接近正方形）

        // 文字檢測參數
        private const val TEXT_LINE_MIN_LENGTH = 50   // 文字行最小長度
        private const val TEXT_LINE_MAX_GAP = 10      // 文字行最大間隙
        private const val HORIZONTAL_TEXT_ANGLE_THRESHOLD = 15.0  // 水平文字角度閾值
    }
    
    /**
     * 檢測文檔邊界
     * @param bitmap 輸入圖像
     * @return 檢測到的四個角點，如果未檢測到則返回null
     */
    fun detectDocumentBoundary(bitmap: Bitmap): List<PointF>? {
        if (!OpenCVManager.isInitialized()) {
            Log.w(TAG, "OpenCV 未初始化，使用默認邊界")
            return getDefaultBoundary(bitmap)
        }

        try {
            Log.d(TAG, "開始 OpenCV 文檔邊界檢測，圖像尺寸: ${bitmap.width}x${bitmap.height}")

            // 將 Bitmap 轉換為 OpenCV Mat
            val originalMat = Mat()
            Utils.bitmapToMat(bitmap, originalMat)

            // 縮放圖像以提高檢測速度和準確性
            val (scaledMat, scaleFactor) = scaleImageForDetection(originalMat)

            // 嘗試多種檢測策略，優先考慮中央區域和矩形度
            var detectedCorners: List<PointF>? = null

            // 策略1：智能中央矩形檢測（基於文字分析的中央矩形）
            val smartDetectionResult = trySmartCenterRectangleDetection(scaledMat)
            detectedCorners = smartDetectionResult?.let { extractCorners(it) }

            // 策略2：如果智能檢測失敗，使用純中央矩形
            if (detectedCorners == null) {
                Log.d(TAG, "智能中央檢測失敗，使用標準中央矩形")
                val centerRectangle = generateOptimalCenterRectangle(scaledMat.size())
                detectedCorners = extractCorners(centerRectangle)
                centerRectangle.release()
            }

            smartDetectionResult?.release()

            // 釋放資源
            originalMat.release()
            scaledMat.release()

            return if (detectedCorners != null) {
                // 將檢測到的角點縮放回原始圖像尺寸
                val scaledBackCorners = detectedCorners.map {
                    PointF(it.x / scaleFactor, it.y / scaleFactor)
                }
                Log.i(TAG, "🎯 成功檢測到文檔邊界！角點: $scaledBackCorners")
                Log.i(TAG, "📐 檢測區域面積比例: ${calculateAreaRatio(scaledBackCorners, bitmap.width, bitmap.height)}")
                scaledBackCorners
            } else {
                Log.w(TAG, "❌ 所有檢測策略都失敗，使用默認邊界")
                Log.w(TAG, "💡 建議：確保文檔與背景有足夠對比度，文檔完整在畫面內")
                null  // 返回 null 而不是默認邊界，讓上層決定如何處理
            }

        } catch (e: Exception) {
            Log.e(TAG, "OpenCV 文檔邊界檢測失敗", e)
            return getDefaultBoundary(bitmap)
        }
    }

    /**
     * 縮放圖像以提高檢測效率
     */
    private fun scaleImageForDetection(originalMat: Mat): Pair<Mat, Float> {
        val originalWidth = originalMat.width().toDouble()
        val originalHeight = originalMat.height().toDouble()

        if (originalWidth <= DETECTION_IMAGE_WIDTH) {
            // 如果圖像已經足夠小，直接返回
            return Pair(originalMat.clone(), 1.0f)
        }

        val scaleFactor = (DETECTION_IMAGE_WIDTH / originalWidth).toFloat()
        val newHeight = (originalHeight * scaleFactor).toInt()

        val scaledMat = Mat()
        Imgproc.resize(originalMat, scaledMat, Size(DETECTION_IMAGE_WIDTH, newHeight.toDouble()))

        Log.d(TAG, "圖像縮放: ${originalWidth}x${originalHeight} -> ${DETECTION_IMAGE_WIDTH}x${newHeight}, 縮放比例: $scaleFactor")
        return Pair(scaledMat, scaleFactor)
    }

    /**
     * 針對考卷優化的檢測策略
     * 優先檢測中央區域的矩形物件
     */
    private fun tryExamPaperDetection(mat: Mat): List<PointF>? {
        return try {
            val processedMat = preprocessImageForExamPaper(mat)
            val contours = detectContours(processedMat)
            val documentContour = findExamPaperContour(contours, mat.size())

            processedMat.release()
            contours.forEach { it.release() }

            documentContour?.let { contour ->
                val corners = extractCorners(contour)
                contour.release()
                // 驗證檢測到的角點是否符合考卷特徵
                if (corners != null && validateExamPaperCorners(corners, mat.size())) {
                    // 進一步檢查是否在中央區域
                    if (isBoundaryInCenterRegion(corners, mat.size())) {
                        corners
                    } else {
                        Log.d(TAG, "檢測到的邊界不在中央區域，生成中央默認邊界")
                        generateCenterDefaultBoundary(mat.size())
                    }
                } else {
                    Log.d(TAG, "檢測到的角點不符合考卷特徵")
                    null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "考卷檢測失敗", e)
            null
        }
    }

    /**
     * 標準邊緣檢測策略
     */
    private fun tryStandardDetection(mat: Mat): List<PointF>? {
        return try {
            val processedMat = preprocessImage(mat)
            val contours = detectContours(processedMat)
            val documentContour = findDocumentContour(contours, mat.size())

            processedMat.release()
            contours.forEach { it.release() }

            documentContour?.let { contour ->
                val corners = extractCorners(contour)
                contour.release()
                corners
            }
        } catch (e: Exception) {
            Log.e(TAG, "標準檢測失敗", e)
            null
        }
    }

    /**
     * 基於文字特徵的檢測策略
     * 利用文字的水平排列特徵來檢測文檔邊界
     */
    private fun tryTextBasedDetection(mat: Mat): List<PointF>? {
        return try {
            Log.d(TAG, "開始基於文字特徵的檢測")

            // 1. 文字檢測預處理
            val textMat = preprocessForTextDetection(mat)

            // 2. 檢測水平文字行
            val textLines = detectHorizontalTextLines(textMat)

            // 3. 基於文字行分布推斷文檔邊界
            val documentBounds = inferDocumentBoundsFromText(textLines, mat.size())

            textMat.release()

            if (documentBounds != null) {
                Log.d(TAG, "✅ 基於文字特徵檢測成功")
                documentBounds
            } else {
                Log.d(TAG, "❌ 文字特徵檢測失敗")
                null
            }

        } catch (e: Exception) {
            Log.e(TAG, "文字特徵檢測失敗", e)
            null
        }
    }

    /**
     * 寬鬆參數檢測策略
     */
    private fun tryRelaxedDetection(mat: Mat): List<PointF>? {
        return try {
            val processedMat = preprocessImageRelaxed(mat)
            val contours = detectContours(processedMat)
            val documentContour = findDocumentContourRelaxed(contours, mat.size())

            processedMat.release()
            contours.forEach { it.release() }

            documentContour?.let { contour ->
                val corners = extractCorners(contour)
                contour.release()
                corners
            }
        } catch (e: Exception) {
            Log.e(TAG, "寬鬆檢測失敗", e)
            null
        }
    }

    /**
     * 基於顏色的檢測策略
     */
    private fun tryColorBasedDetection(mat: Mat): List<PointF>? {
        return try {
            val processedMat = preprocessImageColorBased(mat)
            val contours = detectContours(processedMat)
            val documentContour = findDocumentContourRelaxed(contours, mat.size())

            processedMat.release()
            contours.forEach { it.release() }

            documentContour?.let { contour ->
                val corners = extractCorners(contour)
                contour.release()
                corners
            }
        } catch (e: Exception) {
            Log.e(TAG, "顏色檢測失敗", e)
            null
        }
    }

    /**
     * 針對考卷的圖像預處理
     * 專門針對淺色紙張上的黑色文字進行優化
     */
    private fun preprocessImageForExamPaper(originalMat: Mat): Mat {
        val grayMat = Mat()
        val blurredMat = Mat()
        val threshMat = Mat()
        val morphMat = Mat()
        val edgesMat = Mat()

        try {
            // 轉換為灰度圖
            Imgproc.cvtColor(originalMat, grayMat, Imgproc.COLOR_BGR2GRAY)

            // 輕微高斯模糊去噪，保留邊緣細節
            val kernelSize = Size(3.0, 3.0)
            Imgproc.GaussianBlur(grayMat, blurredMat, kernelSize, 0.0)

            // 使用自適應閾值化，更適合不同光照條件
            Imgproc.adaptiveThreshold(
                blurredMat, threshMat, 255.0,
                Imgproc.ADAPTIVE_THRESH_GAUSSIAN_C,
                Imgproc.THRESH_BINARY,
                15, 3.0
            )

            // 形態學操作去除噪點並連接邊緣
            val kernel = Imgproc.getStructuringElement(
                Imgproc.MORPH_RECT,
                Size(3.0, 3.0)
            )

            // 先腐蝕去除小噪點
            Imgproc.erode(threshMat, morphMat, kernel, org.opencv.core.Point(-1.0, -1.0), 1)

            // 再膨脹恢復主要結構
            Imgproc.dilate(morphMat, morphMat, kernel, org.opencv.core.Point(-1.0, -1.0), 2)

            // Canny 邊緣檢測，使用適中的閾值
            Imgproc.Canny(morphMat, edgesMat, 50.0, 150.0)

            // 最後膨脹以加強邊緣連接
            val edgeKernel = Imgproc.getStructuringElement(
                Imgproc.MORPH_RECT,
                Size(2.0, 2.0)
            )
            Imgproc.dilate(edgesMat, edgesMat, edgeKernel, org.opencv.core.Point(-1.0, -1.0), 1)
            edgeKernel.release()

            kernel.release()

        } finally {
            // 釋放中間結果
            grayMat.release()
            blurredMat.release()
            threshMat.release()
            morphMat.release()
        }

        return edgesMat
    }

    /**
     * 預處理圖像
     */
    private fun preprocessImage(originalMat: Mat): Mat {
        val grayMat = Mat()
        val blurredMat = Mat()
        val adaptiveThreshMat = Mat()
        val morphMat = Mat()
        val edgesMat = Mat()

        try {
            // 轉換為灰度圖
            Imgproc.cvtColor(originalMat, grayMat, Imgproc.COLOR_BGR2GRAY)

            // 高斯模糊去噪
            val kernelSize = Size(GAUSSIAN_BLUR_SIZE.toDouble(), GAUSSIAN_BLUR_SIZE.toDouble())
            Imgproc.GaussianBlur(grayMat, blurredMat, kernelSize, 0.0)

            // 自適應閾值化增強對比度
            Imgproc.adaptiveThreshold(
                blurredMat, adaptiveThreshMat,
                255.0,
                Imgproc.ADAPTIVE_THRESH_GAUSSIAN_C,
                Imgproc.THRESH_BINARY,
                11, 2.0
            )

            // 形態學操作改善邊緣連接
            val kernel = Imgproc.getStructuringElement(
                Imgproc.MORPH_RECT,
                Size(MORPH_KERNEL_SIZE.toDouble(), MORPH_KERNEL_SIZE.toDouble())
            )

            // 膨脹操作
            Imgproc.dilate(adaptiveThreshMat, morphMat, kernel, org.opencv.core.Point(-1.0, -1.0), DILATE_ITERATIONS)

            // 腐蝕操作
            Imgproc.erode(morphMat, morphMat, kernel, org.opencv.core.Point(-1.0, -1.0), ERODE_ITERATIONS)

            // Canny 邊緣檢測
            Imgproc.Canny(morphMat, edgesMat, CANNY_THRESHOLD_1, CANNY_THRESHOLD_2)

            // 再次膨脹以連接斷開的邊緣
            Imgproc.dilate(edgesMat, edgesMat, kernel, org.opencv.core.Point(-1.0, -1.0), 1)

            kernel.release()

        } finally {
            // 釋放中間結果
            grayMat.release()
            blurredMat.release()
            adaptiveThreshMat.release()
            morphMat.release()
        }

        return edgesMat
    }

    /**
     * 寬鬆參數的圖像預處理
     */
    private fun preprocessImageRelaxed(originalMat: Mat): Mat {
        val grayMat = Mat()
        val blurredMat = Mat()
        val edgesMat = Mat()

        try {
            // 轉換為灰度圖
            Imgproc.cvtColor(originalMat, grayMat, Imgproc.COLOR_BGR2GRAY)

            // 更強的高斯模糊
            val kernelSize = Size(7.0, 7.0)
            Imgproc.GaussianBlur(grayMat, blurredMat, kernelSize, 0.0)

            // 更寬鬆的 Canny 閾值
            Imgproc.Canny(blurredMat, edgesMat, 30.0, 100.0)

            // 更強的膨脹操作
            val kernel = Imgproc.getStructuringElement(
                Imgproc.MORPH_RECT,
                Size(5.0, 5.0)
            )
            Imgproc.dilate(edgesMat, edgesMat, kernel, org.opencv.core.Point(-1.0, -1.0), 2)

            kernel.release()

        } finally {
            grayMat.release()
            blurredMat.release()
        }

        return edgesMat
    }

    /**
     * 基於顏色分割的圖像預處理
     */
    private fun preprocessImageColorBased(originalMat: Mat): Mat {
        val hsvMat = Mat()
        val maskMat = Mat()
        val edgesMat = Mat()

        try {
            // 轉換到 HSV 色彩空間
            Imgproc.cvtColor(originalMat, hsvMat, Imgproc.COLOR_BGR2HSV)

            // 創建白色/淺色區域的遮罩（假設文檔是白色或淺色）
            val lowerBound = Scalar(0.0, 0.0, 180.0)  // 低飽和度，高亮度
            val upperBound = Scalar(180.0, 30.0, 255.0)
            Core.inRange(hsvMat, lowerBound, upperBound, maskMat)

            // 形態學操作清理遮罩
            val kernel = Imgproc.getStructuringElement(
                Imgproc.MORPH_RECT,
                Size(5.0, 5.0)
            )
            Imgproc.morphologyEx(maskMat, maskMat, Imgproc.MORPH_CLOSE, kernel)
            Imgproc.morphologyEx(maskMat, maskMat, Imgproc.MORPH_OPEN, kernel)

            // 邊緣檢測
            Imgproc.Canny(maskMat, edgesMat, 50.0, 150.0)

            kernel.release()

        } finally {
            hsvMat.release()
            maskMat.release()
        }

        return edgesMat
    }

    /**
     * 檢測輪廓
     */
    private fun detectContours(edgesMat: Mat): List<MatOfPoint> {
        val contours = mutableListOf<MatOfPoint>()
        val hierarchy = Mat()

        Imgproc.findContours(
            edgesMat,
            contours,
            hierarchy,
            Imgproc.RETR_EXTERNAL,
            Imgproc.CHAIN_APPROX_SIMPLE
        )

        hierarchy.release()
        return contours
    }

    /**
     * 找到最佳的考卷輪廓
     * 優先考慮中央區域和矩形度
     */
    private fun findExamPaperContour(contours: List<MatOfPoint>, imageSize: Size): MatOfPoint? {
        val minArea = imageSize.area() * MIN_CONTOUR_AREA_RATIO
        val centerX = imageSize.width / 2
        val centerY = imageSize.height / 2
        val centerRegionWidth = imageSize.width * CENTER_REGION_RATIO
        val centerRegionHeight = imageSize.height * CENTER_REGION_RATIO

        Log.d(TAG, "檢測到 ${contours.size} 個輪廓，最小面積要求: $minArea")
        Log.d(TAG, "中央區域: ${centerX - centerRegionWidth/2} to ${centerX + centerRegionWidth/2}, ${centerY - centerRegionHeight/2} to ${centerY + centerRegionHeight/2}")

        val candidates = contours
            .filter {
                val area = Imgproc.contourArea(it)
                area > minArea
            }
            .mapNotNull { contour ->
                // 嘗試不同的 epsilon 值找到四邊形
                for (epsilonFactor in listOf(0.01, 0.015, 0.02, 0.025, 0.03)) {
                    val epsilon = epsilonFactor * Imgproc.arcLength(MatOfPoint2f(*contour.toArray()), true)
                    val approx = MatOfPoint2f()
                    Imgproc.approxPolyDP(MatOfPoint2f(*contour.toArray()), approx, epsilon, true)

                    if (approx.total() == 4L) {
                        val result = MatOfPoint(*approx.toArray())
                        approx.release()

                        // 計算輪廓的中心點
                        val moments = Imgproc.moments(result)
                        val contourCenterX = moments.m10 / moments.m00
                        val contourCenterY = moments.m01 / moments.m00

                        // 檢查是否在中央區域內
                        val isInCenterRegion = abs(contourCenterX - centerX) < centerRegionWidth / 2 &&
                                              abs(contourCenterY - centerY) < centerRegionHeight / 2

                        // 檢查是否在嚴格中央區域內
                        val strictCenterWidth = imageSize.width * CENTER_STRICT_RATIO
                        val strictCenterHeight = imageSize.height * CENTER_STRICT_RATIO
                        val isInStrictCenter = abs(contourCenterX - centerX) < strictCenterWidth / 2 &&
                                             abs(contourCenterY - centerY) < strictCenterHeight / 2

                        // 檢查矩形度和長寬比
                        val corners = result.toArray().map { PointF(it.x.toFloat(), it.y.toFloat()) }
                        val rectangularity = calculateRectangularity(corners)
                        val aspectRatio = calculateAspectRatio(corners)

                        // 檢查四個角點是否都在合理範圍內
                        val allCornersValid = corners.all { corner ->
                            corner.x >= 0 && corner.x <= imageSize.width &&
                            corner.y >= 0 && corner.y <= imageSize.height
                        }

                        Log.d(TAG, "輪廓中心: ($contourCenterX, $contourCenterY)")
                        Log.d(TAG, "在中央區域: $isInCenterRegion, 在嚴格中央: $isInStrictCenter")
                        Log.d(TAG, "矩形度: $rectangularity, 長寬比: $aspectRatio, 角點有效: $allCornersValid")

                        // 更嚴格的篩選條件
                        if (rectangularity >= MIN_RECTANGULARITY &&
                            aspectRatio >= MIN_ASPECT_RATIO &&
                            aspectRatio <= MAX_ASPECT_RATIO &&
                            allCornersValid) {

                            // 計算權重：嚴格中央區域獲得最高權重
                            val weight = when {
                                isInStrictCenter -> CENTER_WEIGHT_BONUS * 2.0  // 嚴格中央區域雙倍權重
                                isInCenterRegion -> CENTER_WEIGHT_BONUS        // 中央區域正常權重
                                else -> 0.5  // 非中央區域降低權重
                            }

                            return@mapNotNull Pair(result, weight)
                        }
                    }
                    approx.release()
                }
                null
            }

        Log.d(TAG, "找到 ${candidates.size} 個符合考卷特徵的候選輪廓")

        // 根據面積和中央區域權重選擇最佳輪廓
        return candidates.maxByOrNull { (contour, weight) ->
            Imgproc.contourArea(contour) * weight
        }?.first
    }

    /**
     * 找到最佳的文檔輪廓
     */
    private fun findDocumentContour(contours: List<MatOfPoint>, imageSize: Size): MatOfPoint? {
        val minArea = imageSize.area() * MIN_CONTOUR_AREA_RATIO

        return contours
            .filter { Imgproc.contourArea(it) > minArea }
            .mapNotNull { contour ->
                val epsilon = CONTOUR_APPROXIMATION_EPSILON * Imgproc.arcLength(MatOfPoint2f(*contour.toArray()), true)
                val approx = MatOfPoint2f()
                Imgproc.approxPolyDP(MatOfPoint2f(*contour.toArray()), approx, epsilon, true)

                if (approx.total() == 4L) {
                    MatOfPoint(*approx.toArray())
                } else {
                    approx.release()
                    null
                }
            }
            .maxByOrNull { Imgproc.contourArea(it) }
    }

    /**
     * 寬鬆參數的文檔輪廓檢測
     */
    private fun findDocumentContourRelaxed(contours: List<MatOfPoint>, imageSize: Size): MatOfPoint? {
        val minArea = imageSize.area() * 0.02  // 更小的最小面積要求

        Log.d(TAG, "檢測到 ${contours.size} 個輪廓，最小面積要求: $minArea")

        val candidates = contours
            .filter {
                val area = Imgproc.contourArea(it)
                Log.d(TAG, "輪廓面積: $area")
                area > minArea
            }
            .mapNotNull { contour ->
                // 嘗試不同的 epsilon 值
                for (epsilonFactor in listOf(0.01, 0.02, 0.03, 0.05)) {
                    val epsilon = epsilonFactor * Imgproc.arcLength(MatOfPoint2f(*contour.toArray()), true)
                    val approx = MatOfPoint2f()
                    Imgproc.approxPolyDP(MatOfPoint2f(*contour.toArray()), approx, epsilon, true)

                    if (approx.total() == 4L) {
                        Log.d(TAG, "找到四邊形輪廓，epsilon 因子: $epsilonFactor")
                        val result = MatOfPoint(*approx.toArray())
                        approx.release()
                        return@mapNotNull result
                    }
                    approx.release()
                }
                null
            }

        Log.d(TAG, "找到 ${candidates.size} 個四邊形候選")
        return candidates.maxByOrNull { Imgproc.contourArea(it) }
    }

    /**
     * 從輪廓中提取四個角點
     */
    private fun extractCorners(contour: MatOfPoint): List<PointF>? {
        val points = contour.toArray()
        if (points.size != 4) return null

        // 按照左上、右上、右下、左下的順序排序
        val sortedPoints = sortCorners(points)

        return sortedPoints.map { PointF(it.x.toFloat(), it.y.toFloat()) }
    }

    /**
     * 對角點進行排序：左上、右上、右下、左下
     * 使用更可靠的幾何方法
     */
    private fun sortCorners(points: Array<org.opencv.core.Point>): List<org.opencv.core.Point> {
        // 計算重心
        val centerX = points.map { it.x }.average()
        val centerY = points.map { it.y }.average()
        val center = org.opencv.core.Point(centerX, centerY)

        Log.d(TAG, "角點排序 - 重心: ($centerX, $centerY)")
        Log.d(TAG, "原始角點: ${points.map { "(${it.x}, ${it.y})" }}")

        // 使用極角排序，確保順序正確
        val sortedPoints = points.sortedBy { point ->
            val angle = atan2(point.y - centerY, point.x - centerX)
            // 調整角度範圍，使左上角為起點
            val adjustedAngle = if (angle < -PI/2) angle + 2*PI else angle
            adjustedAngle
        }

        // 驗證排序結果並重新排列為標準順序：左上、右上、右下、左下
        val topPoints = sortedPoints.sortedBy { it.y }.take(2)
        val bottomPoints = sortedPoints.sortedBy { it.y }.takeLast(2)

        val topLeft = topPoints.minByOrNull { it.x }!!
        val topRight = topPoints.maxByOrNull { it.x }!!
        val bottomLeft = bottomPoints.minByOrNull { it.x }!!
        val bottomRight = bottomPoints.maxByOrNull { it.x }!!

        val result = listOf(topLeft, topRight, bottomRight, bottomLeft)
        Log.d(TAG, "排序後角點: ${result.map { "(${it.x}, ${it.y})" }}")

        return result
    }

    /**
     * 計算四邊形的矩形度
     * 返回值越接近1表示越接近矩形
     */
    private fun calculateRectangularity(corners: List<PointF>): Double {
        if (corners.size != 4) return 0.0

        // 計算四個角的角度
        val angles = mutableListOf<Double>()
        for (i in corners.indices) {
            val prev = corners[(i - 1 + 4) % 4]
            val curr = corners[i]
            val next = corners[(i + 1) % 4]

            val angle = calculateAngle(prev, curr, next)
            angles.add(angle)
        }

        // 計算角度與90度的偏差
        val deviations = angles.map { abs(it - 90.0) }
        val avgDeviation = deviations.average()

        // 矩形度 = 1 - (平均偏差 / 90度)
        return max(0.0, 1.0 - avgDeviation / 90.0)
    }

    /**
     * 計算長寬比
     */
    private fun calculateAspectRatio(corners: List<PointF>): Double {
        if (corners.size != 4) return 0.0

        // 計算四條邊的長度
        val sides = mutableListOf<Double>()
        for (i in corners.indices) {
            val curr = corners[i]
            val next = corners[(i + 1) % 4]
            val distance = sqrt((curr.x - next.x).pow(2) + (curr.y - next.y).pow(2)).toDouble()
            sides.add(distance)
        }

        // 找到最長和最短的邊
        val maxSide = sides.maxOrNull() ?: 1.0
        val minSide = sides.minOrNull() ?: 1.0

        return maxSide / minSide
    }

    /**
     * 計算三點之間的角度
     */
    private fun calculateAngle(p1: PointF, p2: PointF, p3: PointF): Double {
        val v1x = p1.x - p2.x
        val v1y = p1.y - p2.y
        val v2x = p3.x - p2.x
        val v2y = p3.y - p2.y

        val dot = v1x * v2x + v1y * v2y
        val mag1 = sqrt(v1x * v1x + v1y * v1y)
        val mag2 = sqrt(v2x * v2x + v2y * v2y)

        if (mag1 == 0f || mag2 == 0f) return 0.0

        val cosAngle = dot / (mag1 * mag2)
        val clampedCos = cosAngle.coerceIn(-1f, 1f)
        return Math.toDegrees(acos(clampedCos.toDouble()))
    }

    /**
     * 驗證檢測到的角點是否符合考卷特徵
     */
    private fun validateExamPaperCorners(corners: List<PointF>, imageSize: Size): Boolean {
        if (corners.size != 4) return false

        // 檢查矩形度
        val rectangularity = calculateRectangularity(corners)
        if (rectangularity < MIN_RECTANGULARITY) {
            Log.d(TAG, "矩形度不足: $rectangularity < $MIN_RECTANGULARITY")
            return false
        }

        // 檢查長寬比
        val aspectRatio = calculateAspectRatio(corners)
        if (aspectRatio < MIN_ASPECT_RATIO || aspectRatio > MAX_ASPECT_RATIO) {
            Log.d(TAG, "長寬比不符合: $aspectRatio 不在 [$MIN_ASPECT_RATIO, $MAX_ASPECT_RATIO] 範圍內")
            return false
        }

        // 檢查是否包含中央區域
        val centerX = imageSize.width / 2
        val centerY = imageSize.height / 2

        // 使用射線法檢查中心點是否在四邊形內
        val isContainsCenter = isPointInPolygon(PointF(centerX.toFloat(), centerY.toFloat()), corners)
        if (!isContainsCenter) {
            Log.d(TAG, "檢測到的四邊形不包含圖像中心")
            return false
        }

        Log.d(TAG, "✅ 角點驗證通過 - 矩形度: $rectangularity, 長寬比: $aspectRatio, 包含中心: $isContainsCenter")
        return true
    }

    /**
     * 檢查點是否在多邊形內（射線法）
     */
    private fun isPointInPolygon(point: PointF, polygon: List<PointF>): Boolean {
        var intersections = 0
        val n = polygon.size

        for (i in 0 until n) {
            val p1 = polygon[i]
            val p2 = polygon[(i + 1) % n]

            if (((p1.y > point.y) != (p2.y > point.y)) &&
                (point.x < (p2.x - p1.x) * (point.y - p1.y) / (p2.y - p1.y) + p1.x)) {
                intersections++
            }
        }

        return intersections % 2 == 1
    }

    /**
     * 文字檢測專用的圖像預處理
     */
    private fun preprocessForTextDetection(originalMat: Mat): Mat {
        val grayMat = Mat()
        val blurredMat = Mat()
        val threshMat = Mat()
        val morphMat = Mat()

        try {
            // 轉換為灰度圖
            Imgproc.cvtColor(originalMat, grayMat, Imgproc.COLOR_BGR2GRAY)

            // 輕微模糊以減少噪點
            Imgproc.GaussianBlur(grayMat, blurredMat, Size(3.0, 3.0), 0.0)

            // 自適應閾值化，更適合文字檢測
            Imgproc.adaptiveThreshold(
                blurredMat, threshMat, 255.0,
                Imgproc.ADAPTIVE_THRESH_GAUSSIAN_C,
                Imgproc.THRESH_BINARY,
                11, 2.0
            )

            // 形態學操作連接文字
            val kernel = Imgproc.getStructuringElement(
                Imgproc.MORPH_RECT,
                Size(3.0, 1.0)  // 水平核心，連接同一行的文字
            )

            Imgproc.morphologyEx(threshMat, morphMat, Imgproc.MORPH_CLOSE, kernel)
            kernel.release()

        } finally {
            grayMat.release()
            blurredMat.release()
            threshMat.release()
        }

        return morphMat
    }

    /**
     * 檢測水平文字行
     */
    private fun detectHorizontalTextLines(textMat: Mat): List<Rect> {
        val contours = mutableListOf<MatOfPoint>()
        val hierarchy = Mat()

        try {
            // 尋找輪廓
            Imgproc.findContours(
                textMat, contours, hierarchy,
                Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE
            )

            // 過濾出可能的文字行
            val textLines = contours.mapNotNull { contour ->
                val boundingRect = Imgproc.boundingRect(contour)

                // 檢查是否符合文字行特徵
                if (isLikelyTextLine(boundingRect)) {
                    boundingRect
                } else {
                    null
                }
            }.sortedBy { it.y }  // 按 Y 座標排序

            Log.d(TAG, "檢測到 ${textLines.size} 個可能的文字行")
            return textLines

        } finally {
            contours.forEach { it.release() }
            hierarchy.release()
        }
    }

    /**
     * 判斷矩形是否像文字行
     */
    private fun isLikelyTextLine(rect: Rect): Boolean {
        val width = rect.width
        val height = rect.height
        val aspectRatio = width.toDouble() / height

        return width >= TEXT_LINE_MIN_LENGTH &&  // 最小長度
               aspectRatio >= 3.0 &&             // 寬高比（文字行通常很寬）
               height >= 8 &&                    // 最小高度
               height <= 100                     // 最大高度
    }

    /**
     * 基於文字行分布推斷文檔邊界
     */
    private fun inferDocumentBoundsFromText(textLines: List<Rect>, imageSize: Size): List<PointF>? {
        if (textLines.size < 3) {  // 至少需要3行文字
            Log.d(TAG, "文字行太少，無法推斷文檔邊界")
            return null
        }

        // 計算文字區域的邊界
        val leftMost = textLines.minOfOrNull { it.x } ?: return null
        val rightMost = textLines.maxOfOrNull { it.x + it.width } ?: return null
        val topMost = textLines.minOfOrNull { it.y } ?: return null
        val bottomMost = textLines.maxOfOrNull { it.y + it.height } ?: return null

        // 添加邊距以包含整個文檔
        val margin = 20f
        val left = maxOf(0f, leftMost - margin)
        val right = minOf(imageSize.width.toFloat(), rightMost + margin)
        val top = maxOf(0f, topMost - margin)
        val bottom = minOf(imageSize.height.toFloat(), bottomMost + margin)

        // 檢查推斷的邊界是否合理
        val inferredWidth = right - left
        val inferredHeight = bottom - top
        val inferredArea = inferredWidth * inferredHeight
        val imageArea = imageSize.width * imageSize.height
        val areaRatio = inferredArea / imageArea

        if (areaRatio < 0.1 || areaRatio > 0.9) {
            Log.d(TAG, "推斷的文檔邊界面積比例不合理: $areaRatio")
            return null
        }

        Log.d(TAG, "基於文字推斷的文檔邊界: ($left, $top) to ($right, $bottom)")

        return listOf(
            PointF(left, top),           // 左上
            PointF(right, top),          // 右上
            PointF(right, bottom),       // 右下
            PointF(left, bottom)         // 左下
        )
    }

    /**
     * 檢查邊界是否在中央區域
     */
    private fun isBoundaryInCenterRegion(corners: List<PointF>, imageSize: Size): Boolean {
        if (corners.size != 4) return false

        val centerX = imageSize.width / 2
        val centerY = imageSize.height / 2

        // 計算邊界的中心點
        val boundaryCenter = PointF(
            corners.map { it.x }.average().toFloat(),
            corners.map { it.y }.average().toFloat()
        )

        // 檢查邊界中心是否在中央區域內
        val centerRegionWidth = imageSize.width * CENTER_REGION_RATIO
        val centerRegionHeight = imageSize.height * CENTER_REGION_RATIO

        val isInCenter = abs(boundaryCenter.x - centerX) < centerRegionWidth / 2 &&
                        abs(boundaryCenter.y - centerY) < centerRegionHeight / 2

        Log.d(TAG, "邊界中心: (${boundaryCenter.x}, ${boundaryCenter.y}), 在中央區域: $isInCenter")
        return isInCenter
    }

    /**
     * 生成中央區域的默認邊界
     */
    private fun generateCenterDefaultBoundary(imageSize: Size): List<PointF> {
        val centerX = (imageSize.width / 2).toFloat()
        val centerY = (imageSize.height / 2).toFloat()

        // 生成一個位於中央的矩形，佔圖像的60%
        val width = (imageSize.width * 0.6f).toFloat()
        val height = (imageSize.height * 0.6f).toFloat()

        val left = centerX - width / 2
        val right = centerX + width / 2
        val top = centerY - height / 2
        val bottom = centerY + height / 2

        Log.d(TAG, "生成中央默認邊界: ($left, $top) to ($right, $bottom)")

        return listOf(
            PointF(left, top),           // 左上
            PointF(right, top),          // 右上
            PointF(right, bottom),       // 右下
            PointF(left, bottom)         // 左下
        )
    }

    /**
     * 獲取默認邊界（當 OpenCV 檢測失敗時使用）
     */
    private fun getDefaultBoundary(bitmap: Bitmap): List<PointF> {
        val width = bitmap.width.toFloat()
        val height = bitmap.height.toFloat()
        val margin = min(width, height) * 0.1f

        return listOf(
            PointF(margin, margin),                    // 左上
            PointF(width - margin, margin),            // 右上
            PointF(width - margin, height - margin),   // 右下
            PointF(margin, height - margin)            // 左下
        )
    }

    /**
     * 智能中央矩形檢測：基於文字分析的中央矩形
     */
    private fun trySmartCenterRectangleDetection(mat: Mat): MatOfPoint? {
        Log.d(TAG, "=== 開始智能中央矩形檢測 ===")

        val imageSize = mat.size()
        Log.d(TAG, "圖像尺寸: ${imageSize.width} x ${imageSize.height}")

        // 檢測文字區域來確定文檔邊界
        val textBounds = detectTextRegions(mat)

        return if (textBounds != null) {
            Log.d(TAG, "✓ 檢測到文字區域，基於文字邊界生成矩形")
            generateRectangleFromTextBounds(textBounds, imageSize)
        } else {
            Log.d(TAG, "✗ 文字檢測失敗，使用標準中央矩形策略")
            generateOptimalCenterRectangle(imageSize)
        }.also {
            Log.d(TAG, "=== 智能檢測完成 ===")
        }
    }

    /**
     * 檢測文字區域 - 針對考卷文檔優化
     */
    private fun detectTextRegions(mat: Mat): Rect? {
        try {
            Log.d(TAG, "開始檢測文字區域")

            // 轉換為灰度圖
            val gray = Mat()
            if (mat.channels() == 3) {
                Imgproc.cvtColor(mat, gray, Imgproc.COLOR_BGR2GRAY)
            } else {
                mat.copyTo(gray)
            }

            // 高斯模糊減少噪聲
            val blurred = Mat()
            Imgproc.GaussianBlur(gray, blurred, Size(3.0, 3.0), 0.0)

            // 自適應二值化，更好地處理不均勻光照
            val binary = Mat()
            Imgproc.adaptiveThreshold(
                blurred, binary, 255.0,
                Imgproc.ADAPTIVE_THRESH_GAUSSIAN_C,
                Imgproc.THRESH_BINARY_INV,
                15, 10.0
            )

            // 使用水平核檢測文字行
            val horizontalKernel = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, Size(25.0, 1.0))
            val horizontalLines = Mat()
            Imgproc.morphologyEx(binary, horizontalLines, Imgproc.MORPH_OPEN, horizontalKernel)

            // 使用垂直核檢測文字列
            val verticalKernel = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, Size(1.0, 25.0))
            val verticalLines = Mat()
            Imgproc.morphologyEx(binary, verticalLines, Imgproc.MORPH_OPEN, verticalKernel)

            // 合併水平和垂直特徵
            val combined = Mat()
            Core.add(horizontalLines, verticalLines, combined)

            // 膨脹操作連接文字區域
            val dilateKernel = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, Size(3.0, 3.0))
            val dilated = Mat()
            Imgproc.dilate(combined, dilated, dilateKernel, Point(-1.0, -1.0), 2)

            // 尋找輪廓
            val contours = mutableListOf<MatOfPoint>()
            val hierarchy = Mat()
            Imgproc.findContours(dilated, contours, hierarchy, Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE)

            Log.d(TAG, "檢測到 ${contours.size} 個文字區域候選")

            // 找到最大且合理的文字區域
            var bestTextBounds: Rect? = null
            var maxValidArea = 0.0
            val imageArea = mat.size().area()

            for (contour in contours) {
                val area = Imgproc.contourArea(contour)
                val bounds = Imgproc.boundingRect(contour)

                // 過濾條件：面積要合理（5%-90%的圖像面積）
                val areaRatio = area / imageArea
                if (areaRatio >= 0.05 && areaRatio <= 0.9) {
                    // 長寬比要合理（不能太細長）
                    val aspectRatio = bounds.width.toDouble() / bounds.height.toDouble()
                    if (aspectRatio >= 0.3 && aspectRatio <= 3.0) {
                        if (area > maxValidArea) {
                            maxValidArea = area
                            bestTextBounds = bounds
                        }
                    }
                }
            }

            // 清理資源
            gray.release()
            blurred.release()
            binary.release()
            horizontalLines.release()
            verticalLines.release()
            combined.release()
            dilated.release()
            horizontalKernel.release()
            verticalKernel.release()
            dilateKernel.release()
            hierarchy.release()
            contours.forEach { it.release() }

            if (bestTextBounds != null) {
                Log.d(TAG, "檢測到文字區域: ${bestTextBounds.x}, ${bestTextBounds.y}, ${bestTextBounds.width}, ${bestTextBounds.height}")
                Log.d(TAG, "文字區域面積比例: ${(maxValidArea / imageArea * 100).toInt()}%")
            } else {
                Log.d(TAG, "未檢測到有效文字區域")
            }

            return bestTextBounds

        } catch (e: Exception) {
            Log.e(TAG, "文字區域檢測失敗", e)
            return null
        }
    }

    /**
     * 基於文字邊界生成矩形 - 智能邊距計算
     */
    private fun generateRectangleFromTextBounds(textBounds: Rect, imageSize: Size): MatOfPoint {
        Log.d(TAG, "基於文字邊界生成矩形")

        // 智能邊距計算：根據圖像大小和文字區域大小動態調整
        val imageWidth = imageSize.width
        val imageHeight = imageSize.height

        // 邊距為圖像尺寸的3-5%，確保有足夠空間但不會太大
        val marginRatio = 0.04
        val horizontalMargin = imageWidth * marginRatio
        val verticalMargin = imageHeight * marginRatio

        // 計算擴展後的邊界
        var left = textBounds.x - horizontalMargin
        var top = textBounds.y - verticalMargin
        var right = textBounds.x + textBounds.width + horizontalMargin
        var bottom = textBounds.y + textBounds.height + verticalMargin

        // 確保邊界在圖像範圍內
        left = maxOf(0.0, left)
        top = maxOf(0.0, top)
        right = minOf(imageWidth, right)
        bottom = minOf(imageHeight, bottom)

        // 進一步優化：確保矩形居中且比例合理
        val rectWidth = right - left
        val rectHeight = bottom - top
        val centerX = imageWidth / 2.0
        val centerY = imageHeight / 2.0

        // 如果檢測到的區域太小，使用最小尺寸
        val minWidth = imageWidth * 0.7
        val minHeight = imageHeight * 0.7

        val finalWidth = maxOf(rectWidth, minWidth)
        val finalHeight = maxOf(rectHeight, minHeight)

        // 重新計算居中的邊界
        val finalLeft = maxOf(0.0, centerX - finalWidth / 2.0)
        val finalTop = maxOf(0.0, centerY - finalHeight / 2.0)
        val finalRight = minOf(imageWidth, centerX + finalWidth / 2.0)
        val finalBottom = minOf(imageHeight, centerY + finalHeight / 2.0)

        val corners = arrayOf(
            Point(finalLeft, finalTop),        // 左上
            Point(finalRight, finalTop),       // 右上
            Point(finalRight, finalBottom),    // 右下
            Point(finalLeft, finalBottom)      // 左下
        )

        Log.d(TAG, "原始文字區域: (${textBounds.x}, ${textBounds.y}) -> (${textBounds.x + textBounds.width}, ${textBounds.y + textBounds.height})")
        Log.d(TAG, "最終矩形邊界: ($finalLeft, $finalTop) -> ($finalRight, $finalBottom)")
        Log.d(TAG, "矩形尺寸: ${finalRight - finalLeft} x ${finalBottom - finalTop}")

        return MatOfPoint(*corners)
    }

    /**
     * 生成最佳中央矩形 - 針對考卷優化
     */
    private fun generateOptimalCenterRectangle(imageSize: Size): MatOfPoint {
        Log.d(TAG, "生成最佳中央矩形")

        // 根據您的圖片分析，使用更大的覆蓋範圍（85%），確保包含所有文字內容
        val documentRatio = 0.85
        val documentWidth = imageSize.width * documentRatio
        val documentHeight = imageSize.height * documentRatio

        // 計算精確的中央位置
        val centerX = imageSize.width / 2.0
        val centerY = imageSize.height / 2.0

        // 確保邊界對齊到像素
        val left = Math.round(centerX - documentWidth / 2.0).toDouble()
        val top = Math.round(centerY - documentHeight / 2.0).toDouble()
        val right = Math.round(centerX + documentWidth / 2.0).toDouble()
        val bottom = Math.round(centerY + documentHeight / 2.0).toDouble()

        // 確保邊界在圖像範圍內
        val finalLeft = maxOf(0.0, left)
        val finalTop = maxOf(0.0, top)
        val finalRight = minOf(imageSize.width, right)
        val finalBottom = minOf(imageSize.height, bottom)

        val corners = arrayOf(
            Point(finalLeft, finalTop),        // 左上
            Point(finalRight, finalTop),       // 右上
            Point(finalRight, finalBottom),    // 右下
            Point(finalLeft, finalBottom)      // 左下
        )

        Log.d(TAG, "生成中央矩形: ($finalLeft, $finalTop) -> ($finalRight, $finalBottom)")
        Log.d(TAG, "矩形尺寸: ${finalRight - finalLeft} x ${finalBottom - finalTop}")
        Log.d(TAG, "覆蓋比例: ${documentRatio * 100}%")

        return MatOfPoint(*corners)
    }

    /**
     * 計算檢測區域面積比例
     */
    private fun calculateAreaRatio(corners: List<PointF>, imageWidth: Int, imageHeight: Int): String {
        if (corners.size != 4) return "無效"

        // 使用鞋帶公式計算多邊形面積
        var area = 0.0
        for (i in corners.indices) {
            val j = (i + 1) % corners.size
            area += corners[i].x * corners[j].y
            area -= corners[j].x * corners[i].y
        }
        area = abs(area) / 2.0

        val imageArea = imageWidth * imageHeight
        val ratio = (area / imageArea * 100)

        return String.format("%.1f%%", ratio)
    }
}
