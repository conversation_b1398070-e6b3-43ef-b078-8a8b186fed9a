<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 外層白色邊框 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="@color/text_white" />
        </shape>
    </item>
    
    <!-- 內層深色背景，創造朝內圓角效果 -->
    <item android:inset="1dp">
        <shape android:shape="oval">
            <solid android:color="@color/overlay_dark" />
        </shape>
    </item>
    
    <!-- 保存圖標 -->
    <item 
        android:width="24dp"
        android:height="24dp"
        android:gravity="center">
        <vector
            android:width="24dp"
            android:height="24dp"
            android:viewportWidth="24"
            android:viewportHeight="24">
            <path
                android:fillColor="@color/text_white"
                android:pathData="M17,3H5C3.89,3 3,3.9 3,5v14c0,1.1 0.89,2 2,2h14c1.1,0 2,-0.9 2,-2V7l-4,-4zM12,19c-1.66,0 -3,-1.34 -3,-3s1.34,-3 3,-3 3,1.34 3,3 -1.34,3 -3,3zM15,9H5V5h10v4z"/>
        </vector>
    </item>
    
</layer-list>
