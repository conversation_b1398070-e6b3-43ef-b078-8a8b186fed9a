package com.erroranalysis.app.ui.settings

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.FileProvider
import androidx.lifecycle.lifecycleScope
import com.erroranalysis.app.databinding.ActivityBackupRestoreBinding
import com.erroranalysis.app.utils.SimpleBackupManager
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.Scope
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.api.client.http.javanet.NetHttpTransport
import com.google.api.client.googleapis.extensions.android.gms.auth.GoogleAccountCredential
import com.google.api.client.googleapis.extensions.android.gms.auth.UserRecoverableAuthIOException
import com.google.api.client.json.gson.GsonFactory
import com.google.api.services.drive.Drive
import com.google.api.services.drive.DriveScopes
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class BackupRestoreActivity : AppCompatActivity() {

    private lateinit var binding: ActivityBackupRestoreBinding
    private lateinit var backupManager: SimpleBackupManager
    private lateinit var googleSignInClient: GoogleSignInClient
    private var driveService: Drive? = null

    private enum class PendingAction { NONE, BACKUP, RESTORE }
    private var pendingAction: PendingAction = PendingAction.NONE

    companion object {
        private const val TAG = "BackupRestoreActivity"
        private const val REQUEST_CODE_CREATE_BACKUP = 1000
        private const val REQUEST_CODE_RESTORE_FILE = 1001
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityBackupRestoreBinding.inflate(layoutInflater)
        setContentView(binding.root)
        backupManager = SimpleBackupManager(this)

        setupToolbar()
        setupClickListeners()
        googleSignInClient = createGoogleSignInClient()
        requestSignIn(silent = true) // Try silent sign-in on start
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "備份與還原"
        }
    }

    private fun setupClickListeners() {
        // 本地備份和還原卡片點擊事件
        binding.layoutLocalBackup.setOnClickListener {
            performLocalBackup()
        }

        binding.layoutLocalRestore.setOnClickListener {
            performLocalRestore()
        }

        // Google Drive 備份和還原卡片點擊事件
        binding.layoutGoogleDriveBackup.setOnClickListener {
            pendingAction = PendingAction.BACKUP
            requestSignIn()
        }

        binding.layoutGoogleDriveRestore.setOnClickListener {
            pendingAction = PendingAction.RESTORE
            requestSignIn()
        }
    }

    private fun createGoogleSignInClient(): GoogleSignInClient {
        val signInOptions = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestEmail()
            // 限制權限：只允許寫入和刪除應用創建的文件
            .requestScopes(Scope(DriveScopes.DRIVE_FILE))
            .build()
        return GoogleSignIn.getClient(this, signInOptions)
    }

    private fun requestSignIn(silent: Boolean = false) {
        Log.d(TAG, "Requesting Google Sign-In (silent: $silent)")
        val account = GoogleSignIn.getLastSignedInAccount(this)

        // 當權限範圍改變時，強制重新登入以獲得新的權限
        if (account != null && GoogleSignIn.hasPermissions(account, Scope(DriveScopes.DRIVE_FILE))) {
            Log.d(TAG, "Already signed in and has limited Drive permissions.")
            handleSignInSuccess(account)
        } else {
            // 清除舊的登入狀態，強制重新授權
            Log.d(TAG, "Clearing old sign-in state and requesting new authorization")
            googleSignInClient.signOut().addOnCompleteListener {
                signInLauncher.launch(googleSignInClient.signInIntent)
            }
        }
    }

    private val signInLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            GoogleSignIn.getSignedInAccountFromIntent(result.data)
                .addOnSuccessListener(::handleSignInSuccess)
                .addOnFailureListener { e -> handleSignInFailure(e) }
        } else {
            Log.w(TAG, "Sign-in failed with result code: ${result.resultCode}")
            Toast.makeText(this, "Google 登入已取消", Toast.LENGTH_SHORT).show()
        }
    }

    // 處理權限重新授權的launcher
    private val authorizationLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            Log.d(TAG, "Authorization successful, retrying operation")
            // 重新執行之前失敗的操作
            when (pendingAction) {
                PendingAction.BACKUP -> performGoogleDriveBackup()
                PendingAction.RESTORE -> performGoogleDriveRestore()
                else -> {}
            }
        } else {
            Log.w(TAG, "Authorization was cancelled or failed.")
            Toast.makeText(this, "權限授權已取消", Toast.LENGTH_SHORT).show()
            pendingAction = PendingAction.NONE
        }
    }

    private fun handleSignInSuccess(account: com.google.android.gms.auth.api.signin.GoogleSignInAccount) {
        Log.d(TAG, "Sign-in successful for ${account.email} with limited Drive permissions")
        // 使用限制的Drive權限：只能存取應用創建的文件
        val credential = GoogleAccountCredential.usingOAuth2(this, listOf(DriveScopes.DRIVE_FILE))
        credential.selectedAccount = account.account
        driveService = Drive.Builder(NetHttpTransport(), GsonFactory.getDefaultInstance(), credential)
            .setApplicationName("ErrorAnalysisApp")
            .build()
        Toast.makeText(this, "Google 登入成功 (限制權限)", Toast.LENGTH_SHORT).show()

        when (pendingAction) {
            PendingAction.BACKUP -> performGoogleDriveBackup()
            PendingAction.RESTORE -> performGoogleDriveRestore()
            else -> {}
        }
        pendingAction = PendingAction.NONE
    }

    private fun handleSignInFailure(exception: Exception) {
        Log.e(TAG, "Google Sign-In failed", exception)
        Toast.makeText(this, "Google 登入失敗: ${exception.message}", Toast.LENGTH_LONG).show()
        pendingAction = PendingAction.NONE
    }

    private fun performGoogleDriveBackup() {
        if (driveService == null) {
            Toast.makeText(this, "Google Drive 服務未準備好", Toast.LENGTH_SHORT).show()
            requestSignIn()
            return
        }

        setLoading(true)

        lifecycleScope.launch {
            try {
                val backupResult = withContext(Dispatchers.IO) { backupManager.createBackup() }

                if (backupResult is SimpleBackupManager.BackupResult.Success) {
                    val tempFile = java.io.File(cacheDir, backupManager.generateBackupFileName())
                    withContext(Dispatchers.IO) {
                        tempFile.outputStream().use { outputStream ->
                            backupManager.saveBackupToOutputStream(backupResult, outputStream)
                        }

                        val fileMetadata = com.google.api.services.drive.model.File().apply {
                            name = tempFile.name
                            // 限制權限：只能存取應用創建的文件，不能存取其他文件
                            // 使用DRIVE_FILE權限確保只能操作應用創建的文件
                        }
                        val mediaContent = com.google.api.client.http.FileContent("application/zip", tempFile)
                        driveService?.files()?.create(fileMetadata, mediaContent)?.execute()
                    }
                    showSuccessDialog("Google Drive 備份成功", "備份文件已上傳到您的應用程式資料夾")
                    Log.d(TAG, "Backup successful: ${tempFile.name}")
                    tempFile.delete()
                } else if (backupResult is SimpleBackupManager.BackupResult.Error) {
                    showErrorDialog("備份失敗", backupResult.message)
                }
            }
            catch (e: UserRecoverableAuthIOException) {
                Log.w(TAG, "Need user authorization for Drive access", e)
                Toast.makeText(this@BackupRestoreActivity, "需要重新授權 Google Drive 權限", Toast.LENGTH_LONG).show()
                // 啟動權限授權流程
                authorizationLauncher.launch(e.intent)
            }
            catch (e: Exception) {
                Log.e(TAG, "Google Drive backup failed", e)
                showErrorDialog("Google Drive 備份失敗", e.localizedMessage ?: "發生未知錯誤")
            }
            finally {
                setLoading(false)
            }
        }
    }



    private fun performGoogleDriveRestore() {
        if (driveService == null) {
            Toast.makeText(this, "Google Drive 服務未準備好", Toast.LENGTH_SHORT).show()
            requestSignIn()
            return
        }

        setLoading(true)

        lifecycleScope.launch {
            try {
                val backupFiles = withContext(Dispatchers.IO) {
                    val query = "name contains '.eabackup' and trashed=false"
                    val result = driveService?.files()?.list()
                        ?.setQ(query)
                        ?.setOrderBy("modifiedTime desc")
                        ?.setPageSize(10)
                        ?.execute()
                    result?.files ?: emptyList()
                }

                if (backupFiles.isEmpty()) {
                    showErrorDialog("沒有找到備份文件", "Google Drive中沒有找到任何備份文件")
                    return@launch
                }

                // 顯示備份文件選擇對話框
                showBackupFileSelectionDialog(backupFiles)

            } catch (e: UserRecoverableAuthIOException) {
                Log.w(TAG, "Need user authorization for Drive access", e)
                Toast.makeText(this@BackupRestoreActivity, "需要重新授權 Google Drive 權限", Toast.LENGTH_LONG).show()
                // 啟動權限授權流程
                authorizationLauncher.launch(e.intent)
                setLoading(false)
            } catch (e: Exception) {
                Log.e(TAG, "Google Drive restore failed", e)
                showErrorDialog("Google Drive 還原失敗", e.localizedMessage ?: "發生未知錯誤")
                setLoading(false)
            }
        }
    }

    private fun showBackupFileSelectionDialog(backupFiles: List<com.google.api.services.drive.model.File>) {
        val fileNames = backupFiles.map { file ->
            val timeStr = file.modifiedTime?.toString()?.substring(0, 19)?.replace("T", " ") ?: "未知時間"
            "${file.name} ($timeStr)"
        }.toTypedArray()

        MaterialAlertDialogBuilder(this)
            .setTitle("選擇要還原的備份文件")
            .setItems(fileNames) { _, which ->
                val selectedFile = backupFiles[which]
                downloadAndRestoreFile(selectedFile)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun downloadAndRestoreFile(file: com.google.api.services.drive.model.File) {
        setLoading(true)
        lifecycleScope.launch {
            try {
                val restoreResult = withContext(Dispatchers.IO) {
                    // 從Google Drive下載文件
                    val inputStream = driveService?.files()?.get(file.id)?.executeMediaAsInputStream()

                    // 創建臨時文件
                    val tempFile = java.io.File.createTempFile("restore_", ".eabackup", cacheDir)
                    tempFile.outputStream().use { output ->
                        inputStream?.copyTo(output)
                    }

                    // 使用臨時文件進行還原
                    val uri = FileProvider.getUriForFile(
                        this@BackupRestoreActivity,
                        "${packageName}.fileprovider",
                        tempFile
                    )

                    val result = backupManager.restoreFromUri(uri)

                    // 清理臨時文件
                    tempFile.delete()

                    result
                }

                when (restoreResult) {
                    is SimpleBackupManager.RestoreResult.Success -> {
                        showSuccessDialog("還原完成", restoreResult.messages.joinToString("\n")) { restartApp() }
                    }
                    is SimpleBackupManager.RestoreResult.Error -> {
                        showErrorDialog("還原失敗", restoreResult.message)
                    }
                }
            } catch (e: UserRecoverableAuthIOException) {
                Log.w(TAG, "Need user authorization for Drive access", e)
                Toast.makeText(this@BackupRestoreActivity, "需要重新授權 Google Drive 權限", Toast.LENGTH_LONG).show()
                // 啟動權限授權流程
                authorizationLauncher.launch(e.intent)
                setLoading(false)
            } catch (e: Exception) {
                Log.e(TAG, "Google Drive restore failed", e)
                showErrorDialog("還原失敗", e.localizedMessage ?: "發生未知錯誤")
                setLoading(false)
            }
        }
    }



    private fun setLoading(isLoading: Boolean) {
        binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        binding.layoutGoogleDriveBackup.isEnabled = !isLoading
        binding.layoutGoogleDriveRestore.isEnabled = !isLoading
    }

    private fun showSuccessDialog(title: String, message: String, onOk: (() -> Unit)? = null) {
        MaterialAlertDialogBuilder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton("確定") { _, _ -> onOk?.invoke() }
            .setCancelable(false)
            .show()
    }

    private fun showErrorDialog(title: String, message: String) {
        MaterialAlertDialogBuilder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton("確定", null)
            .show()
    }

    private fun restartApp() {
        val intent = packageManager.getLaunchIntentForPackage(packageName)!!
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
        finish()
    }

    private fun performLocalBackup() {
        // 使用文件選擇器讓用戶選擇保存位置
        val intent = Intent(Intent.ACTION_CREATE_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = "application/zip"
            val timestamp = java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault())
                .format(java.util.Date())
            putExtra(Intent.EXTRA_TITLE, "ErrorAnalysis_Backup_$timestamp.eabackup")
        }

        try {
            startActivityForResult(intent, REQUEST_CODE_CREATE_BACKUP)
        } catch (e: Exception) {
            Toast.makeText(this, "無法打開文件選擇器", Toast.LENGTH_SHORT).show()
        }
    }

    private fun performActualBackup(targetUri: Uri) {
        setLoading(true)
        lifecycleScope.launch {
            try {
                val backupResult = withContext(Dispatchers.IO) {
                    backupManager.createBackup()
                }

                when (backupResult) {
                    is SimpleBackupManager.BackupResult.Success -> {
                        val saveSuccess = withContext(Dispatchers.IO) {
                            backupManager.saveBackupToUri(backupResult, targetUri)
                        }

                        if (saveSuccess) {
                            showSuccessDialog("備份完成", "備份文件已成功保存")
                        } else {
                            showErrorDialog("備份失敗", "無法保存備份文件")
                        }
                    }
                    is SimpleBackupManager.BackupResult.Error -> {
                        showErrorDialog("備份失敗", backupResult.message)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Local backup failed", e)
                showErrorDialog("備份失敗", e.localizedMessage ?: "發生未知錯誤")
            } finally {
                setLoading(false)
            }
        }
    }

    private fun performLocalRestore() {
        // 使用文件選擇器讓用戶選擇備份文件
        val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
            type = "application/zip"
            addCategory(Intent.CATEGORY_OPENABLE)
        }

        try {
            startActivityForResult(intent, REQUEST_CODE_RESTORE_FILE)
        } catch (e: Exception) {
            Toast.makeText(this, "無法打開文件選擇器", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            REQUEST_CODE_CREATE_BACKUP -> {
                if (resultCode == RESULT_OK) {
                    data?.data?.let { uri ->
                        performActualBackup(uri)
                    }
                }
            }
            REQUEST_CODE_RESTORE_FILE -> {
                if (resultCode == RESULT_OK) {
                    data?.data?.let { uri ->
                        performLocalRestoreFromUri(uri)
                    }
                }
            }
        }
    }

    private fun performLocalRestoreFromUri(uri: Uri) {
        setLoading(true)
        lifecycleScope.launch {
            try {
                val restoreResult = withContext(Dispatchers.IO) {
                    backupManager.restoreFromUri(uri)
                }
                when (restoreResult) {
                    is SimpleBackupManager.RestoreResult.Success -> {
                        showSuccessDialog("還原完成", restoreResult.messages.joinToString("\n")) { restartApp() }
                    }
                    is SimpleBackupManager.RestoreResult.Error -> {
                        showErrorDialog("還原失敗", restoreResult.message)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Local restore failed", e)
                showErrorDialog("還原失敗", e.localizedMessage ?: "發生未知錯誤")
            } finally {
                setLoading(false)
            }
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressedDispatcher.onBackPressed()
        return true
    }

}