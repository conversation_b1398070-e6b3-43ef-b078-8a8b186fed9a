package com.erroranalysis.app.ui.study

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class StudyDeck(
    val id: String,
    val name: String,
    val description: String,
    val icon: String,
    val color: String,
    var cardCount: Int,
    val createdAt: Long,
    var isStarred: Boolean
) : Parcelable {
    fun toSimpleDeck(): SimpleDeck {
        return SimpleDeck(
            id = id,
            name = name,
            description = description,
            icon = icon,
            color = color,
            cardCount = cardCount,
            createdAt = createdAt,
            isStarred = isStarred
        )
    }
}