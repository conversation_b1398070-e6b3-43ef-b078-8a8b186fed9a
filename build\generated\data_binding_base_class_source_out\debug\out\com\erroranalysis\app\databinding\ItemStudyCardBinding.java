// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemStudyCardBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageView iconStar;

  @NonNull
  public final View indicatorStatus;

  @NonNull
  public final ProgressBar progressMastery;

  @NonNull
  public final FrameLayout starContainer;

  @NonNull
  public final TextView textAnswer;

  @NonNull
  public final TextView textCreatedTime;

  @NonNull
  public final TextView textDifficulty;

  @NonNull
  public final TextView textMasteryLevel;

  @NonNull
  public final TextView textQuestion;

  @NonNull
  public final TextView textStats;

  @NonNull
  public final TextView textTags;

  private ItemStudyCardBinding(@NonNull MaterialCardView rootView, @NonNull ImageView iconStar,
      @NonNull View indicatorStatus, @NonNull ProgressBar progressMastery,
      @NonNull FrameLayout starContainer, @NonNull TextView textAnswer,
      @NonNull TextView textCreatedTime, @NonNull TextView textDifficulty,
      @NonNull TextView textMasteryLevel, @NonNull TextView textQuestion,
      @NonNull TextView textStats, @NonNull TextView textTags) {
    this.rootView = rootView;
    this.iconStar = iconStar;
    this.indicatorStatus = indicatorStatus;
    this.progressMastery = progressMastery;
    this.starContainer = starContainer;
    this.textAnswer = textAnswer;
    this.textCreatedTime = textCreatedTime;
    this.textDifficulty = textDifficulty;
    this.textMasteryLevel = textMasteryLevel;
    this.textQuestion = textQuestion;
    this.textStats = textStats;
    this.textTags = textTags;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemStudyCardBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemStudyCardBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_study_card, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemStudyCardBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.icon_star;
      ImageView iconStar = ViewBindings.findChildViewById(rootView, id);
      if (iconStar == null) {
        break missingId;
      }

      id = R.id.indicator_status;
      View indicatorStatus = ViewBindings.findChildViewById(rootView, id);
      if (indicatorStatus == null) {
        break missingId;
      }

      id = R.id.progress_mastery;
      ProgressBar progressMastery = ViewBindings.findChildViewById(rootView, id);
      if (progressMastery == null) {
        break missingId;
      }

      id = R.id.star_container;
      FrameLayout starContainer = ViewBindings.findChildViewById(rootView, id);
      if (starContainer == null) {
        break missingId;
      }

      id = R.id.text_answer;
      TextView textAnswer = ViewBindings.findChildViewById(rootView, id);
      if (textAnswer == null) {
        break missingId;
      }

      id = R.id.text_created_time;
      TextView textCreatedTime = ViewBindings.findChildViewById(rootView, id);
      if (textCreatedTime == null) {
        break missingId;
      }

      id = R.id.text_difficulty;
      TextView textDifficulty = ViewBindings.findChildViewById(rootView, id);
      if (textDifficulty == null) {
        break missingId;
      }

      id = R.id.text_mastery_level;
      TextView textMasteryLevel = ViewBindings.findChildViewById(rootView, id);
      if (textMasteryLevel == null) {
        break missingId;
      }

      id = R.id.text_question;
      TextView textQuestion = ViewBindings.findChildViewById(rootView, id);
      if (textQuestion == null) {
        break missingId;
      }

      id = R.id.text_stats;
      TextView textStats = ViewBindings.findChildViewById(rootView, id);
      if (textStats == null) {
        break missingId;
      }

      id = R.id.text_tags;
      TextView textTags = ViewBindings.findChildViewById(rootView, id);
      if (textTags == null) {
        break missingId;
      }

      return new ItemStudyCardBinding((MaterialCardView) rootView, iconStar, indicatorStatus,
          progressMastery, starContainer, textAnswer, textCreatedTime, textDifficulty,
          textMasteryLevel, textQuestion, textStats, textTags);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
