// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityBatchImportBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton buttonComplete;

  @NonNull
  public final MaterialButton buttonSelectFile;

  @NonNull
  public final MaterialButton buttonShowExample;

  @NonNull
  public final MaterialButton buttonViewDeck;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView textErrors;

  @NonNull
  public final TextView textInstructions;

  @NonNull
  public final TextView textStatus;

  @NonNull
  public final MaterialToolbar toolbar;

  private ActivityBatchImportBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton buttonComplete, @NonNull MaterialButton buttonSelectFile,
      @NonNull MaterialButton buttonShowExample, @NonNull MaterialButton buttonViewDeck,
      @NonNull ProgressBar progressBar, @NonNull TextView textErrors,
      @NonNull TextView textInstructions, @NonNull TextView textStatus,
      @NonNull MaterialToolbar toolbar) {
    this.rootView = rootView;
    this.buttonComplete = buttonComplete;
    this.buttonSelectFile = buttonSelectFile;
    this.buttonShowExample = buttonShowExample;
    this.buttonViewDeck = buttonViewDeck;
    this.progressBar = progressBar;
    this.textErrors = textErrors;
    this.textInstructions = textInstructions;
    this.textStatus = textStatus;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityBatchImportBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityBatchImportBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_batch_import, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityBatchImportBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_complete;
      MaterialButton buttonComplete = ViewBindings.findChildViewById(rootView, id);
      if (buttonComplete == null) {
        break missingId;
      }

      id = R.id.button_select_file;
      MaterialButton buttonSelectFile = ViewBindings.findChildViewById(rootView, id);
      if (buttonSelectFile == null) {
        break missingId;
      }

      id = R.id.button_show_example;
      MaterialButton buttonShowExample = ViewBindings.findChildViewById(rootView, id);
      if (buttonShowExample == null) {
        break missingId;
      }

      id = R.id.button_view_deck;
      MaterialButton buttonViewDeck = ViewBindings.findChildViewById(rootView, id);
      if (buttonViewDeck == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.text_errors;
      TextView textErrors = ViewBindings.findChildViewById(rootView, id);
      if (textErrors == null) {
        break missingId;
      }

      id = R.id.text_instructions;
      TextView textInstructions = ViewBindings.findChildViewById(rootView, id);
      if (textInstructions == null) {
        break missingId;
      }

      id = R.id.text_status;
      TextView textStatus = ViewBindings.findChildViewById(rootView, id);
      if (textStatus == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityBatchImportBinding((LinearLayout) rootView, buttonComplete,
          buttonSelectFile, buttonShowExample, buttonViewDeck, progressBar, textErrors,
          textInstructions, textStatus, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
