// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.erroranalysis.app.ui.widgets.RichTextEditText;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCardEditBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final ImageButton buttonAddAnswerImage;

  @NonNull
  public final ImageButton buttonAddQuestionImage;

  @NonNull
  public final MaterialButton buttonAiAnswer;

  @NonNull
  public final ImageButton buttonCancel;

  @NonNull
  public final ImageButton buttonOcrAnswer;

  @NonNull
  public final ImageButton buttonOcrQuestion;

  @NonNull
  public final ImageButton buttonRotateQuestionImage;

  @NonNull
  public final ImageButton buttonSave;

  @NonNull
  public final RichTextEditText editAiAnswer;

  @NonNull
  public final RichTextEditText editAnswer;

  @NonNull
  public final RichTextEditText editQuestion;

  @NonNull
  public final TextInputEditText editTags;

  @NonNull
  public final TextInputLayout layoutAiAnswer;

  @NonNull
  public final Toolbar toolbar;

  private ActivityCardEditBinding(@NonNull CoordinatorLayout rootView,
      @NonNull ImageButton buttonAddAnswerImage, @NonNull ImageButton buttonAddQuestionImage,
      @NonNull MaterialButton buttonAiAnswer, @NonNull ImageButton buttonCancel,
      @NonNull ImageButton buttonOcrAnswer, @NonNull ImageButton buttonOcrQuestion,
      @NonNull ImageButton buttonRotateQuestionImage, @NonNull ImageButton buttonSave,
      @NonNull RichTextEditText editAiAnswer, @NonNull RichTextEditText editAnswer,
      @NonNull RichTextEditText editQuestion, @NonNull TextInputEditText editTags,
      @NonNull TextInputLayout layoutAiAnswer, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.buttonAddAnswerImage = buttonAddAnswerImage;
    this.buttonAddQuestionImage = buttonAddQuestionImage;
    this.buttonAiAnswer = buttonAiAnswer;
    this.buttonCancel = buttonCancel;
    this.buttonOcrAnswer = buttonOcrAnswer;
    this.buttonOcrQuestion = buttonOcrQuestion;
    this.buttonRotateQuestionImage = buttonRotateQuestionImage;
    this.buttonSave = buttonSave;
    this.editAiAnswer = editAiAnswer;
    this.editAnswer = editAnswer;
    this.editQuestion = editQuestion;
    this.editTags = editTags;
    this.layoutAiAnswer = layoutAiAnswer;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCardEditBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCardEditBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_card_edit, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCardEditBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_add_answer_image;
      ImageButton buttonAddAnswerImage = ViewBindings.findChildViewById(rootView, id);
      if (buttonAddAnswerImage == null) {
        break missingId;
      }

      id = R.id.button_add_question_image;
      ImageButton buttonAddQuestionImage = ViewBindings.findChildViewById(rootView, id);
      if (buttonAddQuestionImage == null) {
        break missingId;
      }

      id = R.id.button_ai_answer;
      MaterialButton buttonAiAnswer = ViewBindings.findChildViewById(rootView, id);
      if (buttonAiAnswer == null) {
        break missingId;
      }

      id = R.id.button_cancel;
      ImageButton buttonCancel = ViewBindings.findChildViewById(rootView, id);
      if (buttonCancel == null) {
        break missingId;
      }

      id = R.id.button_ocr_answer;
      ImageButton buttonOcrAnswer = ViewBindings.findChildViewById(rootView, id);
      if (buttonOcrAnswer == null) {
        break missingId;
      }

      id = R.id.button_ocr_question;
      ImageButton buttonOcrQuestion = ViewBindings.findChildViewById(rootView, id);
      if (buttonOcrQuestion == null) {
        break missingId;
      }

      id = R.id.button_rotate_question_image;
      ImageButton buttonRotateQuestionImage = ViewBindings.findChildViewById(rootView, id);
      if (buttonRotateQuestionImage == null) {
        break missingId;
      }

      id = R.id.button_save;
      ImageButton buttonSave = ViewBindings.findChildViewById(rootView, id);
      if (buttonSave == null) {
        break missingId;
      }

      id = R.id.edit_ai_answer;
      RichTextEditText editAiAnswer = ViewBindings.findChildViewById(rootView, id);
      if (editAiAnswer == null) {
        break missingId;
      }

      id = R.id.edit_answer;
      RichTextEditText editAnswer = ViewBindings.findChildViewById(rootView, id);
      if (editAnswer == null) {
        break missingId;
      }

      id = R.id.edit_question;
      RichTextEditText editQuestion = ViewBindings.findChildViewById(rootView, id);
      if (editQuestion == null) {
        break missingId;
      }

      id = R.id.edit_tags;
      TextInputEditText editTags = ViewBindings.findChildViewById(rootView, id);
      if (editTags == null) {
        break missingId;
      }

      id = R.id.layout_ai_answer;
      TextInputLayout layoutAiAnswer = ViewBindings.findChildViewById(rootView, id);
      if (layoutAiAnswer == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityCardEditBinding((CoordinatorLayout) rootView, buttonAddAnswerImage,
          buttonAddQuestionImage, buttonAiAnswer, buttonCancel, buttonOcrAnswer, buttonOcrQuestion,
          buttonRotateQuestionImage, buttonSave, editAiAnswer, editAnswer, editQuestion, editTags,
          layoutAiAnswer, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
