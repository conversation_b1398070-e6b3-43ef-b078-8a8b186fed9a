package com.erroranalysis.app.ui.camera

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.PointF
import android.graphics.RectF
import android.os.Bundle
import android.os.Environment
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.erroranalysis.app.databinding.ActivityPhotoEditBinding
import com.erroranalysis.app.domain.camera.model.DocumentProcessResult
import com.erroranalysis.app.utils.DocumentProcessor
import com.erroranalysis.app.utils.PerspectiveCorrector

import java.io.File
import java.io.FileOutputStream

/**
 * 照片編輯 Activity
 * 用於在拍照後調整裁剪區域
 */
class PhotoEditActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "PhotoEditActivity"
        const val EXTRA_IMAGE_PATH = "image_path"


    }

    private lateinit var binding: ActivityPhotoEditBinding
    private var imagePath: String? = null
    private var originalBitmap: Bitmap? = null
    private var isInCropMode = false
    private var isInPreviewMode = false
    private var correctedBitmap: Bitmap? = null
    private val perspectiveCorrector = PerspectiveCorrector()

    // 檢查是否為返回圖片模式
    private val isReturnImageMode by lazy {
        intent.getBooleanExtra("return_image", false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPhotoEditBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 獲取參數
        imagePath = intent.getStringExtra(EXTRA_IMAGE_PATH) ?: intent.getStringExtra("image_path")

        if (imagePath == null) {
            finish()
            return
        }

        setupUI()
        loadPhoto()
        // setupDefaultBoundary() 將在 loadPhoto() 成功後調用
    }

    private fun setupUI() {
        Log.d(TAG, "🔧 setupUI() 開始執行")

        // 設置標題
        binding.tvTitle.text = "調整文檔邊界"
        binding.tvInstruction.text = "拖動白色外框調整文檔邊界"

        Log.d(TAG, "📝 標題已設置為: ${binding.tvTitle.text}")
        Log.d(TAG, "📝 說明已設置為: ${binding.tvInstruction.text}")

        // 返回按鈕
        binding.btnBack.setOnClickListener {
            finish()
        }

        // 重置按鈕
        binding.btnReset.setOnClickListener {
            if (isInCropMode) {
                // 重置裁切選擇
                binding.cropSelectionOverlay.resetSelection()
            } else {
                // 重置梯形修正邊界
                setupDefaultBoundary()
            }
        }

        // 直接設置三個處理按鈕
        setupMainProcessingButtons()
    }

    /**
     * 設置主要的三個處理按鈕：根據當前模式設置不同功能
     */
    private fun setupMainProcessingButtons() {
        Log.d(TAG, "🔘 setupMainProcessingButtons() 開始執行，預覽模式: $isInPreviewMode")

        // 隱藏選擇按鈕，顯示處理按鈕
        binding.layoutSelectionButtons.visibility = android.view.View.GONE
        binding.layoutConfirmButtons.visibility = android.view.View.VISIBLE

        if (isInPreviewMode) {
            // 預覽模式：顯示預覽相關按鈕
            updateButtonsForPreviewMode()
        } else {
            // 正常模式：顯示原始處理按鈕
            setupNormalModeButtons()
        }
    }

    /**
     * 設置正常模式的按鈕
     */
    private fun setupNormalModeButtons() {
        // 設置按鈕功能（ImageButton不需要設置文字，文字在布局中的TextView顯示）
        binding.btnCrop.visibility = android.view.View.GONE  // 隱藏第三個按鈕

        Log.d(TAG, "🔘 正常模式按鈕設置完成:")
        Log.d(TAG, "  - btnCancel: 保存功能")
        Log.d(TAG, "  - btnConfirm: 裁切功能")

        // 保存按鈕：梯形修正並存檔
        binding.btnCancel.setOnClickListener {
            performSaveWithCorrectionDirectly()
        }

        // 裁切按鈕：進入裁切模式
        binding.btnConfirm.setOnClickListener {
            enterCropMode()
        }
    }

    /**
     * 設置默認邊界（基於ImageView的顯示區域）
     */
    private fun setupDefaultBoundary() {
        // 等待ImageView佈局完成後設置邊界
        binding.ivPhoto.post {
            val imageView = binding.ivPhoto
            val drawable = imageView.drawable

            if (drawable != null) {
                // 計算圖片在ImageView中的實際顯示區域
                val imageRect = getImageDisplayRect(imageView)

                // 設置邊界點為圖片顯示區域的四個角，並留一些邊距
                val margin = minOf(imageRect.width(), imageRect.height()) * 0.1f
                val defaultBoundaries = listOf(
                    PointF(imageRect.left + margin, imageRect.top + margin),           // 左上
                    PointF(imageRect.right - margin, imageRect.top + margin),          // 右上
                    PointF(imageRect.right - margin, imageRect.bottom - margin),       // 右下
                    PointF(imageRect.left + margin, imageRect.bottom - margin)         // 左下
                )

                // 顯示文檔邊界覆蓋層
                binding.documentBoundaryOverlay.visibility = android.view.View.VISIBLE
                binding.cropSelectionOverlay.visibility = android.view.View.GONE
                binding.documentBoundaryOverlay.setBoundaryPoints(defaultBoundaries)
                binding.documentBoundaryOverlay.setManualAdjustmentEnabled(true)

                isInCropMode = false

                Log.d(TAG, "設置默認邊界: $defaultBoundaries")
            }
        }
    }

    /**
     * 獲取圖片在ImageView中的實際顯示區域
     */
    private fun getImageDisplayRect(imageView: android.widget.ImageView): RectF {
        val drawable = imageView.drawable
        if (drawable == null) {
            return RectF(0f, 0f, imageView.width.toFloat(), imageView.height.toFloat())
        }

        val imageWidth = drawable.intrinsicWidth.toFloat()
        val imageHeight = drawable.intrinsicHeight.toFloat()
        val viewWidth = imageView.width.toFloat()
        val viewHeight = imageView.height.toFloat()

        // 根據ScaleType計算顯示區域
        return when (imageView.scaleType) {
            android.widget.ImageView.ScaleType.CENTER_CROP -> {
                val scaleX = viewWidth / imageWidth
                val scaleY = viewHeight / imageHeight
                val scale = maxOf(scaleX, scaleY)

                val scaledWidth = imageWidth * scale
                val scaledHeight = imageHeight * scale

                val left = (viewWidth - scaledWidth) / 2f
                val top = (viewHeight - scaledHeight) / 2f

                RectF(left, top, left + scaledWidth, top + scaledHeight)
            }
            android.widget.ImageView.ScaleType.FIT_CENTER,
            android.widget.ImageView.ScaleType.CENTER_INSIDE -> {
                val scaleX = viewWidth / imageWidth
                val scaleY = viewHeight / imageHeight
                val scale = minOf(scaleX, scaleY)

                val scaledWidth = imageWidth * scale
                val scaledHeight = imageHeight * scale

                val left = (viewWidth - scaledWidth) / 2f
                val top = (viewHeight - scaledHeight) / 2f

                RectF(left, top, left + scaledWidth, top + scaledHeight)
            }
            else -> {
                // 默認情況：假設圖片填滿整個ImageView
                RectF(0f, 0f, viewWidth, viewHeight)
            }
        }
    }

    /**
     * 進入預覽模式
     */
    private fun enterPreviewMode() {
        isInPreviewMode = true

        // 隱藏文檔邊界覆蓋層
        binding.documentBoundaryOverlay.visibility = android.view.View.GONE

        // 更新UI
        binding.tvTitle.text = "梯形修正預覽"
        binding.tvInstruction.text = "查看修正效果，可繼續進行裁切操作"

        // 更新按鈕為預覽模式
        updateButtonsForPreviewMode()
    }

    /**
     * 退出預覽模式
     */
    private fun exitPreviewMode() {
        isInPreviewMode = false
        correctedBitmap?.recycle()
        correctedBitmap = null

        // 恢復原始圖片
        binding.ivPhoto.setImageBitmap(originalBitmap)

        // 恢復文檔邊界覆蓋層
        binding.documentBoundaryOverlay.visibility = android.view.View.VISIBLE

        // 更新UI
        binding.tvTitle.text = "調整文檔邊界"
        binding.tvInstruction.text = "拖動白色外框調整文檔邊界"

        // 恢復主要處理按鈕
        setupMainProcessingButtons()
    }

    /**
     * 更新按鈕為預覽模式
     */
    private fun updateButtonsForPreviewMode() {
        // ImageButton不需要設置文字，功能保持不變
        binding.btnCrop.visibility = android.view.View.GONE  // 隱藏第三個按鈕

        binding.btnCancel.setOnClickListener {
            // 直接保存校正後的圖片
            correctedBitmap?.let { bitmap ->
                Log.d(TAG, "預覽模式保存校正後圖片，尺寸: ${bitmap.width}x${bitmap.height}")
                saveProcessedImage(bitmap, "梯形修正")
            } ?: run {
                Log.e(TAG, "預覽模式下校正後圖片為空，無法保存")
            }
        }

        // 長按重新調整
        binding.btnCancel.setOnLongClickListener {
            exitPreviewMode()
            true
        }

        binding.btnConfirm.setOnClickListener {
            // 進入裁切模式
            enterCropMode()
        }
    }



    /**
     * 進入裁切模式 - 先執行梯形修正再裁切
     */
    private fun enterCropMode() {
        Log.d(TAG, "進入裁切模式")
        Log.d(TAG, "當前預覽模式: $isInPreviewMode")
        Log.d(TAG, "校正後圖片存在: ${correctedBitmap != null}")
        Log.d(TAG, "原始圖片存在: ${originalBitmap != null}")

        // 如果還沒有執行梯形修正，先執行修正
        if (!isInPreviewMode) {
            val boundaries = binding.documentBoundaryOverlay.getBoundaryPoints()
            if (boundaries != null && boundaries.size == 4) {
                Log.d(TAG, "先執行梯形修正，然後進入裁切模式")
                // 執行梯形修正並顯示預覽，然後自動進入裁切模式
                performPerspectiveCorrectionAndEnterCrop(boundaries)
                return
            } else {
                Log.w(TAG, "無法獲取邊界點，直接使用原始圖片進行裁切")
            }
        }

        // 如果已經在預覽模式或無法獲取邊界，直接進入裁切模式
        enterCropModeDirectly()
    }

    /**
     * 執行梯形修正並自動進入裁切模式
     */
    private fun performPerspectiveCorrectionAndEnterCrop(boundaries: List<PointF>) {
        originalBitmap?.let { bitmap ->
            try {
                // 將ImageView座標轉換為Bitmap座標
                val bitmapBoundaries = convertViewPointsToBitmapPoints(boundaries, bitmap)
                Log.d(TAG, "View邊界點: $boundaries")
                Log.d(TAG, "Bitmap邊界點: $bitmapBoundaries")

                // 執行梯形修正
                correctedBitmap = performPerspectiveCorrection(bitmap, bitmapBoundaries)

                // 顯示校正後的圖片
                binding.ivPhoto.setImageBitmap(correctedBitmap)

                // 進入預覽模式
                isInPreviewMode = true

                // 隱藏文檔邊界覆蓋層
                binding.documentBoundaryOverlay.visibility = android.view.View.GONE

                Log.d(TAG, "梯形修正完成，自動進入裁切模式")

                // 直接進入裁切模式
                enterCropModeDirectly()

            } catch (e: Exception) {
                Log.e(TAG, "梯形修正失敗，使用原始圖片進行裁切", e)
                // 如果修正失敗，直接進入裁切模式
                enterCropModeDirectly()
            }
        }
    }

    /**
     * 直接進入裁切模式（不執行梯形修正）
     */
    private fun enterCropModeDirectly() {
        Log.d(TAG, "直接進入裁切模式")

        if (isInPreviewMode && correctedBitmap != null) {
            Log.d(TAG, "將基於梯形修正後的圖片進行裁切，尺寸: ${correctedBitmap!!.width}x${correctedBitmap!!.height}")
        } else if (originalBitmap != null) {
            Log.d(TAG, "將基於原始圖片進行裁切，尺寸: ${originalBitmap!!.width}x${originalBitmap!!.height}")
        }

        binding.tvTitle.text = "裁切調整"
        binding.tvInstruction.text = "拖動邊線和圓弧角點調整裁切區域"

        // 顯示裁切覆蓋層，隱藏文檔邊界覆蓋層
        binding.cropSelectionOverlay.visibility = android.view.View.VISIBLE
        binding.documentBoundaryOverlay.visibility = android.view.View.GONE

        // 重置裁切選擇框
        binding.cropSelectionOverlay.resetSelection()

        isInCropMode = true

        // 更新按鈕為確認/取消
        updateButtonsForCropMode()
    }

    /**
     * 更新按鈕為裁切模式
     */
    private fun updateButtonsForCropMode() {
        // ImageButton不需要設置文字，但需要更改功能
        binding.btnCrop.visibility = android.view.View.GONE

        binding.btnCancel.setOnClickListener {
            // 保存按鈕：執行裁切並存檔
            performCropAndSave()
        }

        binding.btnConfirm.setOnClickListener {
            // 去筆跡按鈕：取消裁切，回到主模式
            exitCropMode()
        }
    }

    /**
     * 退出裁切模式
     */
    private fun exitCropMode() {
        if (isInPreviewMode) {
            binding.tvTitle.text = "梯形修正預覽"
            binding.tvInstruction.text = "查看修正效果，可繼續進行裁切操作"
        } else {
            binding.tvTitle.text = "調整文檔邊界"
            binding.tvInstruction.text = "拖動白色外框調整文檔邊界"
        }

        // 恢復文檔邊界覆蓋層（如果不在預覽模式）
        if (!isInPreviewMode) {
            binding.documentBoundaryOverlay.visibility = android.view.View.VISIBLE
        } else {
            binding.documentBoundaryOverlay.visibility = android.view.View.GONE
        }
        binding.cropSelectionOverlay.visibility = android.view.View.GONE

        isInCropMode = false

        // 恢復主要處理按鈕
        setupMainProcessingButtons()
    }

    /**
     * 保存：執行梯形修正、存檔並顯示預覽
     */
    private fun performSaveWithCorrectionDirectly() {
        val boundaries = binding.documentBoundaryOverlay.getBoundaryPoints()
        if (boundaries != null && boundaries.size == 4) {
            // 執行梯形修正、存檔並顯示預覽
            performPerspectiveCorrectionSaveAndPreview(boundaries)
        }
    }

    /**
     * 預覽：執行梯形修正並顯示預覽
     */
    private fun performSaveWithCorrection() {
        val boundaries = binding.documentBoundaryOverlay.getBoundaryPoints()
        if (boundaries != null && boundaries.size == 4) {
            // 執行梯形修正並顯示預覽
            performPerspectiveCorrectionAndPreview(boundaries)
        }
    }



    /**
     * 裁切並存檔
     */
    private fun performCropAndSave() {
        val cropRect = binding.cropSelectionOverlay.getSelectionRect()
        Log.d(TAG, "獲取裁切區域: $cropRect")
        Log.d(TAG, "預覽模式: $isInPreviewMode")
        Log.d(TAG, "校正後圖片存在: ${correctedBitmap != null}")
        Log.d(TAG, "原始圖片存在: ${originalBitmap != null}")

        if (cropRect != null && !cropRect.isEmpty) {
            Log.d(TAG, "執行裁切，裁切區域: $cropRect")
            // 執行裁切並存檔
            performCropAndSaveImage(cropRect)
        } else {
            Log.w(TAG, "裁切區域無效或為空")
        }
    }

    /**
     * 執行梯形修正、存檔並顯示預覽
     */
    private fun performPerspectiveCorrectionSaveAndPreview(boundaries: List<PointF>) {
        originalBitmap?.let { bitmap ->
            try {
                // 將ImageView座標轉換為Bitmap座標
                val bitmapBoundaries = convertViewPointsToBitmapPoints(boundaries, bitmap)
                Log.d(TAG, "View邊界點: $boundaries")
                Log.d(TAG, "Bitmap邊界點: $bitmapBoundaries")

                // 執行梯形修正
                correctedBitmap = performPerspectiveCorrection(bitmap, bitmapBoundaries)

                // 先保存圖片（不結束Activity）
                saveProcessedImage(correctedBitmap!!, "梯形修正", finishActivity = false)

                // 然後顯示校正後的圖片並進入預覽模式
                binding.ivPhoto.setImageBitmap(correctedBitmap)
                enterPreviewMode()

            } catch (e: Exception) {
                Log.e(TAG, "梯形修正失敗", e)
            }
        }
    }

    /**
     * 執行梯形修正並顯示預覽（不存檔）
     */
    private fun performPerspectiveCorrectionAndPreview(boundaries: List<PointF>) {
        originalBitmap?.let { bitmap ->
            try {
                // 將ImageView座標轉換為Bitmap座標
                val bitmapBoundaries = convertViewPointsToBitmapPoints(boundaries, bitmap)
                Log.d(TAG, "View邊界點: $boundaries")
                Log.d(TAG, "Bitmap邊界點: $bitmapBoundaries")

                // 執行梯形修正
                correctedBitmap = performPerspectiveCorrection(bitmap, bitmapBoundaries)

                // 顯示校正後的圖片
                binding.ivPhoto.setImageBitmap(correctedBitmap)

                // 進入預覽模式
                enterPreviewMode()

            } catch (e: Exception) {
                Log.e(TAG, "梯形修正失敗", e)
            }
        }
    }

    /**
     * 執行梯形修正並存檔
     */
    private fun performPerspectiveCorrectionAndSave(boundaries: List<PointF>) {
        originalBitmap?.let { bitmap ->
            try {
                // 將ImageView座標轉換為Bitmap座標
                val bitmapBoundaries = convertViewPointsToBitmapPoints(boundaries, bitmap)
                Log.d(TAG, "View邊界點: $boundaries")
                Log.d(TAG, "Bitmap邊界點: $bitmapBoundaries")

                // 執行梯形修正
                val correctedBitmap = performPerspectiveCorrection(bitmap, bitmapBoundaries)

                // 保存圖片
                saveProcessedImage(correctedBitmap, "梯形修正")

            } catch (e: Exception) {
                Log.e(TAG, "處理圖片失敗", e)
            }
        }
    }

    /**
     * 執行裁切並存檔
     */
    private fun performCropAndSaveImage(cropRect: RectF) {
        // 選擇要裁切的圖片：如果在預覽模式使用校正後的圖片，否則使用原始圖片
        val sourceBitmap = if (isInPreviewMode && correctedBitmap != null) {
            Log.d(TAG, "使用梯形修正後的圖片進行裁切")
            correctedBitmap!!
        } else {
            Log.d(TAG, "使用原始圖片進行裁切")
            originalBitmap
        }

        sourceBitmap?.let { bitmap ->
            try {
                Log.d(TAG, "開始裁切處理，使用圖片尺寸: ${bitmap.width}x${bitmap.height}")

                // 將ImageView座標轉換為Bitmap座標
                val bitmapCropRect = convertViewRectToBitmapRect(cropRect, bitmap)
                Log.d(TAG, "預覽模式: $isInPreviewMode")
                Log.d(TAG, "View裁切區域: $cropRect")
                Log.d(TAG, "Bitmap裁切區域: $bitmapCropRect")

                // 驗證裁切區域
                if (bitmapCropRect.left < 0 || bitmapCropRect.top < 0 ||
                    bitmapCropRect.right > bitmap.width || bitmapCropRect.bottom > bitmap.height ||
                    bitmapCropRect.width() <= 0 || bitmapCropRect.height() <= 0) {
                    Log.w(TAG, "裁切區域超出圖片範圍或無效")
                    return
                }

                Log.d(TAG, "開始執行裁切...")
                // 執行裁切
                val croppedBitmap = cropBitmap(bitmap, bitmapCropRect)
                Log.d(TAG, "裁切完成，結果尺寸: ${croppedBitmap.width}x${croppedBitmap.height}")

                // 保存圖片
                Log.d(TAG, "開始保存裁切後的圖片...")
                saveProcessedImage(croppedBitmap, "裁切")

            } catch (e: Exception) {
                Log.e(TAG, "裁切圖片失敗", e)
            }
        } ?: run {
            Log.e(TAG, "無法獲取源圖片進行裁切")
        }
    }

    /**
     * 執行梯形修正
     */
    private fun performPerspectiveCorrection(bitmap: Bitmap, boundaries: List<PointF>): Bitmap {
        Log.d(TAG, "執行梯形修正，邊界點: ${boundaries.size}")

        return try {
            // 使用 PerspectiveCorrector 進行透視校正
            val correctedBitmap = perspectiveCorrector.correctPerspective(bitmap, boundaries)

            if (correctedBitmap != null) {
                Log.d(TAG, "✅ 梯形修正成功")
                correctedBitmap
            } else {
                Log.w(TAG, "⚠️ 梯形修正失敗，返回原圖")
                bitmap
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 梯形修正發生錯誤", e)
            bitmap
        }
    }



    /**
     * 將ImageView座標點轉換為Bitmap座標點
     */
    private fun convertViewPointsToBitmapPoints(viewPoints: List<PointF>, bitmap: Bitmap): List<PointF> {
        val imageView = binding.ivPhoto
        if (imageView.drawable == null) return viewPoints

        // 獲取圖片在ImageView中的實際顯示區域
        val imageRect = getImageDisplayRect(imageView)

        return viewPoints.map { viewPoint ->
            // 計算點相對於顯示圖片的比例
            val relativeX = (viewPoint.x - imageRect.left) / imageRect.width()
            val relativeY = (viewPoint.y - imageRect.top) / imageRect.height()

            // 轉換為Bitmap座標
            PointF(
                relativeX * bitmap.width,
                relativeY * bitmap.height
            )
        }
    }



    /**
     * 將ImageView座標轉換為Bitmap座標
     */
    private fun convertViewRectToBitmapRect(viewRect: RectF, bitmap: Bitmap): RectF {
        val imageView = binding.ivPhoto
        if (imageView.drawable == null) return viewRect

        // 獲取圖片在ImageView中的實際顯示區域
        val imageRect = getImageDisplayRect(imageView)

        // 計算裁切區域相對於顯示圖片的比例
        val relativeLeft = (viewRect.left - imageRect.left) / imageRect.width()
        val relativeTop = (viewRect.top - imageRect.top) / imageRect.height()
        val relativeRight = (viewRect.right - imageRect.left) / imageRect.width()
        val relativeBottom = (viewRect.bottom - imageRect.top) / imageRect.height()

        // 轉換為Bitmap座標
        return RectF(
            relativeLeft * bitmap.width,
            relativeTop * bitmap.height,
            relativeRight * bitmap.width,
            relativeBottom * bitmap.height
        )
    }

    /**
     * 裁切圖片
     */
    private fun cropBitmap(bitmap: Bitmap, cropRect: RectF): Bitmap {
        val left = maxOf(0, cropRect.left.toInt())
        val top = maxOf(0, cropRect.top.toInt())
        val right = minOf(bitmap.width, cropRect.right.toInt())
        val bottom = minOf(bitmap.height, cropRect.bottom.toInt())
        val width = right - left
        val height = bottom - top

        if (width <= 0 || height <= 0) {
            throw IllegalArgumentException("裁切區域無效: width=$width, height=$height")
        }

        return Bitmap.createBitmap(bitmap, left, top, width, height)
    }

    /**
     * 保存處理後的圖片到公共相簿目錄
     */
    private fun saveProcessedImage(bitmap: Bitmap, actionName: String, finishActivity: Boolean = true) {
        try {
            // 如果是返回圖片模式，保存到臨時文件並返回路徑
            if (isReturnImageMode) {
                val tempFile = saveBitmapToTempFile(bitmap, actionName)
                if (tempFile != null) {
                    val resultIntent = android.content.Intent()
                    resultIntent.putExtra("image_path", tempFile.absolutePath)
                    setResult(RESULT_OK, resultIntent)
                    finish()
                    return
                }
            }

            // 正常模式：保存到相簿
            // 生成新的文件名
            val timestamp = System.currentTimeMillis()
            val fileName = "ErrorAnalysis_${actionName}_$timestamp.jpg"

            // 使用 MediaStore 保存到公共相簿目錄
            val contentValues = android.content.ContentValues().apply {
                put(android.provider.MediaStore.Images.Media.DISPLAY_NAME, fileName)
                put(android.provider.MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
                put(android.provider.MediaStore.Images.Media.RELATIVE_PATH, "Pictures/ErrorAnalysis")
            }

            val uri = contentResolver.insert(android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)

            if (uri != null) {
                contentResolver.openOutputStream(uri)?.use { out ->
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
                }

                Log.d(TAG, "${actionName}完成，保存至相簿: Pictures/ErrorAnalysis/$fileName")

                // 根據參數決定是否返回上一頁
                if (finishActivity) {
                    finish()
                }
            } else {
                throw Exception("無法創建圖片文件")
            }

        } catch (e: Exception) {
            Log.e(TAG, "保存圖片失敗", e)

            // 如果 MediaStore 失敗，嘗試保存到應用目錄作為備用
            saveToPicturesDirectory(bitmap, actionName, finishActivity)
        }
    }

    /**
     * 保存圖片到臨時文件（用於返回圖片模式）
     */
    private fun saveBitmapToTempFile(bitmap: Bitmap, actionName: String): java.io.File? {
        return try {
            val timestamp = System.currentTimeMillis()
            val fileName = "temp_${actionName}_$timestamp.jpg"
            val tempFile = java.io.File(cacheDir, fileName)

            java.io.FileOutputStream(tempFile).use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
            }

            Log.d(TAG, "圖片已保存到臨時文件: ${tempFile.absolutePath}")
            tempFile
        } catch (e: Exception) {
            Log.e(TAG, "保存臨時文件失敗", e)
            null
        }
    }

    /**
     * 備用保存方法：保存到 Pictures 目錄
     */
    private fun saveToPicturesDirectory(bitmap: Bitmap, actionName: String, finishActivity: Boolean = true) {
        try {
            val timestamp = System.currentTimeMillis()
            val fileName = "ErrorAnalysis_${actionName}_$timestamp.jpg"
            val picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
            val errorAnalysisDir = File(picturesDir, "ErrorAnalysis")

            // 創建目錄（如果不存在）
            if (!errorAnalysisDir.exists()) {
                errorAnalysisDir.mkdirs()
            }

            val file = File(errorAnalysisDir, fileName)

            FileOutputStream(file).use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
            }

            // 通知媒體掃描器更新相簿
            android.media.MediaScannerConnection.scanFile(
                this,
                arrayOf(file.absolutePath),
                arrayOf("image/jpeg"),
                null
            )

            Log.d(TAG, "${actionName}完成，保存至: ${file.absolutePath}")

            // 根據參數決定是否返回上一頁
            if (finishActivity) {
                finish()
            }

        } catch (e: Exception) {
            Log.e(TAG, "備用保存方法也失敗", e)
        }
    }



    private fun loadPhoto() {
        imagePath?.let { path ->
            val file = File(path)
            if (file.exists()) {
                originalBitmap = BitmapFactory.decodeFile(path)
                binding.ivPhoto.setImageBitmap(originalBitmap)
                Log.d(TAG, "照片載入成功: $path")

                // 照片載入成功後設置默認邊界
                setupDefaultBoundary()
            } else {
                Log.e(TAG, "照片文件不存在: $path")
                finish()
            }
        }
    }











    override fun onDestroy() {
        super.onDestroy()
        originalBitmap?.recycle()
        correctedBitmap?.recycle()
    }
}
